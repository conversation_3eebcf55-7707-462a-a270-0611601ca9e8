{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "core_app",
            "request": "launch",
            "type": "dart",
            "args": [
                "--dart-define=BASE_URL=http://localhost:3000"
            ]
        },
        {
            "name": "core_app network",
            "request": "launch",
            "type": "dart",
            "args": [
                "--dart-define=BASE_URL=https://g5hcmk52-3000.uks1.devtunnels.ms",
            ]
        },
        {
            "name": "core_app prod",
            "request": "launch",
            "type": "dart",
            "args": []
        },
        {
            "name": "core_app (profile mode)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "core_app (release mode)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
    ]
}