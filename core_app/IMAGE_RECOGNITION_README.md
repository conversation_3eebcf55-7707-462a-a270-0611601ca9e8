# Image Recognition Feature Integration

This document describes the integration of the MolarMind image recognition feature into the chat dialog/screen.

## Overview

The image recognition feature allows users to upload dental images and receive AI-powered analysis of dental conditions. The system displays predictions with color-coded indicators and provides a comprehensive legend explaining what each color represents.

## Features

### 1. Image Upload
- Users can tap the gallery icon in the chat input area to select an image
- Supports common image formats (JPEG, PNG, etc.)
- Images are automatically resized and optimized before upload

### 2. AI Analysis
- Images are sent to the MolarMind API for analysis
- The system detects various dental conditions with confidence scores
- Only predictions with confidence > 70% are displayed

### 3. Color-Coded Results
Each detected condition is assigned a specific color:
- **Red**: Cavity/Caries/Decay
- **Blue**: Filling
- **Green**: Crown
- **Purple**: Implant
- **Grey**: Missing Tooth
- **Light Green**: Healthy
- **Yellow**: Plaque
- **Orange**: Tartar
- **Pink**: Gingivitis
- **Brown**: Periodontitis
- **Dark Red**: Abscess
- **Dark Blue**: Crack/Fracture
- **Teal**: Root Canal
- **Black**: Extraction
- **Cyan**: Whitening
- **Indigo**: Orthodontic
- **Amber**: Prosthetic

### 4. Results Display
- **Visual Image Analysis**: Displays the uploaded image with prediction boxes overlaid
- **Network Image Support**: Loads images from S3 URLs returned by backend
- **Fallback Handling**: Gracefully handles both network URLs and local file paths
- **Loading States**: Shows progress indicators while loading network images
- **Error Recovery**: Displays user-friendly error messages for failed image loads
- **Interactive Overlays**: Uses Stack widget to show colored bounding boxes at exact x,y coordinates
- **Condition Labels**: Each prediction box shows the detected condition name
- **Confidence Scores**: Shows a list of detected conditions with confidence percentages
- **Color Legend**: Displays a comprehensive legend explaining what each color represents
- **Responsive Design**: Adapts to both full screen and compact dialog layouts

## Technical Implementation

### Backend Integration
- Uses the existing `/ai/image_recognition/` endpoint
- Supports multipart form data upload
- Returns predictions in JSON format

### Frontend Components

#### Models (`lib/core/models/molar_mind.dart`)
- `MolarMindResponse`: Main response container
- `MolarMindImage`: Image metadata
- `MolarMindPrediction`: Individual prediction data
- `DentalClinicMolarChatImageResponse`: Database model

#### Color Management (`lib/core/constants/prediction_colors.dart`)
- `PredictionColors`: Static class for color mapping
- Maps dental conditions to specific colors
- Provides utility methods for color legend generation

#### Chat Controller (`lib/layout/chat/controller.dart`)
- `pickAndAnalyzeImage()`: Handles image selection and analysis
- `uploadImageForAnalysis()`: Uploads image to backend and receives S3 URL
- `loadImageRecognitionHistory()`: Loads previous analyses
- Handles backend response with `imageUrl` field and predictions

#### Chat Screen (`lib/layout/chat/screen.dart`)
- Added gallery icon for image upload
- `_buildImageRecognitionMessage()`: Renders analysis results with image display
- `_buildImageWidget()`: Handles network and local image loading with fallbacks
- Uses Stack widget to overlay prediction boxes on the uploaded image
- Displays predictions with color indicators and legend
- 200px height image container with scaled coordinate mapping

#### Chat Dialog (`lib/layout/chat/dialog.dart`)
- Added gallery icon for image upload in floating dialog
- `_buildImageRecognitionMessage()`: Renders compact analysis results with image display
- `_buildImageWidget()`: Handles network and local image loading with fallbacks
- Uses Stack widget to overlay prediction boxes on the uploaded image
- Optimized for smaller dialog space with condensed layout
- 120px height image container with scaled coordinate mapping

## Usage

### Chat Screen
1. Open the full chat screen
2. Tap the gallery icon (📷) in the input area
3. Select a dental image from your device
4. Wait for the AI analysis to complete
5. View the results with color-coded conditions and legend

### Chat Dialog (Floating)
1. Open the floating chat dialog
2. Tap the gallery icon (📷) in the input area
3. Select a dental image from your device
4. Wait for the AI analysis to complete
5. View the compact results with color-coded conditions and condensed legend

## Dependencies

- `image_picker: ^1.0.7`: For image selection from gallery
- `dio: ^5.8.0+1`: For HTTP requests (already included)

## API Endpoints

- `POST /ai/image_recognition/`: Upload and analyze image
- `GET /ai/image_recognition/`: Get analysis history

## Error Handling

- Network errors are displayed as user-friendly messages
- Invalid images are rejected with appropriate feedback
- Loading states are shown during analysis

## Future Enhancements

- Camera capture support
- Multiple image upload
- Export analysis results
- Integration with patient records
- Real-time analysis during video calls 