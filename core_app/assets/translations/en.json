{"dashboard": "Dashboard", "data": "Data", "adminstarion": "Adminstarion", "home": "Home", "analysis": "Analysis", "patients": "Patients", "settings": "Settings", "logout": "Logout", "login": "<PERSON><PERSON>", "expenses": "Expenses", "inventory": "Inventory", "payments": "Payments", "specialists": "Specialists", "users": "Users", "branches": "Branches", "labs": "Labs", "branch": "Branch", "visitsDiagram": "Visits Diagram", "newPatients": "New Patients", "recurringPatients": "Recurring Patients", "other": "Other", "patientsList": "Patients List", "search": "Search", "name": "Name", "phoneNumber": "Phone Number", "birthdate": "Birthdate", "addPatient": "Add Patient", "address": "Address", "job": "Job", "fileNumber": "File Number", "dentalHistory": "Dental History", "medicalHistory": "Medical History", "patientProfile": "Patient Profile", "about": "About", "age": "Age", "balance": "Balance", "appointments": "Appointments", "from": "From", "to": "To", "roomNumber": "Room Number", "startTime": "Start Time", "duration": "Duration", "treatmentPlan": "Treatment Plan", "visitsHistory": "Visits History", "diagnosis": "Diagnosis", "comments": "Comments", "treatments": "Treatments", "procedures": "Procedures", "teethNumber": "Teeth Number", "finalPrice": "Final Price", "expand": "Expand", "noProceduresCreated": "No Procedures Created", "noFilesUploaded": "No Files Uploaded", "expensesList": "Expenses List", "amount": "Amount", "type": "Type", "description": "Description", "newExpense": "New Expense", "salary": "Salary", "materials": "Materials", "fees": "Fees", "inventoryList": "Inventory List", "currentQuantity": "Current Quantity", "addItem": "Add Item", "unit": "Unit", "quantity": "Quantity", "warningQuantity": "Warning Quantity", "paymnetsList": "Payments List", "createdby": "Action By", "createdAt": "Created At", "addSpeciality": "Add Speciality", "speciality": "Speciality", "usersList": "Users List", "adduser": "Add User", "role": "Role", "isDentist": "Is Dentist", "username": "Username", "password": "Password", "admin": "Admin", "master": "Master", "secretary": "Secretary", "external": "External", "add": "Add", "brancheslist": "Branches List", "addBranch": "Add Branch", "roomsCount": "Rooms Count", "mapView": "Map View", "googleLink": "Google Link", "removeLocation": "Remove Location", "newLab": "New Lab", "requests": "Requests", "patient": "Patient", "sendDate": "Send Date", "expectedDeliveryDate": "Expected Delivery Date", "actualDeliveryDate": "Actual Delivery Date", "newRequest": "New Request", "price": "Price", "sendingBranch": "Sending Branch", "receivingBranch": "Receiving Branch", "notes": "Notes", "nextStep": "Next Step", "generalInfo": "General Info", "details": "Details", "save": "Save", "shading": "Shading", "ponticDesign": "Pontic Design", "implantWork": "Implant Work", "zro2": "ZrO2", "3dPrinting": "3D Printing", "emax": "E-Max", "customAbutment": "Custom Abutment", "fullArchImplant": "Full Arch Implant"}