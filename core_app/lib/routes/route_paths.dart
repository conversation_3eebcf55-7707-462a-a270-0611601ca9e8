// ignore_for_file: constant_identifier_names

class Routes {
  static const Login = '/login';
  static const ForgotPassword = '/forgot-password';
  static const Home = '/home';
  static const Analysis = '/analysis';
  static const Communication = '/communication';
  static const Branches = '/branches';
  static const PatientsList = '/patients-list';
  static const Users = '/users';
  static const Expenses = '/expenses';
  static const Inventories = '/inventories';
  static const InventoryTransactions = '/inventory-transactions';
  static const Insurance = '/insurance';
  static const Payments = '/payments';
  static const SpecialityTemplates = '/speciality-templates';
  static const SpecialityProcedures = '/speciality-procedures';
  static const Labs = '/labs';
  static const PatientProfile = '/patient-profile';
  static const Settings = '/settings';
  static const Search = '/search';
  static const OverduePatients = '/overdue-patients';
  static const PatientGroups = '/patient-groups';
  static const PatientGroupMembers = '/patient-groups-members';
  static const Timesheet = '/timesheet';

  static path(String routeName) => routeName.substring(1);
}
