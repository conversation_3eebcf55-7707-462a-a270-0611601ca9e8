// ignore_for_file: constant_identifier_names, prefer_const_constructors

import 'package:qlevar_router/qlevar_router.dart';

import 'package:core_app/layout/app_layout.dart' deferred as layout;
import 'package:core_app/layout/controller.dart';

import 'route_names.dart';
import 'route_paths.dart';
import 'middlewares.dart';

import 'feature_routes/auth_routes.dart';
import 'feature_routes/home_routes.dart';
import 'feature_routes/communication_routes.dart';
import 'feature_routes/branches_routes.dart';
import 'feature_routes/users_routes.dart';
import 'feature_routes/patients_routes.dart';
import 'feature_routes/expenses_routes.dart';
import 'feature_routes/inventories_routes.dart';
import 'feature_routes/insurance_routes.dart';
import 'feature_routes/payments_routes.dart';
import 'feature_routes/specialities_routes.dart';
import 'feature_routes/labs_routes.dart';

final List<QRoute> routes = [
  ...authRoutes,
  QRoute.withChild(
    name: RouteNames.Dashboard,
    path: '/',
    initRoute: Routes.path(Routes.Home),
    builderChild: (r) => layout.AppLayout(r),
    middleware: [
      AuthMiddleware(),
      DefferedLoader(layout.loadLibrary),
      StateRemoverMiddleware<LayoutController>(),
    ],
    children: [
      ...homeRoutes,
      ...communicationRoutes,
      ...branchesRoutes,
      ...usersRoutes,
      ...patientsRoutes,
      ...expensesRoutes,
      ...inventoriesRoutes,
      ...insuranceRoutes,
      ...paymentsRoutes,
      ...specialitiesRoutes,
      ...labsRoutes,
    ],
  ),
];
