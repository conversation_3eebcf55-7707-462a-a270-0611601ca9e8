import 'package:core_app/services/auth.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:qlevar_router/qlevar_router.dart';

class AuthMiddleware extends QMiddleware {
  @override
  Future<bool> canPop() async => true;

  @override
  Future<String?> redirectGuard(String path) async {
    if (kIsWeb) {
      final val = QR.params['token'];
      final String token = (val?.value as String?) ?? '';
      if (token.isNotEmpty) {
        await AuthService.to.login(token);
        if (AuthService.loggedIn) return '/';
      }
    }

    if (path == '/login' || AuthService.loggedIn) {
      return null;
    }
    return '/login';
  }
}

class DefferedLoader extends QMiddleware {
  final Future<dynamic> Function() loader;
  const DefferedLoader(this.loader);

  @override
  Future onEnter() async {
    await loader.call();
    if (kDebugMode) print('DefferedLoader: Loaded ${loader.runtimeType}');
  }
}

class StateRemoverMiddleware<T> extends QMiddleware {
  @override
  Future onExit() {
    Get.delete<T>();
    if (kDebugMode) print('StateRemover: Removed $T');
    return super.onExit();
  }
}

class LoadStateMiddleware<T> extends QMiddleware {
  final T Function() loader;
  const LoadStateMiddleware(this.loader);

  @override
  Future onEnter() async {
    try {
      Get.put(loader.call());
      if (kDebugMode) print('LoadStateMiddleware: Loaded $T');
    } catch (e) {
      //
    }
  }

  @override
  Future onExit() {
    try {
      Get.delete<T>();
      if (kDebugMode) print('StateRemover: Removed $T');
    } catch (e) {
      //
    }
    return super.onExit();
  }
}

class LoadTagedStateMiddleware<T> extends QMiddleware {
  final T Function() loader;
  final String Function() tag;
  const LoadTagedStateMiddleware(this.loader, this.tag);

  @override
  Future onEnter() async {
    try {
      final tagID = tag.call();
      Get.put(loader.call(), tag: tagID);
      if (kDebugMode) print('LoadStateMiddleware: Loaded $T with tag $tagID');
    } catch (e) {
      //
    }
  }

  @override
  Future onExit() {
    try {
      Get.delete<T>(tag: tag.call());
      if (kDebugMode) print('StateRemover: Removed $T');
    } catch (e) {
      //
    }
    return super.onExit();
  }
}
