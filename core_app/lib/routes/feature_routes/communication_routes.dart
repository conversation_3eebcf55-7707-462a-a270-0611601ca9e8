import 'package:qlevar_router/qlevar_router.dart';

import 'package:core_app/pages/communication/communication.dart'
    deferred as communication;
import 'package:core_app/pages/communication/controller.dart'
    deferred as communication_controller;

import '../route_names.dart';
import '../route_paths.dart';
import '../middlewares.dart';

final List<QRoute> communicationRoutes = [
  QRoute(
    name: RouteNames.Communication,
    path: Routes.path(Routes.Communication),
    builder: () => communication.CommunicationPage(),
    middleware: [
      Deffered<PERSON>oader(communication.loadLibrary),
      Deffered<PERSON>oader(communication_controller.loadLibrary),
      LoadStateMiddleware(
          () => communication_controller.CommunicationController()),
    ],
  ),
];
