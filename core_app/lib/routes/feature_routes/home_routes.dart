import 'package:qlevar_router/qlevar_router.dart';

import 'package:core_app/pages/home/<USER>' deferred as home;
import 'package:core_app/pages/home/<USER>'
    deferred as home_controller;
import 'package:core_app/pages/analysis/analysis.dart' deferred as analysis;
import 'package:core_app/pages/analysis/controller.dart'
    deferred as analysis_controller;
import 'package:core_app/pages/test_feature_promo.dart';

import '../route_names.dart';
import '../route_paths.dart';
import '../middlewares.dart';

final List<QRoute> homeRoutes = [
  QRoute(
    name: RouteNames.Home,
    path: Routes.path(Routes.Home),
    builder: () => home.HomePage(),
    middleware: [
      Deffered<PERSON>oader(home.loadLibrary),
      Deffered<PERSON>oader(home_controller.loadLibrary),
      LoadStateMiddleware(() => home_controller.HomeController()),
    ],
  ),
  QRoute(
    name: RouteNames.Analysis,
    path: Routes.path(Routes.Analysis),
    builder: () => analysis.AnalysisPage(),
    middleware: [
      Deffered<PERSON>oader(analysis.loadLibrary),
      Deffered<PERSON>oa<PERSON>(analysis_controller.loadLibrary),
      LoadStateMiddleware(() => analysis_controller.AnalysisController()),
    ],
  ),
  QRoute(
    name: RouteNames.TestFeaturePromo,
    path: '/test-feature-promo',
    builder: () => const TestFeaturePromoPage(),
  ),
];
