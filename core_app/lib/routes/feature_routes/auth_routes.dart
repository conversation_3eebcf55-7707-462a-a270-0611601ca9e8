import 'package:qlevar_router/qlevar_router.dart';

import 'package:core_app/pages/auth/login/login.dart' deferred as login;
import 'package:core_app/pages/auth/login/controller.dart'
    deferred as login_controller;
import 'package:core_app/pages/auth/forgot_password/forgot_password.dart'
    deferred as forgot_password;
import 'package:core_app/pages/auth/forgot_password/controller.dart'
    deferred as forgot_password_controller;
import 'package:core_app/pages/auth/settings/settings.dart'
    deferred as settings;
import 'package:core_app/pages/auth/settings/controller.dart'
    deferred as settings_controller;

import '../route_names.dart';
import '../route_paths.dart';
import '../middlewares.dart';

final List<QRoute> authRoutes = [
  QRoute(
    name: RouteNames.Login,
    path: Routes.path(Routes.Login),
    builder: () => login.LoginPage(),
    middleware: [
      DefferedLoader(login.loadLibrary),
      DefferedLoader(login_controller.loadLibrary),
      LoadStateMiddleware(() => login_controller.LoginController()),
    ],
  ),
  QRoute(
    name: RouteNames.ForgotPassword,
    path: Routes.path(Routes.ForgotPassword),
    builder: () => forgot_password.ForgotPasswordPage(),
    middleware: [
      DefferedLoader(forgot_password.loadLibrary),
      DefferedLoader(forgot_password_controller.loadLibrary),
      LoadStateMiddleware(
          () => forgot_password_controller.ForgotPasswordController()),
    ],
  ),
  QRoute(
    name: RouteNames.Settings,
    path: Routes.path(Routes.Settings),
    builder: () => settings.SettingsPage(),
    middleware: [
      DefferedLoader(settings.loadLibrary),
      DefferedLoader(settings_controller.loadLibrary),
      LoadStateMiddleware(() => settings_controller.SettingsController()),
    ],
  ),
];
