import 'package:qlevar_router/qlevar_router.dart';

import 'package:core_app/pages/users/list/list.dart' deferred as users_list;
import 'package:core_app/pages/users/list/controller.dart'
    deferred as users_controller;
import 'package:core_app/pages/timesheet/list/list.dart';

import '../route_names.dart';
import '../route_paths.dart';
import '../middlewares.dart';

final List<QRoute> usersRoutes = [
  QRoute(
    name: RouteNames.Users,
    path: Routes.path(Routes.Users),
    builder: () => users_list.UsersListPage(),
    middleware: [
      Deffered<PERSON>oader(users_list.loadLibrary),
      Deffered<PERSON>oader(users_controller.loadLibrary),
      LoadStateMiddleware(() => users_controller.UsersListController()),
    ],
  ),
  QRoute(
    path: Routes.path(Routes.Timesheet),
    name: 'RouteNames.timesheet',
    builder: () {
      return const TimesheetPage();
    },
    middleware: [
      // LoadStateMiddleware(() {
      //   final query = QR.params['id']!.value as String;
      //   return GroupPateintsController(query);
      // }),
    ],
  ),
];
