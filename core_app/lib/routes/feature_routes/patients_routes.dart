import 'package:qlevar_router/qlevar_router.dart';

import 'package:core_app/pages/patients/list/list.dart'
    deferred as patients_list;
import 'package:core_app/pages/patients/list/controller.dart'
    deferred as patients_controller;
import 'package:core_app/pages/patients/profile/profile.dart'
    deferred as patient_profile;
import 'package:core_app/pages/patients/profile/controller.dart'
    deferred as patient_profile_controller;
import 'package:core_app/pages/patients/search/search.dart' deferred as search;
import 'package:core_app/pages/patients/overdue/overdue.dart'
    deferred as overdue;
import 'package:core_app/pages/patients/overdue/controller.dart'
    deferred as overdue_controller;
import 'package:core_app/pages/patients/groups/groups.dart' deferred as groups;
import 'package:core_app/pages/patients/groups/controller.dart'
    deferred as groups_controller;
import 'package:core_app/pages/patients/groups/list/list.dart';
import 'package:core_app/pages/patients/groups/list/controller.dart';
import 'package:core_app/pages/errors/url_not_valid.dart';

import '../route_names.dart';
import '../route_paths.dart';
import '../middlewares.dart';

final List<QRoute> patientsRoutes = [
  QRoute(
    name: RouteNames.PatientsList,
    path: Routes.path(Routes.PatientsList),
    builder: () => patients_list.PatientsListPage(),
    middleware: [
      DefferedLoader(patients_list.loadLibrary),
      DefferedLoader(patients_controller.loadLibrary),
      LoadStateMiddleware(() => patients_controller.PateintsListController()),
    ],
  ),
  QRoute(
    name: RouteNames.PatientProfile,
    path: Routes.path(Routes.PatientProfile),
    builder: () {
      try {
        final patientID = QR.params['patientID']!.value as String;
        return patient_profile.PateintProfilePage(patientID);
      } catch (e) {
        return const URLNotValidPage();
      }
    },
    middleware: [
      DefferedLoader(patient_profile.loadLibrary),
      DefferedLoader(patient_profile_controller.loadLibrary),
      LoadTagedStateMiddleware(() {
        final patientID = QR.params['patientID']!.value as String;
        return patient_profile_controller.PatientProfileController(patientID);
      }, () {
        final patientID = QR.params['patientID']!.value as String;
        return patientID;
      }),
    ],
  ),
  QRoute(
    name: RouteNames.Search,
    path: Routes.path(Routes.Search),
    builder: () {
      try {
        final query = QR.params['q']!.value as String;
        return search.SearchingPateints(query);
      } catch (e) {
        return const URLNotValidPage();
      }
    },
    middleware: [
      DefferedLoader(search.loadLibrary),
      LoadTagedStateMiddleware(() {
        final query = QR.params['q']!.value as String;
        return search.SearchingPateintsController(query);
      }, () {
        final query = QR.params['q']!.value as String;
        return 'searching-$query';
      }),
    ],
  ),
  QRoute(
    path: Routes.path(Routes.OverduePatients),
    name: RouteNames.OverduePatients,
    builder: () {
      return overdue.OverduePateintsPage();
    },
    middleware: [
      DefferedLoader(overdue.loadLibrary),
      DefferedLoader(overdue_controller.loadLibrary),
      LoadStateMiddleware(() => overdue_controller.OverduePateintsController()),
    ],
  ),
  QRoute(
    path: Routes.path(Routes.PatientGroups),
    name: RouteNames.PatientGroups,
    builder: () {
      return groups.PatientGroupsPage();
    },
    middleware: [
      DefferedLoader(groups.loadLibrary),
      DefferedLoader(groups_controller.loadLibrary),
      LoadStateMiddleware(() => groups_controller.PatientGroupsController()),
    ],
  ),
  QRoute(
    path: Routes.path(Routes.PatientGroupMembers),
    name: RouteNames.PatientGroupMembers,
    builder: () {
      return GroupPateintsPage();
    },
    middleware: [
      LoadStateMiddleware(() {
        final query = QR.params['id']!.value as String;
        return GroupPateintsController(query);
      }),
    ],
  ),
];
