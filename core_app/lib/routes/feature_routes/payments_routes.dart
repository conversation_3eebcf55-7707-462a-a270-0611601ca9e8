import 'package:qlevar_router/qlevar_router.dart';

import 'package:core_app/pages/payments/list/list.dart'
    deferred as payments_list;
import 'package:core_app/pages/payments/list/controller.dart'
    deferred as payments_controller;

import '../route_names.dart';
import '../route_paths.dart';
import '../middlewares.dart';

final List<QRoute> paymentsRoutes = [
  QRoute(
    name: RouteNames.Payments,
    path: Routes.path(Routes.Payments),
    builder: () => payments_list.PaymentsListPage(),
    middleware: [
      Deffered<PERSON>oader(payments_list.loadLibrary),
      DefferedLoader(payments_controller.loadLibrary),
      LoadStateMiddleware(() => payments_controller.PaymentsListController()),
    ],
  ),
];
