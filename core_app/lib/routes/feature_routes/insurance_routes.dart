import 'package:qlevar_router/qlevar_router.dart';

import 'package:core_app/pages/insurance/list/list.dart'
    deferred as insurance_list;
import 'package:core_app/pages/insurance/list/controller.dart'
    deferred as insurance_controller;

import '../route_names.dart';
import '../route_paths.dart';
import '../middlewares.dart';

final List<QRoute> insuranceRoutes = [
  QRoute(
    name: RouteNames.Insurance,
    path: Routes.path(Routes.Insurance),
    builder: () => insurance_list.InsuranceList(),
    middleware: [
      DefferedLoader(insurance_list.loadLibrary),
      DefferedLoader(insurance_controller.loadLibrary),
      LoadStateMiddleware(() => insurance_controller.InsuranceListController()),
    ],
  ),
];
