import 'package:qlevar_router/qlevar_router.dart';

import 'package:core_app/pages/inventoreis/list/list.dart'
    deferred as inventories_list;
import 'package:core_app/pages/inventoreis/list/controller.dart'
    deferred as inventories_controller;
import 'package:core_app/pages/inventoreis/transaction/list.dart'
    deferred as transactions_list;
import 'package:core_app/pages/errors/url_not_valid.dart';

import '../route_names.dart';
import '../route_paths.dart';
import '../middlewares.dart';

final List<QRoute> inventoriesRoutes = [
  QRoute(
    name: RouteNames.Inventories,
    path: Routes.path(Routes.Inventories),
    builder: () => inventories_list.InventoriesListPage(),
    middleware: [
      DefferedLoader(inventories_list.loadLibrary),
      DefferedLoader(inventories_controller.loadLibrary),
      LoadStateMiddleware(
          () => inventories_controller.InventoriesListController()),
    ],
  ),
  QRoute(
    name: RouteNames.InventoryTransactions,
    path: Routes.path(Routes.InventoryTransactions),
    builder: () {
      try {
        final inventoryName = QR.params['inventoryName']!.value as String;
        final inventoryID = QR.params['inventoryID']!.value as String;
        final unit = QR.params['unit']!.value as String;
        return transactions_list.TransactionsListPage(
          inventoryID,
          inventoryName: inventoryName,
          unit: unit,
        );
      } catch (e) {
        return const URLNotValidPage();
      }
    },
    middleware: [
      DefferedLoader(transactions_list.loadLibrary),
    ],
  ),
];
