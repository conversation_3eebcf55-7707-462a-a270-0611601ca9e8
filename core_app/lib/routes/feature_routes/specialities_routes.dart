import 'package:qlevar_router/qlevar_router.dart';

import 'package:core_app/pages/specialities/list.dart'
    deferred as specialities_list;
import 'package:core_app/pages/specialities/procedures_list.dart'
    deferred as specialities_procedures_list;
import 'package:core_app/pages/errors/url_not_valid.dart';

import '../route_names.dart';
import '../route_paths.dart';
import '../middlewares.dart';

final List<QRoute> specialitiesRoutes = [
  QRoute(
    name: RouteNames.SpecialityTemplates,
    path: Routes.path(Routes.SpecialityTemplates),
    builder: () => specialities_list.SpecialitiesListPage(),
    middleware: [
      DefferedLoader(specialities_list.loadLibrary),
      LoadStateMiddleware(
          () => specialities_list.SpecialititesListController()),
    ],
  ),
  QRoute(
    name: RouteNames.SpecialityProcedures,
    path: Routes.path(Routes.SpecialityProcedures),
    builder: () {
      try {
        final specialityID = QR.params['specialityID']!.value as String;
        return specialities_procedures_list.ProceduresListPage(specialityID);
      } catch (e) {
        return const URLNotValidPage();
      }
    },
    middleware: [
      DefferedLoader(specialities_procedures_list.loadLibrary),
      LoadStateMiddleware(() {
        final specialityID = QR.params['specialityID']!.value as String;
        return specialities_procedures_list.SpecialityProceduresListController(
            specialityID);
      }),
    ],
  ),
];
