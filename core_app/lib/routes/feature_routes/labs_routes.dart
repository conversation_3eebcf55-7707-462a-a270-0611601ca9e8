import 'package:qlevar_router/qlevar_router.dart';

import 'package:core_app/pages/labs/list.dart' deferred as labs;

import '../route_names.dart';
import '../route_paths.dart';
import '../middlewares.dart';

final List<QRoute> labsRoutes = [
  QRoute(
    name: RouteNames.Labs,
    path: Routes.path(Routes.Labs),
    builder: () => labs.LabsListPage(),
    middleware: [
      Deffered<PERSON>oader(labs.loadLibrary),
      LoadStateMiddleware(() => labs.LabsListController()),
    ],
  ),
];
