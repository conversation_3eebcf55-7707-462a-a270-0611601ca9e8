import 'package:qlevar_router/qlevar_router.dart';

import 'package:core_app/pages/branches/list/list.dart'
    deferred as branches_list;
import 'package:core_app/pages/branches/list/controller.dart'
    deferred as branches_controller;

import '../route_names.dart';
import '../route_paths.dart';
import '../middlewares.dart';

final List<QRoute> branchesRoutes = [
  QRoute(
    name: RouteNames.Branches,
    path: Routes.path(Routes.Branches),
    builder: () => branches_list.BranchesListPage(),
    middleware: [
      Deffered<PERSON>oader(branches_list.loadLibrary),
      Deffered<PERSON>oader(branches_controller.loadLibrary),
      LoadStateMiddleware(() => branches_controller.BranchesListController()),
    ],
  ),
];
