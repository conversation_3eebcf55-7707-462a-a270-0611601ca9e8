import 'package:qlevar_router/qlevar_router.dart';

import 'package:core_app/pages/expenses/list/list.dart'
    deferred as expenses_list;
import 'package:core_app/pages/expenses/list/controller.dart'
    deferred as expenses_controller;

import '../route_names.dart';
import '../route_paths.dart';
import '../middlewares.dart';

final List<QRoute> expensesRoutes = [
  QRoute(
    name: RouteNames.Expenses,
    path: Routes.path(Routes.Expenses),
    builder: () => expenses_list.ExpensesListPage(),
    middleware: [
      Deffered<PERSON>oader(expenses_list.loadLibrary),
      DefferedLoader(expenses_controller.loadLibrary),
      LoadStateMiddleware(() => expenses_controller.ExpensesListController()),
    ],
  ),
];
