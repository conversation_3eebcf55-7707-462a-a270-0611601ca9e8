import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:qlevar_router/qlevar_router.dart';

class Navigation {
  static final Map<String, String> query = {};
  static final GlobalKey<NavigatorState> navKey = Get.key;
  static BuildContext get navigatorContext => navKey.currentState!.context;

  static double get width {
    return MediaQuery.of(navigatorContext).size.width;
  }

  static double get height {
    return MediaQuery.of(navigatorContext).size.height;
  }

  static String queryAsKey(String key) {
    return QR.params[key]?.value as String? ?? query[key]!;
  }

  static Future<T?> push<T>(Widget page, {bool replace = false}) {
    return navKey.currentState!.push<T>(
      MaterialPageRoute(
        builder: (context) => page,
        fullscreenDialog: false,
      ),
    );
  }

  static Future<T?> to<T>(String path, [Map<String, String>? query]) {
    return QR.to<T>(
      Uri(
        path: path,
        queryParameters: query,
      ).toString(),
      ignoreSamePath: true,
    );
  }

  static Future<T?> toNew<T>(String path, [Map<String, String>? query]) {
    return QR.to<T>(
      Uri(
        path: path,
        queryParameters: query,
      ).toString(),
      ignoreSamePath: false,
      pageAlreadyExistAction: PageAlreadyExistAction.BringToTop,
    );
  }

  static Future<void> off<T>(String path, [Map<String, String>? query]) {
    return QR.replaceAll(path);
  }

  static Future<PopResult> back([dynamic result]) {
    return QR.back(result);
  }
}
