import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

class StorageService extends GetxService {
  StorageService();
  static StorageService get to => Get.find();
  Future<StorageService> init() async {
    await GetStorage.init("naab");
    storage = GetStorage("naab");
    return this;
  }

  late final GetStorage storage;
  static GetStorage get getStorage => to.storage;

  @override
  Future<void> onClose() async {
    await storage.save();
    super.onClose();
  }

  static String? get userToken => getStorage.read<String>('userToken');
  static set userToken(String? token) => getStorage.write('userToken', token);
  static void removeUserToken() => getStorage.remove('userToken');

  static Map<String, dynamic>? get userData => getStorage.read<Map<String, dynamic>>('userData');
  static set userData(Map<String, dynamic>? token) => getStorage.write('userData', token);
  static void removeUserData() => getStorage.remove('userData');

  static Map<String, dynamic>? get clinicData =>
      getStorage.read<Map<String, dynamic>>('clinicData');
  static set clinicData(Map<String, dynamic>? token) => getStorage.write('clinicData', token);
  static void removeClinicData() => getStorage.remove('clinicData');

  static String? get language => getStorage.read<String>('language');
  static set language(String? token) => getStorage.write('language', token);
  static void removeLanguage() => getStorage.remove('language');

  static List<Map<String, dynamic>>? get chatMessages =>
      getStorage.read<List<Map<String, dynamic>>>('chatMessages');
  static set chatMessages(List<Map<String, dynamic>>? token) =>
      getStorage.write('chatMessages', token);
  static void removeChatMessages() => getStorage.remove('chatMessages');
}
