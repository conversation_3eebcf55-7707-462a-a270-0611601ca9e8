import 'dart:async';

import 'package:core_app/routes/navigation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'notice.dart';

enum _MessageType {
  notice,
}

class MsgColor {
  static const Color success = Colors.green;
  static const Color danger = Colors.red;
}

class _Message {
  final GlobalKey key;
  final _MessageType type;
  final OverlayEntry overlayEntry;
  _Message({
    required this.key,
    required this.type,
    required this.overlayEntry,
  });

  get state => key.currentState!;
}

class NotifyService extends GetxService {
  static NotifyService get to => Get.find();

  final messages = <_Message>[];

  NotifyService init() {
    return this;
  }

  void removeEntry(OverlayEntry entry) {
    messages.removeWhere((element) => entry == element.overlayEntry);
    entry.remove();
  }

  Future<void> clear() async {
    // where are using list to seprate the for loop list from the messages list
    //
    // If we use the native messages list it will throw an error
    // Unhandled Exception: Concurrent modification during iteration: Instance(length:0) of '_GrowableList'.
    for (var message in List.of(messages)) {
      await message.state.forceRemove();
    }
  }

  static Future<void> notice({
    required String title,
    String? body,
    Color msgColor = MsgColor.danger,
  }) async {
    var to = NotifyService.to;
    await to.clear();
    // ignore: use_build_context_synchronously
    final navigatorState =
        Navigator.of(Navigation.navigatorContext, rootNavigator: false);
    final overlayState = navigatorState.overlay!;

    late final OverlayEntry overlayEntry;
    final globalKey = GlobalKey<NoticeBuilderState>();
    overlayEntry = OverlayEntry(
      builder: (_) {
        return NoticeBuilder(
          title: title,
          body: body,
          msgColor: msgColor,
          entry: overlayEntry,
          key: globalKey,
        );
      },
    );

    overlayState.insert(overlayEntry);
    to.messages.add(_Message(
      key: globalKey,
      type: _MessageType.notice,
      overlayEntry: overlayEntry,
    ));
  }

  static success(String title) => notice(
        title: title,
        msgColor: MsgColor.success,
      );
}
