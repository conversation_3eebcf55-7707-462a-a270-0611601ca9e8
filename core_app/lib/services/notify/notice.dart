import 'dart:async';

import 'package:core_app/core/utils/responsive.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'messenger.dart';

class NoticeBuilder extends StatefulWidget {
  const NoticeBuilder({
    super.key,
    required this.entry,
    required this.title,
    required this.body,
    required this.msgColor,
  });

  final String title;
  final String? body;
  final OverlayEntry entry;
  final Color msgColor;

  @override
  State<NoticeBuilder> createState() => NoticeBuilderState();
}

class NoticeBuilderState extends State<NoticeBuilder> with SingleTickerProviderStateMixin {
  late Animation<double> animation;
  late AnimationController animationController;
  late Timer timer;

  Future<void> forceRemove() {
    timer.cancel();
    return animationController
        .reverse()
        .whenComplete(() => NotifyService.to.removeEntry(widget.entry));
  }

  @override
  void initState() {
    animationController = AnimationController(vsync: this, duration: const Duration(seconds: 1));
    animation = CurveTween(curve: Curves.fastOutSlowIn).animate(animationController);
    super.initState();

    animationController.addListener(() {
      setState(() {});
    });

    animationController.forward();

    timer = Timer.periodic(const Duration(seconds: 5), (timer) {
      animationController.reverse().whenComplete(forceRemove);
    });
  }

  @override
  void dispose() {
    animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final msg = Text.rich(TextSpan(
        style: const TextStyle(
          color: Colors.white,
        ),
        children: [
          TextSpan(text: widget.title),
          if (widget.body?.isNotEmpty ?? false) TextSpan(text: ':  ${widget.body!}'),
        ]));

    return Positioned(
      bottom: 21,
      right: 21,
      child: FadeTransition(
        opacity: animation,
        child: Material(
          color: Colors.transparent,
          child: Container(
            width: context.mobileView ? context.width * 0.9 : null,
            padding: const EdgeInsets.symmetric(horizontal: 11, vertical: 7),
            decoration: BoxDecoration(
              color: widget.msgColor,
              borderRadius: BorderRadius.circular(11),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const Icon(
                  Icons.info_outline_rounded,
                  color: Colors.white,
                ),
                const SizedBox(width: 7),
                context.mobileView ? Expanded(child: msg) : msg,
              ],
            ),
          ),
        ),
      ),
    );
  }
}
