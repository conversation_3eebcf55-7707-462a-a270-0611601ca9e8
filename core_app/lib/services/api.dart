import 'package:core_app/core/constants/configuration.dart';
import 'package:core_app/core/utils/json.dart';
import 'package:dio/dio.dart';
import 'package:get/get.dart' hide Response;

/// Response type to separate the getx Response from the dio Response
typedef DioRes = Response<Json>;

class ApiService extends GetxController {
  static ApiService get to => Get.find();

  late final Dio _dio;
  static Dio get dio => ApiService.to._dio;

  ApiService init() {
    _dio = Dio(
      BaseOptions(
        baseUrl: BASE_URL,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        // validateStatus: (status) {
        //   return status! < 500;
        // },
      ),
    );
    return this;
  }

  void setToken(String token) {
    dio.options.headers['token'] = token;
  }

  void removeToken() {
    dio.options.headers.remove('token');
  }

  static Future<Response<Json>> get(String path,
      {Map<String, dynamic>? queryParameters}) {
    return to._dio.get<Json>(path, queryParameters: queryParameters);
  }

  static Future<Response<List>> getList(String path,
      {Map<String, dynamic>? queryParameters}) {
    return to._dio.get<List>(path, queryParameters: queryParameters);
  }

  static Future<Response<Json>> post(String path,
      {Map<String, dynamic>? queryParameters, dynamic data}) {
    return to._dio
        .post<Json>(path, queryParameters: queryParameters, data: data);
  }

  static Future<Response<Json>> put(String path,
      {Map<String, dynamic>? queryParameters, dynamic data}) {
    return to._dio
        .put<Json>(path, queryParameters: queryParameters, data: data);
  }

  static Future<Response<Json>> delete(String path,
      {Map<String, dynamic>? queryParameters}) {
    return to._dio.delete<Json>(path, queryParameters: queryParameters);
  }

  static Future<Response<Json>> patch(String path,
      {Map<String, dynamic>? queryParameters, dynamic data}) {
    return to._dio
        .patch<Json>(path, queryParameters: queryParameters, data: data);
  }
}
