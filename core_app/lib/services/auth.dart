import 'package:core_app/core/api/users.dart';
import 'package:core_app/core/constants/configuration.dart';
import 'package:core_app/core/fcm_init.dart';
import 'package:core_app/core/models/clinic.dart';
import 'package:core_app/core/models/user.dart';
import 'package:core_app/pages/auth/login/login.dart';
import 'package:core_app/services/api.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:jwt_decode/jwt_decode.dart';

import 'storage.dart';

class AuthService extends GetxService {
  static AuthService get to => Get.find();
  AuthService init() {
    // Check token existance and expiration
    final String? savedToken = StorageService.userToken;
    if (savedToken == null) return this;
    bool isExpired = Jwt.isExpired(savedToken);
    if (isExpired) return this;

    // Set logged user data
    token.value = savedToken;
    final userData = StorageService.userData!;
    user.value = UserModel.fromJson(userData);

    // Set clinic data
    final clinicData = StorageService.clinicData;
    if (clinicData != null) clinic.value = ClinicModel.fromJson(clinicData);
    // Set Dio token header & refresh data
    ApiService.to.setToken(savedToken);
    refreshData();
    return this;
  }

  final RxString token = ''.obs;
  final user = Rx<UserModel?>(null);
  final clinic = Rx<ClinicModel?>(null);
  final notification = Rx<PatientNotificationConfigModel?>(null);

  static String get getToken => to.token.value;
  static UserModel get getUser => to.user.value!;
  static bool get loggedIn => to.token.isNotEmpty || to.user.value != null;

  bool get isSubEnded {
    final DateTime now = DateTime.now();
    if (clinic.value?.subscriptionEndDate != null) {
      if (clinic.value!.subscriptionEndDate.isBefore(now)) return true;
    }
    final difference = now.difference(clinic.value!.subscriptionRenewedAt);
    if (difference.inDays > 30) return true;
    return false;
  }

  /// save user data to local storage (token, user repo)
  Future<void> login(String t) async {
    ApiService.to.setToken(t);
    final repo = await UsersAPI.me();
    token.value = t;
    user.value = repo.user;
    clinic.value = repo.clinic;
    notification.value = repo.patientNotificationConfig;

    StorageService.userToken = t;
    StorageService.userData = repo.user.toJson();
    StorageService.clinicData = repo.clinic.toJson();

    if (kIsWeb == false && supportsFirebase) {
      await FCMInitializer.setToken();
      await FCMInitializer.initRemoteConfig();
    }
  }

  /// remove user data from local storage
  Future<void> logout() async {
    token.value = '';
    user.value = null;
    clinic.value = null;
    notification.value = null;

    StorageService.removeUserToken();
    StorageService.removeUserData();
    StorageService.removeClinicData();

    ApiService.to.removeToken();
    // TempDataService.to.clear();
  }

  // refresh user data
  Future<void> refreshData() async {
    if (token.isEmpty) return;
    // catch Dio 401 error
    try {
      final res = await UsersAPI.me();
      user.value = res.user;
      clinic.value = res.clinic;
      notification.value = res.patientNotificationConfig;

      StorageService.userData = res.user.toJson();
      StorageService.clinicData = res.clinic.toJson();
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        await logout();
        Get.offAll(() => const LoginPage());
      }
    }
  }
}
