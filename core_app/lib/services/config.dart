import 'package:core_app/services/auth.dart';
import 'package:get/get.dart';

enum VersionStatus {
  upToDate,
  needUpdate,
  needForceUpdate,
}

class ConfigService extends GetxService {
  static ConfigService get to => Get.find();

  final stableInternet = true.obs;
  String get currency => AuthService.to.clinic.value?.currency.toUpperCase() ?? 'EGP';

  final _vesrionStatus = VersionStatus.upToDate.obs;
  VersionStatus get versionStatus => _vesrionStatus.value;
  set versionStatus(VersionStatus value) => _vesrionStatus.value = value;
}
