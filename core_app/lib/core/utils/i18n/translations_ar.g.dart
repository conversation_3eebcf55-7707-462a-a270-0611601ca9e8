///
/// Generated file. Do not edit.
///
// coverage:ignore-file
// ignore_for_file: type=lint, unused_import

import 'package:flutter/widgets.dart';
import 'package:intl/intl.dart';
import 'package:slang/generated.dart';
import 'translations.g.dart';

// Path: <root>
class TranslationsAr extends Translations {
	/// You can call this constructor and build your own translation instance of this locale.
	/// Constructing via the enum [AppLocale.build] is preferred.
	TranslationsAr({Map<String, Node>? overrides, PluralResolver? cardinalResolver, PluralResolver? ordinalResolver, TranslationMetadata<AppLocale, Translations>? meta})
		: assert(overrides == null, 'Set "translation_overrides: true" in order to enable this feature.'),
		  $meta = meta ?? TranslationMetadata(
		    locale: AppLocale.ar,
		    overrides: overrides ?? {},
		    cardinalResolver: cardinalResolver,
		    ordinalResolver: ordinalResolver,
		  ),
		  super(cardinalResolver: cardinalResolver, ordinalResolver: ordinalResolver) {
		super.$meta.setFlatMapFunction($meta.getTranslation); // copy base translations to super.$meta
		$meta.setFlatMapFunction(_flatMapFunction);
	}

	/// Metadata for the translations of <ar>.
	@override final TranslationMetadata<AppLocale, Translations> $meta;

	/// Access flat map
	@override dynamic operator[](String key) => $meta.getTranslation(key) ?? super.$meta.getTranslation(key);

	late final TranslationsAr _root = this; // ignore: unused_field

	@override 
	TranslationsAr $copyWith({TranslationMetadata<AppLocale, Translations>? meta}) => TranslationsAr(meta: meta ?? this.$meta);

	// Translations
	@override String get skip => 'تخطي';
	@override String get delete => 'حذف';
	@override String get search => 'بحث';
	@override String get none => 'لا شيء';
	@override String get yes => 'نعم';
	@override String get no => 'لا';
	@override String get noData => 'لا يوجد بيانات';
	@override String get edit => 'تعديل';
	@override String get finish => 'انهاء';
	@override String get error => 'خطأ';
	@override String get blankFields => 'الرجاء ملئ الحقول الفارغة';
	@override String pleaseFillRequiredFields({required Object field}) => 'الرجاء ملئ الحقول المطلوبة ${field}';
	@override String get createdAt => 'تاريخ الانشاء';
	@override String get subEnded => 'انتهى الاشتراك الفرعي';
	@override String get switchView => 'تبديل العرض';
	@override String get from => 'من';
	@override String get to => 'الى';
	@override String get cancel => 'الغاء';
	@override String get confirm => 'تأكيد';
	@override String get english => 'الانجليزية';
	@override String get arabic => 'العربية';
	@override Map<String, String> get locales => {
		'en': 'English',
		'ar': 'العربية',
	};
	@override late final _TranslationsButtonTxtAr buttonTxt = _TranslationsButtonTxtAr._(_root);
	@override late final _TranslationsClinicAr clinic = _TranslationsClinicAr._(_root);
	@override late final _TranslationsFormErrorsAr formErrors = _TranslationsFormErrorsAr._(_root);
	@override late final _TranslationsLoginAr login = _TranslationsLoginAr._(_root);
	@override late final _TranslationsNavAr nav = _TranslationsNavAr._(_root);
	@override late final _TranslationsAnalysisAr analysis = _TranslationsAnalysisAr._(_root);
	@override late final _TranslationsBranchesAr branches = _TranslationsBranchesAr._(_root);
	@override late final _TranslationsPatientsListAr patientsList = _TranslationsPatientsListAr._(_root);
	@override late final _TranslationsPatientFormAr patientForm = _TranslationsPatientFormAr._(_root);
	@override late final _TranslationsUserFormAr userForm = _TranslationsUserFormAr._(_root);
	@override late final _TranslationsUsersAr users = _TranslationsUsersAr._(_root);
	@override late final _TranslationsExpenseFormAr expenseForm = _TranslationsExpenseFormAr._(_root);
	@override late final _TranslationsExpensesListAr expensesList = _TranslationsExpensesListAr._(_root);
	@override late final _TranslationsInventoryTransactionFormAr inventoryTransactionForm = _TranslationsInventoryTransactionFormAr._(_root);
	@override late final _TranslationsPatientAr patient = _TranslationsPatientAr._(_root);
	@override late final _TranslationsAppointmentAr appointment = _TranslationsAppointmentAr._(_root);
	@override late final _TranslationsPatientProfileAr patientProfile = _TranslationsPatientProfileAr._(_root);
	@override late final _TranslationsPaymentsViewAr paymentsView = _TranslationsPaymentsViewAr._(_root);
	@override late final _TranslationsPaymentAr payment = _TranslationsPaymentAr._(_root);
	@override late final _TranslationsProfileVisitItemAr profileVisitItem = _TranslationsProfileVisitItemAr._(_root);
	@override late final _TranslationsSpecialitiesListAr specialitiesList = _TranslationsSpecialitiesListAr._(_root);
	@override late final _TranslationsSpecialityAr speciality = _TranslationsSpecialityAr._(_root);
	@override late final _TranslationsProcedureTemplateAr procedureTemplate = _TranslationsProcedureTemplateAr._(_root);
	@override late final _TranslationsProcedureTemplateFormAr procedureTemplateForm = _TranslationsProcedureTemplateFormAr._(_root);
	@override late final _TranslationsVisitFormAr visitForm = _TranslationsVisitFormAr._(_root);
	@override late final _TranslationsVisitAr visit = _TranslationsVisitAr._(_root);
	@override late final _TranslationsVisitProcedureAr visitProcedure = _TranslationsVisitProcedureAr._(_root);
	@override late final _TranslationsVisitProcedureFormAr visitProcedureForm = _TranslationsVisitProcedureFormAr._(_root);
	@override late final _TranslationsInventoryViewAr inventoryView = _TranslationsInventoryViewAr._(_root);
	@override late final _TranslationsInventoryAr inventory = _TranslationsInventoryAr._(_root);
	@override late final _TranslationsInsuranceViewAr insuranceView = _TranslationsInsuranceViewAr._(_root);
	@override late final _TranslationsInsuranceClaimAr insuranceClaim = _TranslationsInsuranceClaimAr._(_root);
	@override late final _TranslationsInsuranceClaimViewAr insuranceClaimView = _TranslationsInsuranceClaimViewAr._(_root);
	@override late final _TranslationsInsuranceClaimFormAr insuranceClaimForm = _TranslationsInsuranceClaimFormAr._(_root);
	@override late final _TranslationsInsuranceCompanyAr insuranceCompany = _TranslationsInsuranceCompanyAr._(_root);
	@override late final _TranslationsInsuranceCompanyViewAr insuranceCompanyView = _TranslationsInsuranceCompanyViewAr._(_root);
	@override late final _TranslationsCustomPaymentMethodsAr custom_payment_methods = _TranslationsCustomPaymentMethodsAr._(_root);
	@override late final _TranslationsSettingsAr settings = _TranslationsSettingsAr._(_root);
	@override late final _TranslationsLabAr lab = _TranslationsLabAr._(_root);
	@override late final _TranslationsLabViewAr labView = _TranslationsLabViewAr._(_root);
	@override late final _TranslationsLabFormAr labForm = _TranslationsLabFormAr._(_root);
	@override late final _TranslationsLabRequestViewAr labRequestView = _TranslationsLabRequestViewAr._(_root);
	@override late final _TranslationsLabRequestFormAr labRequestForm = _TranslationsLabRequestFormAr._(_root);
	@override late final _TranslationsLabRequestAr labRequest = _TranslationsLabRequestAr._(_root);
	@override late final _TranslationsOverduePatientsAr overduePatients = _TranslationsOverduePatientsAr._(_root);
	@override late final _TranslationsPatientGroupsAr patientGroups = _TranslationsPatientGroupsAr._(_root);
}

// Path: buttonTxt
class _TranslationsButtonTxtAr extends TranslationsButtonTxtEn {
	_TranslationsButtonTxtAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get add => 'اضافة';
	@override String get save => 'حفظ';
	@override String get cancel => 'الغاء';
}

// Path: clinic
class _TranslationsClinicAr extends TranslationsClinicEn {
	_TranslationsClinicAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get name => 'اسم العيادة';
	@override String get language => 'اللغة';
	@override String get daysPrior => 'عدد ايام الاشعارات القادمة';
}

// Path: formErrors
class _TranslationsFormErrorsAr extends TranslationsFormErrorsEn {
	_TranslationsFormErrorsAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get invalid => 'غير صحيح';
	@override String get shouldBeNumber => 'يجب ان يكون رقم';
}

// Path: login
class _TranslationsLoginAr extends TranslationsLoginEn {
	_TranslationsLoginAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get clinicName => 'اسم العيادة';
	@override String get username => 'اسم المستخدم';
	@override String get password => 'كلمة المرور';
	@override String get login => 'تسجيل الدخول';
}

// Path: nav
class _TranslationsNavAr extends TranslationsNavEn {
	_TranslationsNavAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get dashboard => 'الرئيسية';
	@override String get data => 'معلومات';
	@override String get administration => 'التحكم';
	@override String get home => 'الرئيسية';
	@override String get analysis => 'تحليلات';
	@override String get insurance => 'التأمين';
	@override String get patients => 'المرضى';
	@override String get expeenses => 'المصروفات';
	@override String get inventory => 'المخزون';
	@override String get payments => 'المدفوعات';
	@override String get specialities => 'التخصصات';
	@override String get labs => 'المختبرات';
	@override String get users => 'المستخدمين';
	@override String get branches => 'الفروع';
	@override String get communication => 'التواصل';
}

// Path: analysis
class _TranslationsAnalysisAr extends TranslationsAnalysisEn {
	_TranslationsAnalysisAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get title => 'تحليلات';
	@override String get visitsDiagram => 'رسم بياني للزيارات';
	@override String get visits => 'الزيارات';
	@override String get patients => 'المرضى';
	@override String get noData => 'لا يوجد بيانات';
	@override String get expenses => ' المصروفات';
	@override String get patientsReachChannel => 'مصادر الوصول للمرضى';
	@override String get unknown => 'غير معروف';
}

// Path: branches
class _TranslationsBranchesAr extends TranslationsBranchesEn {
	_TranslationsBranchesAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get title => 'الفروع';
	@override String get add => 'اضافة فرع';
	@override String get edit => 'تعديل فرع';
	@override String get name => 'الاسم';
	@override String get selectColor => 'اختر لون';
	@override String get googleMapURL => 'رابط خرائط جوجل';
	@override String get clear => 'مسح';
	@override String get googleLink => 'رابط خرائط جوجل';
	@override String get mapView => 'عرض الخريطة';
	@override String get roomsCount => 'عدد الغرف';
	@override String get branchUpdated => 'تم تعديل الفرع بنجاح';
	@override String get branchAdded => 'تم اضافة الفرع بنجاح';
	@override String get branchDeleted => 'تم حذف الفرع بنجاح';
	@override String get deleteBranch => 'حذف الفرع';
	@override String get listBranches => 'قائمة الفروع';
}

// Path: patientsList
class _TranslationsPatientsListAr extends TranslationsPatientsListEn {
	_TranslationsPatientsListAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get title => 'قائمة المرضى';
	@override String get add => 'اضافة مريض';
	@override String get edit => 'تعديل مريض';
}

// Path: patientForm
class _TranslationsPatientFormAr extends TranslationsPatientFormEn {
	_TranslationsPatientFormAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get title => 'اضافة مريض';
	@override String get name => 'الاسم';
	@override String get email => 'البريد الإلكتروني';
	@override String get phone => 'رقم الهاتف';
	@override String get age => 'السن';
	@override String get balance => 'الرصيد';
	@override String get reachChannel => 'مصدر الوصول';
	@override String get address => 'العنوان';
	@override String get birthdate => 'تاريخ الميلاد';
	@override String get job => 'الوظيفة';
	@override String get fileNumber => 'رقم الملف';
	@override String get dentalHistory => 'تاريخ الاسنان';
	@override String get medicalHistory => 'التاريخ الطبي';
	@override String get treatmentPlan => 'خطة العلاج';
	@override late final _TranslationsPatientFormReachChannelsAr reachChannels = _TranslationsPatientFormReachChannelsAr._(_root);
}

// Path: userForm
class _TranslationsUserFormAr extends TranslationsUserFormEn {
	_TranslationsUserFormAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get title => 'اضافة مستخدم';
	@override String get name => 'الاسم';
	@override String get username => 'اسم المستخدم';
	@override String get password => 'كلمة المرور';
	@override String get role => 'الدور';
	@override String get isDentist => 'طبيب اسنان';
	@override String get percentage => 'النسبة';
}

// Path: users
class _TranslationsUsersAr extends TranslationsUsersEn {
	_TranslationsUsersAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get list => 'قائمة المستخدمين';
	@override String get add => 'اضافة مستخدم';
	@override String get userUpdated => 'تم تعديل المستخدم بنجاح';
	@override String get userAdded => 'تم اضافة المستخدم بنجاح';
	@override String get userDeleted => 'تم حذف المستخدم بنجاح';
	@override String get confirmDelete => 'هل انت متأكد من حذف المستخدم؟';
	@override String get confirmDeleteUser => 'هل انت متأكد من حذف المستخدم؟';
	@override late final _TranslationsUsersRolesAr roles = _TranslationsUsersRolesAr._(_root);
}

// Path: expenseForm
class _TranslationsExpenseFormAr extends TranslationsExpenseFormEn {
	_TranslationsExpenseFormAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get title => 'اضافة مصروف';
	@override String get name => 'الاسم';
	@override String get amount => 'المبلغ';
	@override String get description => 'الوصف';
	@override String get type => 'النوع';
	@override late final _TranslationsExpenseFormTypesAr types = _TranslationsExpenseFormTypesAr._(_root);
}

// Path: expensesList
class _TranslationsExpensesListAr extends TranslationsExpensesListEn {
	_TranslationsExpensesListAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get title => 'قائمة المصروفات';
	@override String get add => 'اضافة مصروف';
	@override String get edit => 'تعديل مصروف';
}

// Path: inventoryTransactionForm
class _TranslationsInventoryTransactionFormAr extends TranslationsInventoryTransactionFormEn {
	_TranslationsInventoryTransactionFormAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get changeQuantity => 'تغيير الكمية';
	@override String get label => 'اكتب كمية المخزن الموجود حاليا';
	@override String get quantity => 'الكمية';
}

// Path: patient
class _TranslationsPatientAr extends TranslationsPatientEn {
	_TranslationsPatientAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get addNewPatient => 'اضافة مريض جديد';
	@override String askToCreatePatient({required Object name}) => 'هل تريد اضافة مريض جديد؟ `${name}`';
}

// Path: appointment
class _TranslationsAppointmentAr extends TranslationsAppointmentEn {
	_TranslationsAppointmentAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get addNewAppointment => 'اضافة موعد جديد';
	@override String get branch => 'الفرع';
	@override String get patient => 'المريض';
	@override String get dentist => 'الطبيب';
	@override String get room => 'الغرفة';
	@override String roomNumber({required Object number}) => 'رقم الغرفة ${number}';
	@override String get startTime => 'وقت البدء';
	@override String get endTime => 'وقت الانتهاء';
	@override String get duration => 'المدة';
	@override String from({required Object time}) => 'من ${time}';
	@override String to({required Object time}) => 'الى ${time}';
	@override String get chooseFutureDate => 'اختر تاريخ مستقبلي';
	@override late final _TranslationsAppointmentCancelAr cancel = _TranslationsAppointmentCancelAr._(_root);
}

// Path: patientProfile
class _TranslationsPatientProfileAr extends TranslationsPatientProfileEn {
	_TranslationsPatientProfileAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get appointments => 'المواعيد';
	@override String get insuranceClaims => 'المطالبات';
	@override String get visitsHistory => 'تاريخ الزيارات';
	@override String get visits => 'الزيارات';
	@override String get patientProfile => 'ملف المريض';
	@override String get edit => 'تعديل';
	@override String get switchTeethView => 'تبديل عرض الاسنان';
	@override String get paymentsList => 'قائمة المدفوعات';
	@override String get noTreatmentPlan => 'لا يوجد خطة علاج';
	@override String get payments => 'المدفوعات';
	@override String get about => 'عن المريض';
}

// Path: paymentsView
class _TranslationsPaymentsViewAr extends TranslationsPaymentsViewEn {
	_TranslationsPaymentsViewAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get list => 'قائمة المدفوعات';
	@override String get newPayment => 'مدفوعات جديدة';
	@override String get add => 'اضافة مدفوعات';
	@override String get edit => 'تعديل مدفوعات';
	@override String get paidAmount => 'المبلغ المدفوع';
	@override String get cancel => 'ملغى';
	@override String get cancelTitle => 'الغاء المدفوعات';
	@override String get cancelFormReason => 'سبب الالغاء';
	@override String get cancelFormValidation => 'الرجاء ملئ سبب الالغاء';
	@override String get cancelled => 'ملغى';
}

// Path: payment
class _TranslationsPaymentAr extends TranslationsPaymentEn {
	_TranslationsPaymentAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get amount => 'المبلغ';
	@override String get type => 'النوع';
	@override String get branch => 'الفرع';
	@override String get patient => 'المريض';
	@override String get createdAt => 'تاريخ الانشاء';
	@override String get createdBy => 'انشئ بواسطة';
	@override String get cancelled => 'ملغى';
	@override String get cancelledBy => 'ملغى بواسطة';
	@override String get cancellationReason => 'سبب الالغاء';
	@override late final _TranslationsPaymentTypesAr types = _TranslationsPaymentTypesAr._(_root);
}

// Path: profileVisitItem
class _TranslationsProfileVisitItemAr extends TranslationsProfileVisitItemEn {
	_TranslationsProfileVisitItemAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get discount => 'خصم';
	@override String get toothNumber => 'رقم السن';
	@override String get procedures => 'الاجراءات';
	@override String get noProcedures => 'لا يوجد اجراءات';
	@override String get diagnosis => 'التشخيصات';
	@override String get noDiagnosis => 'لا يوجد تشخيصات';
	@override String get treatments => 'العلاجات';
	@override String get noTreatments => 'لا يوجد علاجات';
	@override String get comment => 'التعليق';
	@override String get noComment => 'لا يوجد تعليق';
}

// Path: specialitiesList
class _TranslationsSpecialitiesListAr extends TranslationsSpecialitiesListEn {
	_TranslationsSpecialitiesListAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get title => 'قائمة التخصصات';
	@override String get add => 'اضافة تخصص';
	@override String get edit => 'تعديل تخصص';
}

// Path: speciality
class _TranslationsSpecialityAr extends TranslationsSpecialityEn {
	_TranslationsSpecialityAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get name => 'الاسم';
}

// Path: procedureTemplate
class _TranslationsProcedureTemplateAr extends TranslationsProcedureTemplateEn {
	_TranslationsProcedureTemplateAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get procedures => 'الاجراءات';
	@override String get editProcedure => 'تعديل الاجراءات';
	@override String get addProcedure => 'اضافة اجراءات';
	@override String get name => 'الاسم';
	@override String get price => 'السعر';
	@override String get teethRemoved => 'الاسنان المستأصلة';
	@override String get crown => 'تاج';
	@override String get endo => 'علاج عصب';
	@override String get implant => 'زرع';
	@override String get operative => 'ترميم';
	@override String get speciality => 'التخصص';
}

// Path: procedureTemplateForm
class _TranslationsProcedureTemplateFormAr extends TranslationsProcedureTemplateFormEn {
	_TranslationsProcedureTemplateFormAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get saveAndAddOther => 'حفظ واضافة اخرى';
}

// Path: visitForm
class _TranslationsVisitFormAr extends TranslationsVisitFormEn {
	_TranslationsVisitFormAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get createVisit => 'انشاء زيارة';
	@override String get viewPatientProfile => 'عرض ملف المريض';
	@override String get noProcedures => 'لا يوجد اجراءات';
	@override String get noFiles => 'لا يوجد ملفات';
}

// Path: visit
class _TranslationsVisitAr extends TranslationsVisitEn {
	_TranslationsVisitAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get comments => 'التعليقات';
	@override String get diagnosis => 'التشخيصات';
	@override String get nextVisit => 'الزيارة القادمة';
	@override String get treatments => 'العلاجات';
	@override String get procedures => 'الاجراءات';
	@override String get files => 'الملفات';
}

// Path: visitProcedure
class _TranslationsVisitProcedureAr extends TranslationsVisitProcedureEn {
	_TranslationsVisitProcedureAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get procedure => 'الاجراء';
	@override String get price => 'السعر';
	@override String get discount => 'التخفيض';
	@override String get total => 'الاجمالي';
	@override String get teethRemoved => 'الاسنان المستأصلة';
	@override String get crown => 'تاج';
	@override String get endo => 'علاج عصب';
	@override String get implant => 'زرع';
	@override String get operative => 'ترميم';
	@override String get speciality => 'التخصص';
	@override String get dentist => 'الاسنان';
	@override String get notes => 'الملاحظات';
	@override String get nextVisit => 'الزيارة القادمة';
}

// Path: visitProcedureForm
class _TranslationsVisitProcedureFormAr extends TranslationsVisitProcedureFormEn {
	_TranslationsVisitProcedureFormAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get create => 'انشاء اجراء';
	@override String get price => 'السعر';
	@override String get discount => 'التخفيض';
	@override String get total => 'الاجمالي';
	@override String get teethRemoved => 'الاسنان المستأصلة';
	@override String get crown => 'تاج';
	@override String get endo => 'علاج عصب';
	@override String get implant => 'زرع';
	@override String get operative => 'ترميم';
	@override String get speciality => 'التخصص';
	@override String get affectedTeeth => 'الاسنان المتأثرة';
	@override String get chooseTeeth => 'اختر الاسنان';
	@override String get chooseSpecialityAndProcedure => 'اختر التخصص والاجراء';
}

// Path: inventoryView
class _TranslationsInventoryViewAr extends TranslationsInventoryViewEn {
	_TranslationsInventoryViewAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get list => 'قائمة المخزون';
	@override String get add => 'اضافة مخزون';
	@override String get edit => 'تعديل مخزون';
}

// Path: inventory
class _TranslationsInventoryAr extends TranslationsInventoryEn {
	_TranslationsInventoryAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get name => 'الاسم';
	@override String get currentQuantity => 'الكمية الحالية';
	@override String get unit => 'الوحدة';
	@override String get quantity => 'الكمية';
	@override String get warningQuantity => 'كمية التحذير';
}

// Path: insuranceView
class _TranslationsInsuranceViewAr extends TranslationsInsuranceViewEn {
	_TranslationsInsuranceViewAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get edit => 'تعديل التأمين';
	@override String get add => 'اضافة تأمين';
	@override String get delete => 'حذف التأمين';
	@override String get claims => 'المطالبات';
}

// Path: insuranceClaim
class _TranslationsInsuranceClaimAr extends TranslationsInsuranceClaimEn {
	_TranslationsInsuranceClaimAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get date => 'التاريخ';
	@override String get patient => 'المريض';
	@override String get company => 'الشركة';
}

// Path: insuranceClaimView
class _TranslationsInsuranceClaimViewAr extends TranslationsInsuranceClaimViewEn {
	_TranslationsInsuranceClaimViewAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get edit => 'تعديل المطالبة';
	@override String get add => 'اضافة مطالبة';
	@override String get delete => 'حذف المطالبة';
}

// Path: insuranceClaimForm
class _TranslationsInsuranceClaimFormAr extends TranslationsInsuranceClaimFormEn {
	_TranslationsInsuranceClaimFormAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get treatments => 'العلاجات';
	@override String get status => 'الحالة';
}

// Path: insuranceCompany
class _TranslationsInsuranceCompanyAr extends TranslationsInsuranceCompanyEn {
	_TranslationsInsuranceCompanyAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get name => 'الاسم';
	@override String get defaultCoverage => 'التغطية الافتراضية';
}

// Path: insuranceCompanyView
class _TranslationsInsuranceCompanyViewAr extends TranslationsInsuranceCompanyViewEn {
	_TranslationsInsuranceCompanyViewAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get edit => 'تعديل شركة التأمين';
	@override String get add => 'اضافة شركة تأمين';
	@override String get delete => 'حذف شركة التأمين';
}

// Path: custom_payment_methods
class _TranslationsCustomPaymentMethodsAr extends TranslationsCustomPaymentMethodsEn {
	_TranslationsCustomPaymentMethodsAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get title => 'طرق دفع';
	@override String get add => 'اضافة طريقة دفع';
	@override String get edit => 'تعديل طريقة دفع';
	@override String get delete => 'حذف طريقة دفع';
	@override String get name => 'الاسم';
	@override String get percentage => 'النسبة';
	@override String get noCustomPaymentMethods => 'لا يوجد طرق دفع مخصصة';
}

// Path: settings
class _TranslationsSettingsAr extends TranslationsSettingsEn {
	_TranslationsSettingsAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get settings => 'الاعدادات';
	@override String get changeLanguage => 'تغيير اللغة';
	@override String get noPhoneNumber => 'لا يوجد رقم هاتف';
	@override String get editClinicSettings => 'تعديل اعدادات العيادة';
	@override String get changePassword => 'تغيير كلمة المرور';
	@override String get logout => 'تسجيل الخروج';
	@override String get billing => 'الفواتير';
	@override String get totalVisits => 'اجمالي الزيارات';
	@override String get pricePerVisit => 'السعر للزيارة';
	@override String get totalPrice => 'السعر الكلي';
}

// Path: lab
class _TranslationsLabAr extends TranslationsLabEn {
	_TranslationsLabAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get name => 'الاسم';
	@override String get phone => 'الهاتف';
}

// Path: labView
class _TranslationsLabViewAr extends TranslationsLabViewEn {
	_TranslationsLabViewAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get labs => 'المختبرات';
	@override String get edit => 'تعديل مختبر';
	@override String get add => 'اضافة مختبر';
}

// Path: labForm
class _TranslationsLabFormAr extends TranslationsLabFormEn {
	_TranslationsLabFormAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get edit => 'تعديل مختبر';
	@override String get add => 'اضافة مختبر';
}

// Path: labRequestView
class _TranslationsLabRequestViewAr extends TranslationsLabRequestViewEn {
	_TranslationsLabRequestViewAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get edit => 'تعديل طلب مختبر';
	@override String get add => 'اضافة طلب مختبر';
	@override String get delete => 'حذف طلب مختبر';
	@override String get requests => 'طلبات المختبر';
}

// Path: labRequestForm
class _TranslationsLabRequestFormAr extends TranslationsLabRequestFormEn {
	_TranslationsLabRequestFormAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get generalInfo => 'معلومات عامة';
	@override String get details => 'التفاصيل';
}

// Path: labRequest
class _TranslationsLabRequestAr extends TranslationsLabRequestEn {
	_TranslationsLabRequestAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get misc => 'متفرقات';
	@override String get price => 'السعر';
	@override String get notes => 'الملاحظات';
	@override String get nextStep => 'الخطوة القادمة';
	@override String get ponticDesign => 'تصميم البونتيك';
	@override String get implantWork => 'عمل الزرع';
	@override String get zro2 => 'ZrO2';
	@override String get threeDPrinting => 'طباعة ثلاثية الابعاد';
	@override String get fullArchImplant => 'زرع القوس الكامل';
	@override String get emax => 'Emax';
	@override String get customAbutment => 'المسمار المخصص';
	@override String get pmma => 'PMMA';
	@override String get patientId => 'رقم المريض';
	@override String get sendingBranch => 'الفرع المرسل';
	@override String get receivingBranch => 'الفرع المستلم';
	@override String get clinicLab => 'العيادة/المختبر';
	@override String get expectedDeliveryDate => 'تاريخ التسليم المتوقع';
	@override String get sendDate => 'تاريخ الارسال';
	@override String get actualDeliveryDate => 'تاريخ التسليم الفعلي';
	@override String get shadeDetails => 'تفاصيل الظل';
}

// Path: overduePatients
class _TranslationsOverduePatientsAr extends TranslationsOverduePatientsEn {
	_TranslationsOverduePatientsAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get title => 'المرضى المتأخرين';
	@override String get noOverduePatients => 'لا يوجد مرضى متأخرين';
}

// Path: patientGroups
class _TranslationsPatientGroupsAr extends TranslationsPatientGroupsEn {
	_TranslationsPatientGroupsAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get name => 'الأسم';
	@override String get nameInList => 'مصنف ك';
	@override String get newGroup => 'مجموعة جديدة';
	@override String get title => 'المجموعات';
	@override String get singleTitle => 'المجموعات';
}

// Path: patientForm.reachChannels
class _TranslationsPatientFormReachChannelsAr extends TranslationsPatientFormReachChannelsEn {
	_TranslationsPatientFormReachChannelsAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get facebook => 'فيسبوك';
	@override String get instagram => 'إنستجرام';
	@override String get twitter => 'تويتر';
	@override String get google => 'جوجل';
	@override String get wordOfMouth => 'التوصية';
	@override String get other => 'أخرى';
}

// Path: users.roles
class _TranslationsUsersRolesAr extends TranslationsUsersRolesEn {
	_TranslationsUsersRolesAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get master => 'مدير';
	@override String get admin => 'مشرف';
	@override String get doctor => 'دكتور';
	@override String get secretary => 'سكرتير';
	@override String get basic => 'بسيط';
	@override String get external => 'خارجي';
}

// Path: expenseForm.types
class _TranslationsExpenseFormTypesAr extends TranslationsExpenseFormTypesEn {
	_TranslationsExpenseFormTypesAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get salary => 'مرتبات';
	@override String get materials => 'مواد';
	@override String get fees => 'رسوم';
	@override String get others => 'اخرى';
}

// Path: appointment.cancel
class _TranslationsAppointmentCancelAr extends TranslationsAppointmentCancelEn {
	_TranslationsAppointmentCancelAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get title => 'الغاء الموعد';
	@override String message({required Object patientName}) => 'هل انت متأكد من الغاء الموعد؟ ${patientName}';
}

// Path: payment.types
class _TranslationsPaymentTypesAr extends TranslationsPaymentTypesEn {
	_TranslationsPaymentTypesAr._(TranslationsAr root) : this._root = root, super.internal(root);

	final TranslationsAr _root; // ignore: unused_field

	// Translations
	@override String get cash => 'كاش';
	@override String get valu => 'فاليو';
	@override String get card => 'بطاقة';
	@override String get custom => 'مخصص';
}

/// Flat map(s) containing all translations.
/// Only for edge cases! For simple maps, use the map function of this library.
extension on TranslationsAr {
	dynamic _flatMapFunction(String path) {
		switch (path) {
			case 'skip': return 'تخطي';
			case 'delete': return 'حذف';
			case 'search': return 'بحث';
			case 'none': return 'لا شيء';
			case 'yes': return 'نعم';
			case 'no': return 'لا';
			case 'noData': return 'لا يوجد بيانات';
			case 'edit': return 'تعديل';
			case 'finish': return 'انهاء';
			case 'error': return 'خطأ';
			case 'blankFields': return 'الرجاء ملئ الحقول الفارغة';
			case 'pleaseFillRequiredFields': return ({required Object field}) => 'الرجاء ملئ الحقول المطلوبة ${field}';
			case 'createdAt': return 'تاريخ الانشاء';
			case 'subEnded': return 'انتهى الاشتراك الفرعي';
			case 'switchView': return 'تبديل العرض';
			case 'from': return 'من';
			case 'to': return 'الى';
			case 'cancel': return 'الغاء';
			case 'confirm': return 'تأكيد';
			case 'english': return 'الانجليزية';
			case 'arabic': return 'العربية';
			case 'locales.en': return 'English';
			case 'locales.ar': return 'العربية';
			case 'buttonTxt.add': return 'اضافة';
			case 'buttonTxt.save': return 'حفظ';
			case 'buttonTxt.cancel': return 'الغاء';
			case 'clinic.name': return 'اسم العيادة';
			case 'clinic.language': return 'اللغة';
			case 'clinic.daysPrior': return 'عدد ايام الاشعارات القادمة';
			case 'formErrors.invalid': return 'غير صحيح';
			case 'formErrors.shouldBeNumber': return 'يجب ان يكون رقم';
			case 'login.clinicName': return 'اسم العيادة';
			case 'login.username': return 'اسم المستخدم';
			case 'login.password': return 'كلمة المرور';
			case 'login.login': return 'تسجيل الدخول';
			case 'nav.dashboard': return 'الرئيسية';
			case 'nav.data': return 'معلومات';
			case 'nav.administration': return 'التحكم';
			case 'nav.home': return 'الرئيسية';
			case 'nav.analysis': return 'تحليلات';
			case 'nav.insurance': return 'التأمين';
			case 'nav.patients': return 'المرضى';
			case 'nav.expeenses': return 'المصروفات';
			case 'nav.inventory': return 'المخزون';
			case 'nav.payments': return 'المدفوعات';
			case 'nav.specialities': return 'التخصصات';
			case 'nav.labs': return 'المختبرات';
			case 'nav.users': return 'المستخدمين';
			case 'nav.branches': return 'الفروع';
			case 'nav.communication': return 'التواصل';
			case 'analysis.title': return 'تحليلات';
			case 'analysis.visitsDiagram': return 'رسم بياني للزيارات';
			case 'analysis.visits': return 'الزيارات';
			case 'analysis.patients': return 'المرضى';
			case 'analysis.noData': return 'لا يوجد بيانات';
			case 'analysis.expenses': return ' المصروفات';
			case 'analysis.patientsReachChannel': return 'مصادر الوصول للمرضى';
			case 'analysis.unknown': return 'غير معروف';
			case 'branches.title': return 'الفروع';
			case 'branches.add': return 'اضافة فرع';
			case 'branches.edit': return 'تعديل فرع';
			case 'branches.name': return 'الاسم';
			case 'branches.selectColor': return 'اختر لون';
			case 'branches.googleMapURL': return 'رابط خرائط جوجل';
			case 'branches.clear': return 'مسح';
			case 'branches.googleLink': return 'رابط خرائط جوجل';
			case 'branches.mapView': return 'عرض الخريطة';
			case 'branches.roomsCount': return 'عدد الغرف';
			case 'branches.branchUpdated': return 'تم تعديل الفرع بنجاح';
			case 'branches.branchAdded': return 'تم اضافة الفرع بنجاح';
			case 'branches.branchDeleted': return 'تم حذف الفرع بنجاح';
			case 'branches.deleteBranch': return 'حذف الفرع';
			case 'branches.listBranches': return 'قائمة الفروع';
			case 'patientsList.title': return 'قائمة المرضى';
			case 'patientsList.add': return 'اضافة مريض';
			case 'patientsList.edit': return 'تعديل مريض';
			case 'patientForm.title': return 'اضافة مريض';
			case 'patientForm.name': return 'الاسم';
			case 'patientForm.email': return 'البريد الإلكتروني';
			case 'patientForm.phone': return 'رقم الهاتف';
			case 'patientForm.age': return 'السن';
			case 'patientForm.balance': return 'الرصيد';
			case 'patientForm.reachChannel': return 'مصدر الوصول';
			case 'patientForm.address': return 'العنوان';
			case 'patientForm.birthdate': return 'تاريخ الميلاد';
			case 'patientForm.job': return 'الوظيفة';
			case 'patientForm.fileNumber': return 'رقم الملف';
			case 'patientForm.dentalHistory': return 'تاريخ الاسنان';
			case 'patientForm.medicalHistory': return 'التاريخ الطبي';
			case 'patientForm.treatmentPlan': return 'خطة العلاج';
			case 'patientForm.reachChannels.facebook': return 'فيسبوك';
			case 'patientForm.reachChannels.instagram': return 'إنستجرام';
			case 'patientForm.reachChannels.twitter': return 'تويتر';
			case 'patientForm.reachChannels.google': return 'جوجل';
			case 'patientForm.reachChannels.wordOfMouth': return 'التوصية';
			case 'patientForm.reachChannels.other': return 'أخرى';
			case 'userForm.title': return 'اضافة مستخدم';
			case 'userForm.name': return 'الاسم';
			case 'userForm.username': return 'اسم المستخدم';
			case 'userForm.password': return 'كلمة المرور';
			case 'userForm.role': return 'الدور';
			case 'userForm.isDentist': return 'طبيب اسنان';
			case 'userForm.percentage': return 'النسبة';
			case 'users.list': return 'قائمة المستخدمين';
			case 'users.add': return 'اضافة مستخدم';
			case 'users.userUpdated': return 'تم تعديل المستخدم بنجاح';
			case 'users.userAdded': return 'تم اضافة المستخدم بنجاح';
			case 'users.userDeleted': return 'تم حذف المستخدم بنجاح';
			case 'users.confirmDelete': return 'هل انت متأكد من حذف المستخدم؟';
			case 'users.confirmDeleteUser': return 'هل انت متأكد من حذف المستخدم؟';
			case 'users.roles.master': return 'مدير';
			case 'users.roles.admin': return 'مشرف';
			case 'users.roles.doctor': return 'دكتور';
			case 'users.roles.secretary': return 'سكرتير';
			case 'users.roles.basic': return 'بسيط';
			case 'users.roles.external': return 'خارجي';
			case 'expenseForm.title': return 'اضافة مصروف';
			case 'expenseForm.name': return 'الاسم';
			case 'expenseForm.amount': return 'المبلغ';
			case 'expenseForm.description': return 'الوصف';
			case 'expenseForm.type': return 'النوع';
			case 'expenseForm.types.salary': return 'مرتبات';
			case 'expenseForm.types.materials': return 'مواد';
			case 'expenseForm.types.fees': return 'رسوم';
			case 'expenseForm.types.others': return 'اخرى';
			case 'expensesList.title': return 'قائمة المصروفات';
			case 'expensesList.add': return 'اضافة مصروف';
			case 'expensesList.edit': return 'تعديل مصروف';
			case 'inventoryTransactionForm.changeQuantity': return 'تغيير الكمية';
			case 'inventoryTransactionForm.label': return 'اكتب كمية المخزن الموجود حاليا';
			case 'inventoryTransactionForm.quantity': return 'الكمية';
			case 'patient.addNewPatient': return 'اضافة مريض جديد';
			case 'patient.askToCreatePatient': return ({required Object name}) => 'هل تريد اضافة مريض جديد؟ `${name}`';
			case 'appointment.addNewAppointment': return 'اضافة موعد جديد';
			case 'appointment.branch': return 'الفرع';
			case 'appointment.patient': return 'المريض';
			case 'appointment.dentist': return 'الطبيب';
			case 'appointment.room': return 'الغرفة';
			case 'appointment.roomNumber': return ({required Object number}) => 'رقم الغرفة ${number}';
			case 'appointment.startTime': return 'وقت البدء';
			case 'appointment.endTime': return 'وقت الانتهاء';
			case 'appointment.duration': return 'المدة';
			case 'appointment.from': return ({required Object time}) => 'من ${time}';
			case 'appointment.to': return ({required Object time}) => 'الى ${time}';
			case 'appointment.chooseFutureDate': return 'اختر تاريخ مستقبلي';
			case 'appointment.cancel.title': return 'الغاء الموعد';
			case 'appointment.cancel.message': return ({required Object patientName}) => 'هل انت متأكد من الغاء الموعد؟ ${patientName}';
			case 'patientProfile.appointments': return 'المواعيد';
			case 'patientProfile.insuranceClaims': return 'المطالبات';
			case 'patientProfile.visitsHistory': return 'تاريخ الزيارات';
			case 'patientProfile.visits': return 'الزيارات';
			case 'patientProfile.patientProfile': return 'ملف المريض';
			case 'patientProfile.edit': return 'تعديل';
			case 'patientProfile.switchTeethView': return 'تبديل عرض الاسنان';
			case 'patientProfile.paymentsList': return 'قائمة المدفوعات';
			case 'patientProfile.noTreatmentPlan': return 'لا يوجد خطة علاج';
			case 'patientProfile.payments': return 'المدفوعات';
			case 'patientProfile.about': return 'عن المريض';
			case 'paymentsView.list': return 'قائمة المدفوعات';
			case 'paymentsView.newPayment': return 'مدفوعات جديدة';
			case 'paymentsView.add': return 'اضافة مدفوعات';
			case 'paymentsView.edit': return 'تعديل مدفوعات';
			case 'paymentsView.paidAmount': return 'المبلغ المدفوع';
			case 'paymentsView.cancel': return 'ملغى';
			case 'paymentsView.cancelTitle': return 'الغاء المدفوعات';
			case 'paymentsView.cancelFormReason': return 'سبب الالغاء';
			case 'paymentsView.cancelFormValidation': return 'الرجاء ملئ سبب الالغاء';
			case 'paymentsView.cancelled': return 'ملغى';
			case 'payment.amount': return 'المبلغ';
			case 'payment.type': return 'النوع';
			case 'payment.branch': return 'الفرع';
			case 'payment.patient': return 'المريض';
			case 'payment.createdAt': return 'تاريخ الانشاء';
			case 'payment.createdBy': return 'انشئ بواسطة';
			case 'payment.cancelled': return 'ملغى';
			case 'payment.cancelledBy': return 'ملغى بواسطة';
			case 'payment.cancellationReason': return 'سبب الالغاء';
			case 'payment.types.cash': return 'كاش';
			case 'payment.types.valu': return 'فاليو';
			case 'payment.types.card': return 'بطاقة';
			case 'payment.types.custom': return 'مخصص';
			case 'profileVisitItem.discount': return 'خصم';
			case 'profileVisitItem.toothNumber': return 'رقم السن';
			case 'profileVisitItem.procedures': return 'الاجراءات';
			case 'profileVisitItem.noProcedures': return 'لا يوجد اجراءات';
			case 'profileVisitItem.diagnosis': return 'التشخيصات';
			case 'profileVisitItem.noDiagnosis': return 'لا يوجد تشخيصات';
			case 'profileVisitItem.treatments': return 'العلاجات';
			case 'profileVisitItem.noTreatments': return 'لا يوجد علاجات';
			case 'profileVisitItem.comment': return 'التعليق';
			case 'profileVisitItem.noComment': return 'لا يوجد تعليق';
			case 'specialitiesList.title': return 'قائمة التخصصات';
			case 'specialitiesList.add': return 'اضافة تخصص';
			case 'specialitiesList.edit': return 'تعديل تخصص';
			case 'speciality.name': return 'الاسم';
			case 'procedureTemplate.procedures': return 'الاجراءات';
			case 'procedureTemplate.editProcedure': return 'تعديل الاجراءات';
			case 'procedureTemplate.addProcedure': return 'اضافة اجراءات';
			case 'procedureTemplate.name': return 'الاسم';
			case 'procedureTemplate.price': return 'السعر';
			case 'procedureTemplate.teethRemoved': return 'الاسنان المستأصلة';
			case 'procedureTemplate.crown': return 'تاج';
			case 'procedureTemplate.endo': return 'علاج عصب';
			case 'procedureTemplate.implant': return 'زرع';
			case 'procedureTemplate.operative': return 'ترميم';
			case 'procedureTemplate.speciality': return 'التخصص';
			case 'procedureTemplateForm.saveAndAddOther': return 'حفظ واضافة اخرى';
			case 'visitForm.createVisit': return 'انشاء زيارة';
			case 'visitForm.viewPatientProfile': return 'عرض ملف المريض';
			case 'visitForm.noProcedures': return 'لا يوجد اجراءات';
			case 'visitForm.noFiles': return 'لا يوجد ملفات';
			case 'visit.comments': return 'التعليقات';
			case 'visit.diagnosis': return 'التشخيصات';
			case 'visit.nextVisit': return 'الزيارة القادمة';
			case 'visit.treatments': return 'العلاجات';
			case 'visit.procedures': return 'الاجراءات';
			case 'visit.files': return 'الملفات';
			case 'visitProcedure.procedure': return 'الاجراء';
			case 'visitProcedure.price': return 'السعر';
			case 'visitProcedure.discount': return 'التخفيض';
			case 'visitProcedure.total': return 'الاجمالي';
			case 'visitProcedure.teethRemoved': return 'الاسنان المستأصلة';
			case 'visitProcedure.crown': return 'تاج';
			case 'visitProcedure.endo': return 'علاج عصب';
			case 'visitProcedure.implant': return 'زرع';
			case 'visitProcedure.operative': return 'ترميم';
			case 'visitProcedure.speciality': return 'التخصص';
			case 'visitProcedure.dentist': return 'الاسنان';
			case 'visitProcedure.notes': return 'الملاحظات';
			case 'visitProcedure.nextVisit': return 'الزيارة القادمة';
			case 'visitProcedureForm.create': return 'انشاء اجراء';
			case 'visitProcedureForm.price': return 'السعر';
			case 'visitProcedureForm.discount': return 'التخفيض';
			case 'visitProcedureForm.total': return 'الاجمالي';
			case 'visitProcedureForm.teethRemoved': return 'الاسنان المستأصلة';
			case 'visitProcedureForm.crown': return 'تاج';
			case 'visitProcedureForm.endo': return 'علاج عصب';
			case 'visitProcedureForm.implant': return 'زرع';
			case 'visitProcedureForm.operative': return 'ترميم';
			case 'visitProcedureForm.speciality': return 'التخصص';
			case 'visitProcedureForm.affectedTeeth': return 'الاسنان المتأثرة';
			case 'visitProcedureForm.chooseTeeth': return 'اختر الاسنان';
			case 'visitProcedureForm.chooseSpecialityAndProcedure': return 'اختر التخصص والاجراء';
			case 'inventoryView.list': return 'قائمة المخزون';
			case 'inventoryView.add': return 'اضافة مخزون';
			case 'inventoryView.edit': return 'تعديل مخزون';
			case 'inventory.name': return 'الاسم';
			case 'inventory.currentQuantity': return 'الكمية الحالية';
			case 'inventory.unit': return 'الوحدة';
			case 'inventory.quantity': return 'الكمية';
			case 'inventory.warningQuantity': return 'كمية التحذير';
			case 'insuranceView.edit': return 'تعديل التأمين';
			case 'insuranceView.add': return 'اضافة تأمين';
			case 'insuranceView.delete': return 'حذف التأمين';
			case 'insuranceView.claims': return 'المطالبات';
			case 'insuranceClaim.date': return 'التاريخ';
			case 'insuranceClaim.patient': return 'المريض';
			case 'insuranceClaim.company': return 'الشركة';
			case 'insuranceClaimView.edit': return 'تعديل المطالبة';
			case 'insuranceClaimView.add': return 'اضافة مطالبة';
			case 'insuranceClaimView.delete': return 'حذف المطالبة';
			case 'insuranceClaimForm.treatments': return 'العلاجات';
			case 'insuranceClaimForm.status': return 'الحالة';
			case 'insuranceCompany.name': return 'الاسم';
			case 'insuranceCompany.defaultCoverage': return 'التغطية الافتراضية';
			case 'insuranceCompanyView.edit': return 'تعديل شركة التأمين';
			case 'insuranceCompanyView.add': return 'اضافة شركة تأمين';
			case 'insuranceCompanyView.delete': return 'حذف شركة التأمين';
			case 'custom_payment_methods.title': return 'طرق دفع';
			case 'custom_payment_methods.add': return 'اضافة طريقة دفع';
			case 'custom_payment_methods.edit': return 'تعديل طريقة دفع';
			case 'custom_payment_methods.delete': return 'حذف طريقة دفع';
			case 'custom_payment_methods.name': return 'الاسم';
			case 'custom_payment_methods.percentage': return 'النسبة';
			case 'custom_payment_methods.noCustomPaymentMethods': return 'لا يوجد طرق دفع مخصصة';
			case 'settings.settings': return 'الاعدادات';
			case 'settings.changeLanguage': return 'تغيير اللغة';
			case 'settings.noPhoneNumber': return 'لا يوجد رقم هاتف';
			case 'settings.editClinicSettings': return 'تعديل اعدادات العيادة';
			case 'settings.changePassword': return 'تغيير كلمة المرور';
			case 'settings.logout': return 'تسجيل الخروج';
			case 'settings.billing': return 'الفواتير';
			case 'settings.totalVisits': return 'اجمالي الزيارات';
			case 'settings.pricePerVisit': return 'السعر للزيارة';
			case 'settings.totalPrice': return 'السعر الكلي';
			case 'lab.name': return 'الاسم';
			case 'lab.phone': return 'الهاتف';
			case 'labView.labs': return 'المختبرات';
			case 'labView.edit': return 'تعديل مختبر';
			case 'labView.add': return 'اضافة مختبر';
			case 'labForm.edit': return 'تعديل مختبر';
			case 'labForm.add': return 'اضافة مختبر';
			case 'labRequestView.edit': return 'تعديل طلب مختبر';
			case 'labRequestView.add': return 'اضافة طلب مختبر';
			case 'labRequestView.delete': return 'حذف طلب مختبر';
			case 'labRequestView.requests': return 'طلبات المختبر';
			case 'labRequestForm.generalInfo': return 'معلومات عامة';
			case 'labRequestForm.details': return 'التفاصيل';
			case 'labRequest.misc': return 'متفرقات';
			case 'labRequest.price': return 'السعر';
			case 'labRequest.notes': return 'الملاحظات';
			case 'labRequest.nextStep': return 'الخطوة القادمة';
			case 'labRequest.ponticDesign': return 'تصميم البونتيك';
			case 'labRequest.implantWork': return 'عمل الزرع';
			case 'labRequest.zro2': return 'ZrO2';
			case 'labRequest.threeDPrinting': return 'طباعة ثلاثية الابعاد';
			case 'labRequest.fullArchImplant': return 'زرع القوس الكامل';
			case 'labRequest.emax': return 'Emax';
			case 'labRequest.customAbutment': return 'المسمار المخصص';
			case 'labRequest.pmma': return 'PMMA';
			case 'labRequest.patientId': return 'رقم المريض';
			case 'labRequest.sendingBranch': return 'الفرع المرسل';
			case 'labRequest.receivingBranch': return 'الفرع المستلم';
			case 'labRequest.clinicLab': return 'العيادة/المختبر';
			case 'labRequest.expectedDeliveryDate': return 'تاريخ التسليم المتوقع';
			case 'labRequest.sendDate': return 'تاريخ الارسال';
			case 'labRequest.actualDeliveryDate': return 'تاريخ التسليم الفعلي';
			case 'labRequest.shadeDetails': return 'تفاصيل الظل';
			case 'overduePatients.title': return 'المرضى المتأخرين';
			case 'overduePatients.noOverduePatients': return 'لا يوجد مرضى متأخرين';
			case 'patientGroups.name': return 'الأسم';
			case 'patientGroups.nameInList': return 'مصنف ك';
			case 'patientGroups.newGroup': return 'مجموعة جديدة';
			case 'patientGroups.title': return 'المجموعات';
			case 'patientGroups.singleTitle': return 'المجموعات';
			default: return null;
		}
	}
}

