///
/// Generated file. Do not edit.
///
// coverage:ignore-file
// ignore_for_file: type=lint, unused_import

part of 'translations.g.dart';

// Path: <root>
typedef TranslationsEn = Translations; // ignore: unused_element
class Translations implements BaseTranslations<AppLocale, Translations> {
	/// Returns the current translations of the given [context].
	///
	/// Usage:
	/// final t = Translations.of(context);
	static Translations of(BuildContext context) => InheritedLocaleData.of<AppLocale, Translations>(context).translations;

	/// You can call this constructor and build your own translation instance of this locale.
	/// Constructing via the enum [AppLocale.build] is preferred.
	Translations({Map<String, Node>? overrides, PluralResolver? cardinalResolver, PluralResolver? ordinalResolver, TranslationMetadata<AppLocale, Translations>? meta})
		: assert(overrides == null, 'Set "translation_overrides: true" in order to enable this feature.'),
		  $meta = meta ?? TranslationMetadata(
		    locale: AppLocale.en,
		    overrides: overrides ?? {},
		    cardinalResolver: cardinalResolver,
		    ordinalResolver: ordinalResolver,
		  ) {
		$meta.setFlatMapFunction(_flatMapFunction);
	}

	/// Metadata for the translations of <en>.
	@override final TranslationMetadata<AppLocale, Translations> $meta;

	/// Access flat map
	dynamic operator[](String key) => $meta.getTranslation(key);

	late final Translations _root = this; // ignore: unused_field

	Translations $copyWith({TranslationMetadata<AppLocale, Translations>? meta}) => Translations(meta: meta ?? this.$meta);

	// Translations

	/// en: 'Skip'
	String get skip => 'Skip';

	/// en: 'Delete'
	String get delete => 'Delete';

	/// en: 'Search'
	String get search => 'Search';

	/// en: 'None'
	String get none => 'None';

	/// en: 'Yes'
	String get yes => 'Yes';

	/// en: 'No'
	String get no => 'No';

	/// en: 'No Data'
	String get noData => 'No Data';

	/// en: 'Edit'
	String get edit => 'Edit';

	/// en: 'Finish'
	String get finish => 'Finish';

	/// en: 'Error'
	String get error => 'Error';

	/// en: 'Blank Fields'
	String get blankFields => 'Blank Fields';

	/// en: 'Please fill the required fields $field'
	String pleaseFillRequiredFields({required Object field}) => 'Please fill the required fields ${field}';

	/// en: 'Created At'
	String get createdAt => 'Created At';

	/// en: 'Your Subscription Ended'
	String get subEnded => 'Your Subscription Ended';

	/// en: 'Switch View'
	String get switchView => 'Switch View';

	/// en: 'From'
	String get from => 'From';

	/// en: 'To'
	String get to => 'To';

	/// en: 'Cancel'
	String get cancel => 'Cancel';

	/// en: 'Confirm'
	String get confirm => 'Confirm';

	/// en: 'English'
	String get english => 'English';

	/// en: 'Arabic'
	String get arabic => 'Arabic';

	Map<String, String> get locales => {
		'en': 'English',
		'ar': 'Arabic',
	};
	late final TranslationsButtonTxtEn buttonTxt = TranslationsButtonTxtEn.internal(_root);
	late final TranslationsClinicEn clinic = TranslationsClinicEn.internal(_root);
	late final TranslationsFormErrorsEn formErrors = TranslationsFormErrorsEn.internal(_root);
	late final TranslationsLoginEn login = TranslationsLoginEn.internal(_root);
	late final TranslationsNavEn nav = TranslationsNavEn.internal(_root);
	late final TranslationsAnalysisEn analysis = TranslationsAnalysisEn.internal(_root);
	late final TranslationsBranchesEn branches = TranslationsBranchesEn.internal(_root);
	late final TranslationsPatientsListEn patientsList = TranslationsPatientsListEn.internal(_root);
	late final TranslationsPatientFormEn patientForm = TranslationsPatientFormEn.internal(_root);
	late final TranslationsUserFormEn userForm = TranslationsUserFormEn.internal(_root);
	late final TranslationsUsersEn users = TranslationsUsersEn.internal(_root);
	late final TranslationsExpenseFormEn expenseForm = TranslationsExpenseFormEn.internal(_root);
	late final TranslationsExpensesListEn expensesList = TranslationsExpensesListEn.internal(_root);
	late final TranslationsInventoryTransactionFormEn inventoryTransactionForm = TranslationsInventoryTransactionFormEn.internal(_root);
	late final TranslationsPatientEn patient = TranslationsPatientEn.internal(_root);
	late final TranslationsAppointmentEn appointment = TranslationsAppointmentEn.internal(_root);
	late final TranslationsPatientProfileEn patientProfile = TranslationsPatientProfileEn.internal(_root);
	late final TranslationsPaymentsViewEn paymentsView = TranslationsPaymentsViewEn.internal(_root);
	late final TranslationsPaymentEn payment = TranslationsPaymentEn.internal(_root);
	late final TranslationsProfileVisitItemEn profileVisitItem = TranslationsProfileVisitItemEn.internal(_root);
	late final TranslationsSpecialitiesListEn specialitiesList = TranslationsSpecialitiesListEn.internal(_root);
	late final TranslationsSpecialityEn speciality = TranslationsSpecialityEn.internal(_root);
	late final TranslationsProcedureTemplateEn procedureTemplate = TranslationsProcedureTemplateEn.internal(_root);
	late final TranslationsProcedureTemplateFormEn procedureTemplateForm = TranslationsProcedureTemplateFormEn.internal(_root);
	late final TranslationsVisitFormEn visitForm = TranslationsVisitFormEn.internal(_root);
	late final TranslationsVisitEn visit = TranslationsVisitEn.internal(_root);
	late final TranslationsVisitProcedureEn visitProcedure = TranslationsVisitProcedureEn.internal(_root);
	late final TranslationsVisitProcedureFormEn visitProcedureForm = TranslationsVisitProcedureFormEn.internal(_root);
	late final TranslationsInventoryViewEn inventoryView = TranslationsInventoryViewEn.internal(_root);
	late final TranslationsInventoryEn inventory = TranslationsInventoryEn.internal(_root);
	late final TranslationsInsuranceViewEn insuranceView = TranslationsInsuranceViewEn.internal(_root);
	late final TranslationsInsuranceClaimEn insuranceClaim = TranslationsInsuranceClaimEn.internal(_root);
	late final TranslationsInsuranceClaimViewEn insuranceClaimView = TranslationsInsuranceClaimViewEn.internal(_root);
	late final TranslationsInsuranceClaimFormEn insuranceClaimForm = TranslationsInsuranceClaimFormEn.internal(_root);
	late final TranslationsInsuranceCompanyEn insuranceCompany = TranslationsInsuranceCompanyEn.internal(_root);
	late final TranslationsInsuranceCompanyViewEn insuranceCompanyView = TranslationsInsuranceCompanyViewEn.internal(_root);
	late final TranslationsCustomPaymentMethodsEn custom_payment_methods = TranslationsCustomPaymentMethodsEn.internal(_root);
	late final TranslationsSettingsEn settings = TranslationsSettingsEn.internal(_root);
	late final TranslationsLabEn lab = TranslationsLabEn.internal(_root);
	late final TranslationsLabViewEn labView = TranslationsLabViewEn.internal(_root);
	late final TranslationsLabFormEn labForm = TranslationsLabFormEn.internal(_root);
	late final TranslationsLabRequestViewEn labRequestView = TranslationsLabRequestViewEn.internal(_root);
	late final TranslationsLabRequestFormEn labRequestForm = TranslationsLabRequestFormEn.internal(_root);
	late final TranslationsLabRequestEn labRequest = TranslationsLabRequestEn.internal(_root);
	late final TranslationsOverduePatientsEn overduePatients = TranslationsOverduePatientsEn.internal(_root);
	late final TranslationsPatientGroupsEn patientGroups = TranslationsPatientGroupsEn.internal(_root);
}

// Path: buttonTxt
class TranslationsButtonTxtEn {
	TranslationsButtonTxtEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'Add'
	String get add => 'Add';

	/// en: 'Save'
	String get save => 'Save';

	/// en: 'Cancel'
	String get cancel => 'Cancel';
}

// Path: clinic
class TranslationsClinicEn {
	TranslationsClinicEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'Clinic Name'
	String get name => 'Clinic Name';

	/// en: 'Languange'
	String get language => 'Languange';

	/// en: 'Days Prior'
	String get daysPrior => 'Days Prior';
}

// Path: formErrors
class TranslationsFormErrorsEn {
	TranslationsFormErrorsEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'Invalid Input'
	String get invalid => 'Invalid Input';

	/// en: 'You must type a number'
	String get shouldBeNumber => 'You must type a number';
}

// Path: login
class TranslationsLoginEn {
	TranslationsLoginEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'Clinic Name'
	String get clinicName => 'Clinic Name';

	/// en: 'Username'
	String get username => 'Username';

	/// en: 'Password'
	String get password => 'Password';

	/// en: 'Login'
	String get login => 'Login';
}

// Path: nav
class TranslationsNavEn {
	TranslationsNavEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'Dashboard'
	String get dashboard => 'Dashboard';

	/// en: 'Data'
	String get data => 'Data';

	/// en: 'Administration'
	String get administration => 'Administration';

	/// en: 'Home'
	String get home => 'Home';

	/// en: 'Analysis'
	String get analysis => 'Analysis';

	/// en: 'Insurance'
	String get insurance => 'Insurance';

	/// en: 'Patients'
	String get patients => 'Patients';

	/// en: 'Expenses'
	String get expeenses => 'Expenses';

	/// en: 'Inventory'
	String get inventory => 'Inventory';

	/// en: 'Payments'
	String get payments => 'Payments';

	/// en: 'Specialities'
	String get specialities => 'Specialities';

	/// en: 'Labs'
	String get labs => 'Labs';

	/// en: 'Users'
	String get users => 'Users';

	/// en: 'Branches'
	String get branches => 'Branches';

	/// en: 'Communication'
	String get communication => 'Communication';
}

// Path: analysis
class TranslationsAnalysisEn {
	TranslationsAnalysisEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'Analysis'
	String get title => 'Analysis';

	/// en: 'Visits Diagram'
	String get visitsDiagram => 'Visits Diagram';

	/// en: 'Visits'
	String get visits => 'Visits';

	/// en: 'Patients'
	String get patients => 'Patients';

	/// en: 'No Data'
	String get noData => 'No Data';

	/// en: 'Expenses'
	String get expenses => 'Expenses';

	/// en: 'Patients Reach Channel'
	String get patientsReachChannel => 'Patients Reach Channel';

	/// en: 'Unknown'
	String get unknown => 'Unknown';
}

// Path: branches
class TranslationsBranchesEn {
	TranslationsBranchesEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'Branches'
	String get title => 'Branches';

	/// en: 'Add Branch'
	String get add => 'Add Branch';

	/// en: 'Edit Branch'
	String get edit => 'Edit Branch';

	/// en: 'Name'
	String get name => 'Name';

	/// en: 'Select Color'
	String get selectColor => 'Select Color';

	/// en: 'Google Map URL'
	String get googleMapURL => 'Google Map URL';

	/// en: 'Clear'
	String get clear => 'Clear';

	/// en: 'Google Link'
	String get googleLink => 'Google Link';

	/// en: 'Map View'
	String get mapView => 'Map View';

	/// en: 'Rooms Count'
	String get roomsCount => 'Rooms Count';

	/// en: 'Branch Updated Successfully'
	String get branchUpdated => 'Branch Updated Successfully';

	/// en: 'Branch Added Successfully'
	String get branchAdded => 'Branch Added Successfully';

	/// en: 'Branch Deleted Successfully'
	String get branchDeleted => 'Branch Deleted Successfully';

	/// en: 'Delete Branch'
	String get deleteBranch => 'Delete Branch';

	/// en: 'List Branches'
	String get listBranches => 'List Branches';
}

// Path: patientsList
class TranslationsPatientsListEn {
	TranslationsPatientsListEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'Patients List'
	String get title => 'Patients List';

	/// en: 'Add Patient'
	String get add => 'Add Patient';

	/// en: 'Edit Patient'
	String get edit => 'Edit Patient';
}

// Path: patientForm
class TranslationsPatientFormEn {
	TranslationsPatientFormEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'Patient Form'
	String get title => 'Patient Form';

	/// en: 'Name'
	String get name => 'Name';

	/// en: 'Email'
	String get email => 'Email';

	/// en: 'Phone'
	String get phone => 'Phone';

	/// en: 'Age'
	String get age => 'Age';

	/// en: 'Balance'
	String get balance => 'Balance';

	/// en: 'Reach Channel'
	String get reachChannel => 'Reach Channel';

	/// en: 'Address'
	String get address => 'Address';

	/// en: 'Birthdate'
	String get birthdate => 'Birthdate';

	/// en: 'Job'
	String get job => 'Job';

	/// en: 'File Number'
	String get fileNumber => 'File Number';

	/// en: 'Dental History'
	String get dentalHistory => 'Dental History';

	/// en: 'Medical History'
	String get medicalHistory => 'Medical History';

	/// en: 'Treatment Plan'
	String get treatmentPlan => 'Treatment Plan';

	late final TranslationsPatientFormReachChannelsEn reachChannels = TranslationsPatientFormReachChannelsEn.internal(_root);
}

// Path: userForm
class TranslationsUserFormEn {
	TranslationsUserFormEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'User Form'
	String get title => 'User Form';

	/// en: 'Name'
	String get name => 'Name';

	/// en: 'Username'
	String get username => 'Username';

	/// en: 'Password'
	String get password => 'Password';

	/// en: 'Role'
	String get role => 'Role';

	/// en: 'Is Dentist'
	String get isDentist => 'Is Dentist';

	/// en: 'Percentage'
	String get percentage => 'Percentage';
}

// Path: users
class TranslationsUsersEn {
	TranslationsUsersEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'Users List'
	String get list => 'Users List';

	/// en: 'Add User'
	String get add => 'Add User';

	/// en: 'User Updated Successfully'
	String get userUpdated => 'User Updated Successfully';

	/// en: 'User Added Successfully'
	String get userAdded => 'User Added Successfully';

	/// en: 'User Deleted Successfully'
	String get userDeleted => 'User Deleted Successfully';

	/// en: 'Confirm Delete'
	String get confirmDelete => 'Confirm Delete';

	/// en: 'Are you sure you want to delete user?'
	String get confirmDeleteUser => 'Are you sure you want to delete user?';

	late final TranslationsUsersRolesEn roles = TranslationsUsersRolesEn.internal(_root);
}

// Path: expenseForm
class TranslationsExpenseFormEn {
	TranslationsExpenseFormEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'Expense Form'
	String get title => 'Expense Form';

	/// en: 'Name'
	String get name => 'Name';

	/// en: 'Amount'
	String get amount => 'Amount';

	/// en: 'Description'
	String get description => 'Description';

	/// en: 'Type'
	String get type => 'Type';

	late final TranslationsExpenseFormTypesEn types = TranslationsExpenseFormTypesEn.internal(_root);
}

// Path: expensesList
class TranslationsExpensesListEn {
	TranslationsExpensesListEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'Expenses List'
	String get title => 'Expenses List';

	/// en: 'Add Expense'
	String get add => 'Add Expense';

	/// en: 'Edit Expense'
	String get edit => 'Edit Expense';
}

// Path: inventoryTransactionForm
class TranslationsInventoryTransactionFormEn {
	TranslationsInventoryTransactionFormEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'Change Quantity'
	String get changeQuantity => 'Change Quantity';

	/// en: 'Put the current inventory quantity'
	String get label => 'Put the current inventory quantity';

	/// en: 'Quantity'
	String get quantity => 'Quantity';
}

// Path: patient
class TranslationsPatientEn {
	TranslationsPatientEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'Add New Patient'
	String get addNewPatient => 'Add New Patient';

	/// en: 'Do you want to create a new patient with name: `$name`?'
	String askToCreatePatient({required Object name}) => 'Do you want to create a new patient with name: `${name}`?';
}

// Path: appointment
class TranslationsAppointmentEn {
	TranslationsAppointmentEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'New Appointment'
	String get addNewAppointment => 'New Appointment';

	/// en: 'Branch'
	String get branch => 'Branch';

	/// en: 'Patient'
	String get patient => 'Patient';

	/// en: 'Dentist'
	String get dentist => 'Dentist';

	/// en: 'Room'
	String get room => 'Room';

	/// en: 'Room $number'
	String roomNumber({required Object number}) => 'Room ${number}';

	/// en: 'Start Time'
	String get startTime => 'Start Time';

	/// en: 'End Time'
	String get endTime => 'End Time';

	/// en: 'Duration'
	String get duration => 'Duration';

	/// en: 'From $time'
	String from({required Object time}) => 'From ${time}';

	/// en: 'To $time'
	String to({required Object time}) => 'To ${time}';

	/// en: 'You should select a day after today'
	String get chooseFutureDate => 'You should select a day after today';

	late final TranslationsAppointmentCancelEn cancel = TranslationsAppointmentCancelEn.internal(_root);
}

// Path: patientProfile
class TranslationsPatientProfileEn {
	TranslationsPatientProfileEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'Appointments'
	String get appointments => 'Appointments';

	/// en: 'Insurance Claims'
	String get insuranceClaims => 'Insurance Claims';

	/// en: 'Visits History'
	String get visitsHistory => 'Visits History';

	/// en: 'Visits'
	String get visits => 'Visits';

	/// en: 'Patient Profile'
	String get patientProfile => 'Patient Profile';

	/// en: 'Edit Profile'
	String get edit => 'Edit Profile';

	/// en: 'Switch Teeth View'
	String get switchTeethView => 'Switch Teeth View';

	/// en: 'Payments List'
	String get paymentsList => 'Payments List';

	/// en: 'No Treatment Plan'
	String get noTreatmentPlan => 'No Treatment Plan';

	/// en: 'Payments'
	String get payments => 'Payments';

	/// en: 'About'
	String get about => 'About';
}

// Path: paymentsView
class TranslationsPaymentsViewEn {
	TranslationsPaymentsViewEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'Payments List'
	String get list => 'Payments List';

	/// en: 'New Payment'
	String get newPayment => 'New Payment';

	/// en: 'Add Payment'
	String get add => 'Add Payment';

	/// en: 'Edit Payment'
	String get edit => 'Edit Payment';

	/// en: 'Paid Amount'
	String get paidAmount => 'Paid Amount';

	/// en: 'Cancel'
	String get cancel => 'Cancel';

	/// en: 'Cancel Payment'
	String get cancelTitle => 'Cancel Payment';

	/// en: 'Cancellation Reason'
	String get cancelFormReason => 'Cancellation Reason';

	/// en: 'Please fill the cancellation reason'
	String get cancelFormValidation => 'Please fill the cancellation reason';

	/// en: 'Cancelled'
	String get cancelled => 'Cancelled';
}

// Path: payment
class TranslationsPaymentEn {
	TranslationsPaymentEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'Amount'
	String get amount => 'Amount';

	/// en: 'Type'
	String get type => 'Type';

	/// en: 'Branch'
	String get branch => 'Branch';

	/// en: 'Patient'
	String get patient => 'Patient';

	/// en: 'Created At'
	String get createdAt => 'Created At';

	/// en: 'Action By'
	String get createdBy => 'Action By';

	/// en: 'Cancelled'
	String get cancelled => 'Cancelled';

	/// en: 'Cancelled By'
	String get cancelledBy => 'Cancelled By';

	/// en: 'Cancellation Reason'
	String get cancellationReason => 'Cancellation Reason';

	late final TranslationsPaymentTypesEn types = TranslationsPaymentTypesEn.internal(_root);
}

// Path: profileVisitItem
class TranslationsProfileVisitItemEn {
	TranslationsProfileVisitItemEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'Discount'
	String get discount => 'Discount';

	/// en: 'Tooth Number'
	String get toothNumber => 'Tooth Number';

	/// en: 'Procedures'
	String get procedures => 'Procedures';

	/// en: 'No Procedures'
	String get noProcedures => 'No Procedures';

	/// en: 'Diagnosis'
	String get diagnosis => 'Diagnosis';

	/// en: 'No Diagnosis'
	String get noDiagnosis => 'No Diagnosis';

	/// en: 'Treatments'
	String get treatments => 'Treatments';

	/// en: 'No Treatments'
	String get noTreatments => 'No Treatments';

	/// en: 'Comment'
	String get comment => 'Comment';

	/// en: 'No Comment'
	String get noComment => 'No Comment';
}

// Path: specialitiesList
class TranslationsSpecialitiesListEn {
	TranslationsSpecialitiesListEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'Specialities List'
	String get title => 'Specialities List';

	/// en: 'Add Speciality'
	String get add => 'Add Speciality';

	/// en: 'Edit Speciality'
	String get edit => 'Edit Speciality';
}

// Path: speciality
class TranslationsSpecialityEn {
	TranslationsSpecialityEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'Name'
	String get name => 'Name';
}

// Path: procedureTemplate
class TranslationsProcedureTemplateEn {
	TranslationsProcedureTemplateEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'Procedures'
	String get procedures => 'Procedures';

	/// en: 'Edit Procedure'
	String get editProcedure => 'Edit Procedure';

	/// en: 'Add Procedure'
	String get addProcedure => 'Add Procedure';

	/// en: 'Name'
	String get name => 'Name';

	/// en: 'Price'
	String get price => 'Price';

	/// en: 'Teeth Removed'
	String get teethRemoved => 'Teeth Removed';

	/// en: 'Crown'
	String get crown => 'Crown';

	/// en: 'Endo'
	String get endo => 'Endo';

	/// en: 'Implant'
	String get implant => 'Implant';

	/// en: 'Operative'
	String get operative => 'Operative';

	/// en: 'Speciality'
	String get speciality => 'Speciality';
}

// Path: procedureTemplateForm
class TranslationsProcedureTemplateFormEn {
	TranslationsProcedureTemplateFormEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'Save and Add Other'
	String get saveAndAddOther => 'Save and Add Other';
}

// Path: visitForm
class TranslationsVisitFormEn {
	TranslationsVisitFormEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'Create Visit'
	String get createVisit => 'Create Visit';

	/// en: 'View Patient Profile'
	String get viewPatientProfile => 'View Patient Profile';

	/// en: 'No Procedures'
	String get noProcedures => 'No Procedures';

	/// en: 'No Files'
	String get noFiles => 'No Files';
}

// Path: visit
class TranslationsVisitEn {
	TranslationsVisitEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'Comments'
	String get comments => 'Comments';

	/// en: 'Diagnosis'
	String get diagnosis => 'Diagnosis';

	/// en: 'Next Visit'
	String get nextVisit => 'Next Visit';

	/// en: 'Treatments'
	String get treatments => 'Treatments';

	/// en: 'Procedures'
	String get procedures => 'Procedures';

	/// en: 'Files'
	String get files => 'Files';
}

// Path: visitProcedure
class TranslationsVisitProcedureEn {
	TranslationsVisitProcedureEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'Procedure'
	String get procedure => 'Procedure';

	/// en: 'Price'
	String get price => 'Price';

	/// en: 'Discount'
	String get discount => 'Discount';

	/// en: 'Total'
	String get total => 'Total';

	/// en: 'Teeth Removed'
	String get teethRemoved => 'Teeth Removed';

	/// en: 'Crown'
	String get crown => 'Crown';

	/// en: 'Endo'
	String get endo => 'Endo';

	/// en: 'Implant'
	String get implant => 'Implant';

	/// en: 'Operative'
	String get operative => 'Operative';

	/// en: 'Speciality'
	String get speciality => 'Speciality';

	/// en: 'Dentist'
	String get dentist => 'Dentist';

	/// en: 'Notes'
	String get notes => 'Notes';

	/// en: 'Next Visit'
	String get nextVisit => 'Next Visit';
}

// Path: visitProcedureForm
class TranslationsVisitProcedureFormEn {
	TranslationsVisitProcedureFormEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'Create Procedure'
	String get create => 'Create Procedure';

	/// en: 'Price'
	String get price => 'Price';

	/// en: 'Discount'
	String get discount => 'Discount';

	/// en: 'Total'
	String get total => 'Total';

	/// en: 'Teeth Removed'
	String get teethRemoved => 'Teeth Removed';

	/// en: 'Crown'
	String get crown => 'Crown';

	/// en: 'Endo'
	String get endo => 'Endo';

	/// en: 'Implant'
	String get implant => 'Implant';

	/// en: 'Operative'
	String get operative => 'Operative';

	/// en: 'Speciality'
	String get speciality => 'Speciality';

	/// en: 'Affected Teeth'
	String get affectedTeeth => 'Affected Teeth';

	/// en: 'Choose Teeth'
	String get chooseTeeth => 'Choose Teeth';

	/// en: 'Choose Speciality and Procedure'
	String get chooseSpecialityAndProcedure => 'Choose Speciality and Procedure';
}

// Path: inventoryView
class TranslationsInventoryViewEn {
	TranslationsInventoryViewEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'Inventory List'
	String get list => 'Inventory List';

	/// en: 'Add Item'
	String get add => 'Add Item';

	/// en: 'Edit Item'
	String get edit => 'Edit Item';
}

// Path: inventory
class TranslationsInventoryEn {
	TranslationsInventoryEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'Name'
	String get name => 'Name';

	/// en: 'Current Quantity'
	String get currentQuantity => 'Current Quantity';

	/// en: 'Unit'
	String get unit => 'Unit';

	/// en: 'Quantity'
	String get quantity => 'Quantity';

	/// en: 'Warning Quantity'
	String get warningQuantity => 'Warning Quantity';
}

// Path: insuranceView
class TranslationsInsuranceViewEn {
	TranslationsInsuranceViewEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'Edit Insurance'
	String get edit => 'Edit Insurance';

	/// en: 'Add Insurance'
	String get add => 'Add Insurance';

	/// en: 'Delete Insurance'
	String get delete => 'Delete Insurance';

	/// en: 'Claims'
	String get claims => 'Claims';
}

// Path: insuranceClaim
class TranslationsInsuranceClaimEn {
	TranslationsInsuranceClaimEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'Date'
	String get date => 'Date';

	/// en: 'Patient'
	String get patient => 'Patient';

	/// en: 'Company'
	String get company => 'Company';
}

// Path: insuranceClaimView
class TranslationsInsuranceClaimViewEn {
	TranslationsInsuranceClaimViewEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'Edit Claim'
	String get edit => 'Edit Claim';

	/// en: 'Add Claim'
	String get add => 'Add Claim';

	/// en: 'Delete Claim'
	String get delete => 'Delete Claim';
}

// Path: insuranceClaimForm
class TranslationsInsuranceClaimFormEn {
	TranslationsInsuranceClaimFormEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'Treatments'
	String get treatments => 'Treatments';

	/// en: 'Status'
	String get status => 'Status';
}

// Path: insuranceCompany
class TranslationsInsuranceCompanyEn {
	TranslationsInsuranceCompanyEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'Name'
	String get name => 'Name';

	/// en: 'Default Coverage'
	String get defaultCoverage => 'Default Coverage';
}

// Path: insuranceCompanyView
class TranslationsInsuranceCompanyViewEn {
	TranslationsInsuranceCompanyViewEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'Edit Insurance Company'
	String get edit => 'Edit Insurance Company';

	/// en: 'Add Insurance Company'
	String get add => 'Add Insurance Company';

	/// en: 'Delete Insurance Company'
	String get delete => 'Delete Insurance Company';
}

// Path: custom_payment_methods
class TranslationsCustomPaymentMethodsEn {
	TranslationsCustomPaymentMethodsEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'Custom Payment Methods'
	String get title => 'Custom Payment Methods';

	/// en: 'Add Custom Payment Method'
	String get add => 'Add Custom Payment Method';

	/// en: 'Edit Custom Payment Method'
	String get edit => 'Edit Custom Payment Method';

	/// en: 'Delete Custom Payment Method'
	String get delete => 'Delete Custom Payment Method';

	/// en: 'Name'
	String get name => 'Name';

	/// en: 'Percentage'
	String get percentage => 'Percentage';

	/// en: 'No Custom Payment Methods'
	String get noCustomPaymentMethods => 'No Custom Payment Methods';
}

// Path: settings
class TranslationsSettingsEn {
	TranslationsSettingsEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'Settings'
	String get settings => 'Settings';

	/// en: 'Change Language'
	String get changeLanguage => 'Change Language';

	/// en: 'No Phone Number'
	String get noPhoneNumber => 'No Phone Number';

	/// en: 'Edit Clinic Settings'
	String get editClinicSettings => 'Edit Clinic Settings';

	/// en: 'Change Password'
	String get changePassword => 'Change Password';

	/// en: 'Logout'
	String get logout => 'Logout';

	/// en: 'Billing'
	String get billing => 'Billing';

	/// en: 'Total Visits'
	String get totalVisits => 'Total Visits';

	/// en: 'Price Per Visit'
	String get pricePerVisit => 'Price Per Visit';

	/// en: 'Total Price'
	String get totalPrice => 'Total Price';
}

// Path: lab
class TranslationsLabEn {
	TranslationsLabEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'Name'
	String get name => 'Name';

	/// en: 'Phone'
	String get phone => 'Phone';
}

// Path: labView
class TranslationsLabViewEn {
	TranslationsLabViewEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'Labs'
	String get labs => 'Labs';

	/// en: 'Edit Lab'
	String get edit => 'Edit Lab';

	/// en: 'Add Lab'
	String get add => 'Add Lab';
}

// Path: labForm
class TranslationsLabFormEn {
	TranslationsLabFormEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'Edit Lab'
	String get edit => 'Edit Lab';

	/// en: 'Add Lab'
	String get add => 'Add Lab';
}

// Path: labRequestView
class TranslationsLabRequestViewEn {
	TranslationsLabRequestViewEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'Edit Lab Request'
	String get edit => 'Edit Lab Request';

	/// en: 'Add Lab Request'
	String get add => 'Add Lab Request';

	/// en: 'Delete Lab Request'
	String get delete => 'Delete Lab Request';

	/// en: 'Requests'
	String get requests => 'Requests';
}

// Path: labRequestForm
class TranslationsLabRequestFormEn {
	TranslationsLabRequestFormEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'General Info'
	String get generalInfo => 'General Info';

	/// en: 'Details'
	String get details => 'Details';
}

// Path: labRequest
class TranslationsLabRequestEn {
	TranslationsLabRequestEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'Miscellaneous'
	String get misc => 'Miscellaneous';

	/// en: 'Price'
	String get price => 'Price';

	/// en: 'Notes'
	String get notes => 'Notes';

	/// en: 'Next Step'
	String get nextStep => 'Next Step';

	/// en: 'Pontic Design'
	String get ponticDesign => 'Pontic Design';

	/// en: 'Implant Work'
	String get implantWork => 'Implant Work';

	/// en: 'ZrO2'
	String get zro2 => 'ZrO2';

	/// en: '3D Printing'
	String get threeDPrinting => '3D Printing';

	/// en: 'Full Arch Implant'
	String get fullArchImplant => 'Full Arch Implant';

	/// en: 'Emax'
	String get emax => 'Emax';

	/// en: 'Custom Abutment'
	String get customAbutment => 'Custom Abutment';

	/// en: 'PMMA'
	String get pmma => 'PMMA';

	/// en: 'Patient'
	String get patientId => 'Patient';

	/// en: 'Sending Branch'
	String get sendingBranch => 'Sending Branch';

	/// en: 'Receiving Branch'
	String get receivingBranch => 'Receiving Branch';

	/// en: 'Clinic Lab'
	String get clinicLab => 'Clinic Lab';

	/// en: 'Expected Delivery Date'
	String get expectedDeliveryDate => 'Expected Delivery Date';

	/// en: 'Send Date'
	String get sendDate => 'Send Date';

	/// en: 'Actual Delivery Date'
	String get actualDeliveryDate => 'Actual Delivery Date';

	/// en: 'Shade Details'
	String get shadeDetails => 'Shade Details';
}

// Path: overduePatients
class TranslationsOverduePatientsEn {
	TranslationsOverduePatientsEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'Overdue Patients'
	String get title => 'Overdue Patients';

	/// en: 'No Overdue Patients'
	String get noOverduePatients => 'No Overdue Patients';
}

// Path: patientGroups
class TranslationsPatientGroupsEn {
	TranslationsPatientGroupsEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'Name'
	String get name => 'Name';

	/// en: 'Grouped In'
	String get nameInList => 'Grouped In';

	/// en: 'New Group'
	String get newGroup => 'New Group';

	/// en: 'Patient Groups'
	String get title => 'Patient Groups';

	/// en: 'Groups'
	String get singleTitle => 'Groups';
}

// Path: patientForm.reachChannels
class TranslationsPatientFormReachChannelsEn {
	TranslationsPatientFormReachChannelsEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'Facebook'
	String get facebook => 'Facebook';

	/// en: 'Instagram'
	String get instagram => 'Instagram';

	/// en: 'Twitter'
	String get twitter => 'Twitter';

	/// en: 'Google'
	String get google => 'Google';

	/// en: 'Word Of Mouth'
	String get wordOfMouth => 'Word Of Mouth';

	/// en: 'Other'
	String get other => 'Other';
}

// Path: users.roles
class TranslationsUsersRolesEn {
	TranslationsUsersRolesEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'Master'
	String get master => 'Master';

	/// en: 'Admin'
	String get admin => 'Admin';

	/// en: 'Doctor'
	String get doctor => 'Doctor';

	/// en: 'Secretary'
	String get secretary => 'Secretary';

	/// en: 'Limited Access'
	String get basic => 'Limited Access';

	/// en: 'External Dentist'
	String get external => 'External Dentist';
}

// Path: expenseForm.types
class TranslationsExpenseFormTypesEn {
	TranslationsExpenseFormTypesEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'Salary'
	String get salary => 'Salary';

	/// en: 'Materials'
	String get materials => 'Materials';

	/// en: 'Fees'
	String get fees => 'Fees';

	/// en: 'Others'
	String get others => 'Others';
}

// Path: appointment.cancel
class TranslationsAppointmentCancelEn {
	TranslationsAppointmentCancelEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'Cancel Appointment'
	String get title => 'Cancel Appointment';

	/// en: 'Are you sure you want to cancel $patientName appointment? This action cannot be undone.'
	String message({required Object patientName}) => 'Are you sure you want to cancel ${patientName} appointment? This action cannot be undone.';
}

// Path: payment.types
class TranslationsPaymentTypesEn {
	TranslationsPaymentTypesEn.internal(this._root);

	final Translations _root; // ignore: unused_field

	// Translations

	/// en: 'Cash'
	String get cash => 'Cash';

	/// en: 'ValU'
	String get valu => 'ValU';

	/// en: 'Card'
	String get card => 'Card';

	/// en: 'Custom'
	String get custom => 'Custom';
}

/// Flat map(s) containing all translations.
/// Only for edge cases! For simple maps, use the map function of this library.
extension on Translations {
	dynamic _flatMapFunction(String path) {
		switch (path) {
			case 'skip': return 'Skip';
			case 'delete': return 'Delete';
			case 'search': return 'Search';
			case 'none': return 'None';
			case 'yes': return 'Yes';
			case 'no': return 'No';
			case 'noData': return 'No Data';
			case 'edit': return 'Edit';
			case 'finish': return 'Finish';
			case 'error': return 'Error';
			case 'blankFields': return 'Blank Fields';
			case 'pleaseFillRequiredFields': return ({required Object field}) => 'Please fill the required fields ${field}';
			case 'createdAt': return 'Created At';
			case 'subEnded': return 'Your Subscription Ended';
			case 'switchView': return 'Switch View';
			case 'from': return 'From';
			case 'to': return 'To';
			case 'cancel': return 'Cancel';
			case 'confirm': return 'Confirm';
			case 'english': return 'English';
			case 'arabic': return 'Arabic';
			case 'locales.en': return 'English';
			case 'locales.ar': return 'Arabic';
			case 'buttonTxt.add': return 'Add';
			case 'buttonTxt.save': return 'Save';
			case 'buttonTxt.cancel': return 'Cancel';
			case 'clinic.name': return 'Clinic Name';
			case 'clinic.language': return 'Languange';
			case 'clinic.daysPrior': return 'Days Prior';
			case 'formErrors.invalid': return 'Invalid Input';
			case 'formErrors.shouldBeNumber': return 'You must type a number';
			case 'login.clinicName': return 'Clinic Name';
			case 'login.username': return 'Username';
			case 'login.password': return 'Password';
			case 'login.login': return 'Login';
			case 'nav.dashboard': return 'Dashboard';
			case 'nav.data': return 'Data';
			case 'nav.administration': return 'Administration';
			case 'nav.home': return 'Home';
			case 'nav.analysis': return 'Analysis';
			case 'nav.insurance': return 'Insurance';
			case 'nav.patients': return 'Patients';
			case 'nav.expeenses': return 'Expenses';
			case 'nav.inventory': return 'Inventory';
			case 'nav.payments': return 'Payments';
			case 'nav.specialities': return 'Specialities';
			case 'nav.labs': return 'Labs';
			case 'nav.users': return 'Users';
			case 'nav.branches': return 'Branches';
			case 'nav.communication': return 'Communication';
			case 'analysis.title': return 'Analysis';
			case 'analysis.visitsDiagram': return 'Visits Diagram';
			case 'analysis.visits': return 'Visits';
			case 'analysis.patients': return 'Patients';
			case 'analysis.noData': return 'No Data';
			case 'analysis.expenses': return 'Expenses';
			case 'analysis.patientsReachChannel': return 'Patients Reach Channel';
			case 'analysis.unknown': return 'Unknown';
			case 'branches.title': return 'Branches';
			case 'branches.add': return 'Add Branch';
			case 'branches.edit': return 'Edit Branch';
			case 'branches.name': return 'Name';
			case 'branches.selectColor': return 'Select Color';
			case 'branches.googleMapURL': return 'Google Map URL';
			case 'branches.clear': return 'Clear';
			case 'branches.googleLink': return 'Google Link';
			case 'branches.mapView': return 'Map View';
			case 'branches.roomsCount': return 'Rooms Count';
			case 'branches.branchUpdated': return 'Branch Updated Successfully';
			case 'branches.branchAdded': return 'Branch Added Successfully';
			case 'branches.branchDeleted': return 'Branch Deleted Successfully';
			case 'branches.deleteBranch': return 'Delete Branch';
			case 'branches.listBranches': return 'List Branches';
			case 'patientsList.title': return 'Patients List';
			case 'patientsList.add': return 'Add Patient';
			case 'patientsList.edit': return 'Edit Patient';
			case 'patientForm.title': return 'Patient Form';
			case 'patientForm.name': return 'Name';
			case 'patientForm.email': return 'Email';
			case 'patientForm.phone': return 'Phone';
			case 'patientForm.age': return 'Age';
			case 'patientForm.balance': return 'Balance';
			case 'patientForm.reachChannel': return 'Reach Channel';
			case 'patientForm.address': return 'Address';
			case 'patientForm.birthdate': return 'Birthdate';
			case 'patientForm.job': return 'Job';
			case 'patientForm.fileNumber': return 'File Number';
			case 'patientForm.dentalHistory': return 'Dental History';
			case 'patientForm.medicalHistory': return 'Medical History';
			case 'patientForm.treatmentPlan': return 'Treatment Plan';
			case 'patientForm.reachChannels.facebook': return 'Facebook';
			case 'patientForm.reachChannels.instagram': return 'Instagram';
			case 'patientForm.reachChannels.twitter': return 'Twitter';
			case 'patientForm.reachChannels.google': return 'Google';
			case 'patientForm.reachChannels.wordOfMouth': return 'Word Of Mouth';
			case 'patientForm.reachChannels.other': return 'Other';
			case 'userForm.title': return 'User Form';
			case 'userForm.name': return 'Name';
			case 'userForm.username': return 'Username';
			case 'userForm.password': return 'Password';
			case 'userForm.role': return 'Role';
			case 'userForm.isDentist': return 'Is Dentist';
			case 'userForm.percentage': return 'Percentage';
			case 'users.list': return 'Users List';
			case 'users.add': return 'Add User';
			case 'users.userUpdated': return 'User Updated Successfully';
			case 'users.userAdded': return 'User Added Successfully';
			case 'users.userDeleted': return 'User Deleted Successfully';
			case 'users.confirmDelete': return 'Confirm Delete';
			case 'users.confirmDeleteUser': return 'Are you sure you want to delete user?';
			case 'users.roles.master': return 'Master';
			case 'users.roles.admin': return 'Admin';
			case 'users.roles.doctor': return 'Doctor';
			case 'users.roles.secretary': return 'Secretary';
			case 'users.roles.basic': return 'Limited Access';
			case 'users.roles.external': return 'External Dentist';
			case 'expenseForm.title': return 'Expense Form';
			case 'expenseForm.name': return 'Name';
			case 'expenseForm.amount': return 'Amount';
			case 'expenseForm.description': return 'Description';
			case 'expenseForm.type': return 'Type';
			case 'expenseForm.types.salary': return 'Salary';
			case 'expenseForm.types.materials': return 'Materials';
			case 'expenseForm.types.fees': return 'Fees';
			case 'expenseForm.types.others': return 'Others';
			case 'expensesList.title': return 'Expenses List';
			case 'expensesList.add': return 'Add Expense';
			case 'expensesList.edit': return 'Edit Expense';
			case 'inventoryTransactionForm.changeQuantity': return 'Change Quantity';
			case 'inventoryTransactionForm.label': return 'Put the current inventory quantity';
			case 'inventoryTransactionForm.quantity': return 'Quantity';
			case 'patient.addNewPatient': return 'Add New Patient';
			case 'patient.askToCreatePatient': return ({required Object name}) => 'Do you want to create a new patient with name: `${name}`?';
			case 'appointment.addNewAppointment': return 'New Appointment';
			case 'appointment.branch': return 'Branch';
			case 'appointment.patient': return 'Patient';
			case 'appointment.dentist': return 'Dentist';
			case 'appointment.room': return 'Room';
			case 'appointment.roomNumber': return ({required Object number}) => 'Room ${number}';
			case 'appointment.startTime': return 'Start Time';
			case 'appointment.endTime': return 'End Time';
			case 'appointment.duration': return 'Duration';
			case 'appointment.from': return ({required Object time}) => 'From ${time}';
			case 'appointment.to': return ({required Object time}) => 'To ${time}';
			case 'appointment.chooseFutureDate': return 'You should select a day after today';
			case 'appointment.cancel.title': return 'Cancel Appointment';
			case 'appointment.cancel.message': return ({required Object patientName}) => 'Are you sure you want to cancel ${patientName} appointment? This action cannot be undone.';
			case 'patientProfile.appointments': return 'Appointments';
			case 'patientProfile.insuranceClaims': return 'Insurance Claims';
			case 'patientProfile.visitsHistory': return 'Visits History';
			case 'patientProfile.visits': return 'Visits';
			case 'patientProfile.patientProfile': return 'Patient Profile';
			case 'patientProfile.edit': return 'Edit Profile';
			case 'patientProfile.switchTeethView': return 'Switch Teeth View';
			case 'patientProfile.paymentsList': return 'Payments List';
			case 'patientProfile.noTreatmentPlan': return 'No Treatment Plan';
			case 'patientProfile.payments': return 'Payments';
			case 'patientProfile.about': return 'About';
			case 'paymentsView.list': return 'Payments List';
			case 'paymentsView.newPayment': return 'New Payment';
			case 'paymentsView.add': return 'Add Payment';
			case 'paymentsView.edit': return 'Edit Payment';
			case 'paymentsView.paidAmount': return 'Paid Amount';
			case 'paymentsView.cancel': return 'Cancel';
			case 'paymentsView.cancelTitle': return 'Cancel Payment';
			case 'paymentsView.cancelFormReason': return 'Cancellation Reason';
			case 'paymentsView.cancelFormValidation': return 'Please fill the cancellation reason';
			case 'paymentsView.cancelled': return 'Cancelled';
			case 'payment.amount': return 'Amount';
			case 'payment.type': return 'Type';
			case 'payment.branch': return 'Branch';
			case 'payment.patient': return 'Patient';
			case 'payment.createdAt': return 'Created At';
			case 'payment.createdBy': return 'Action By';
			case 'payment.cancelled': return 'Cancelled';
			case 'payment.cancelledBy': return 'Cancelled By';
			case 'payment.cancellationReason': return 'Cancellation Reason';
			case 'payment.types.cash': return 'Cash';
			case 'payment.types.valu': return 'ValU';
			case 'payment.types.card': return 'Card';
			case 'payment.types.custom': return 'Custom';
			case 'profileVisitItem.discount': return 'Discount';
			case 'profileVisitItem.toothNumber': return 'Tooth Number';
			case 'profileVisitItem.procedures': return 'Procedures';
			case 'profileVisitItem.noProcedures': return 'No Procedures';
			case 'profileVisitItem.diagnosis': return 'Diagnosis';
			case 'profileVisitItem.noDiagnosis': return 'No Diagnosis';
			case 'profileVisitItem.treatments': return 'Treatments';
			case 'profileVisitItem.noTreatments': return 'No Treatments';
			case 'profileVisitItem.comment': return 'Comment';
			case 'profileVisitItem.noComment': return 'No Comment';
			case 'specialitiesList.title': return 'Specialities List';
			case 'specialitiesList.add': return 'Add Speciality';
			case 'specialitiesList.edit': return 'Edit Speciality';
			case 'speciality.name': return 'Name';
			case 'procedureTemplate.procedures': return 'Procedures';
			case 'procedureTemplate.editProcedure': return 'Edit Procedure';
			case 'procedureTemplate.addProcedure': return 'Add Procedure';
			case 'procedureTemplate.name': return 'Name';
			case 'procedureTemplate.price': return 'Price';
			case 'procedureTemplate.teethRemoved': return 'Teeth Removed';
			case 'procedureTemplate.crown': return 'Crown';
			case 'procedureTemplate.endo': return 'Endo';
			case 'procedureTemplate.implant': return 'Implant';
			case 'procedureTemplate.operative': return 'Operative';
			case 'procedureTemplate.speciality': return 'Speciality';
			case 'procedureTemplateForm.saveAndAddOther': return 'Save and Add Other';
			case 'visitForm.createVisit': return 'Create Visit';
			case 'visitForm.viewPatientProfile': return 'View Patient Profile';
			case 'visitForm.noProcedures': return 'No Procedures';
			case 'visitForm.noFiles': return 'No Files';
			case 'visit.comments': return 'Comments';
			case 'visit.diagnosis': return 'Diagnosis';
			case 'visit.nextVisit': return 'Next Visit';
			case 'visit.treatments': return 'Treatments';
			case 'visit.procedures': return 'Procedures';
			case 'visit.files': return 'Files';
			case 'visitProcedure.procedure': return 'Procedure';
			case 'visitProcedure.price': return 'Price';
			case 'visitProcedure.discount': return 'Discount';
			case 'visitProcedure.total': return 'Total';
			case 'visitProcedure.teethRemoved': return 'Teeth Removed';
			case 'visitProcedure.crown': return 'Crown';
			case 'visitProcedure.endo': return 'Endo';
			case 'visitProcedure.implant': return 'Implant';
			case 'visitProcedure.operative': return 'Operative';
			case 'visitProcedure.speciality': return 'Speciality';
			case 'visitProcedure.dentist': return 'Dentist';
			case 'visitProcedure.notes': return 'Notes';
			case 'visitProcedure.nextVisit': return 'Next Visit';
			case 'visitProcedureForm.create': return 'Create Procedure';
			case 'visitProcedureForm.price': return 'Price';
			case 'visitProcedureForm.discount': return 'Discount';
			case 'visitProcedureForm.total': return 'Total';
			case 'visitProcedureForm.teethRemoved': return 'Teeth Removed';
			case 'visitProcedureForm.crown': return 'Crown';
			case 'visitProcedureForm.endo': return 'Endo';
			case 'visitProcedureForm.implant': return 'Implant';
			case 'visitProcedureForm.operative': return 'Operative';
			case 'visitProcedureForm.speciality': return 'Speciality';
			case 'visitProcedureForm.affectedTeeth': return 'Affected Teeth';
			case 'visitProcedureForm.chooseTeeth': return 'Choose Teeth';
			case 'visitProcedureForm.chooseSpecialityAndProcedure': return 'Choose Speciality and Procedure';
			case 'inventoryView.list': return 'Inventory List';
			case 'inventoryView.add': return 'Add Item';
			case 'inventoryView.edit': return 'Edit Item';
			case 'inventory.name': return 'Name';
			case 'inventory.currentQuantity': return 'Current Quantity';
			case 'inventory.unit': return 'Unit';
			case 'inventory.quantity': return 'Quantity';
			case 'inventory.warningQuantity': return 'Warning Quantity';
			case 'insuranceView.edit': return 'Edit Insurance';
			case 'insuranceView.add': return 'Add Insurance';
			case 'insuranceView.delete': return 'Delete Insurance';
			case 'insuranceView.claims': return 'Claims';
			case 'insuranceClaim.date': return 'Date';
			case 'insuranceClaim.patient': return 'Patient';
			case 'insuranceClaim.company': return 'Company';
			case 'insuranceClaimView.edit': return 'Edit Claim';
			case 'insuranceClaimView.add': return 'Add Claim';
			case 'insuranceClaimView.delete': return 'Delete Claim';
			case 'insuranceClaimForm.treatments': return 'Treatments';
			case 'insuranceClaimForm.status': return 'Status';
			case 'insuranceCompany.name': return 'Name';
			case 'insuranceCompany.defaultCoverage': return 'Default Coverage';
			case 'insuranceCompanyView.edit': return 'Edit Insurance Company';
			case 'insuranceCompanyView.add': return 'Add Insurance Company';
			case 'insuranceCompanyView.delete': return 'Delete Insurance Company';
			case 'custom_payment_methods.title': return 'Custom Payment Methods';
			case 'custom_payment_methods.add': return 'Add Custom Payment Method';
			case 'custom_payment_methods.edit': return 'Edit Custom Payment Method';
			case 'custom_payment_methods.delete': return 'Delete Custom Payment Method';
			case 'custom_payment_methods.name': return 'Name';
			case 'custom_payment_methods.percentage': return 'Percentage';
			case 'custom_payment_methods.noCustomPaymentMethods': return 'No Custom Payment Methods';
			case 'settings.settings': return 'Settings';
			case 'settings.changeLanguage': return 'Change Language';
			case 'settings.noPhoneNumber': return 'No Phone Number';
			case 'settings.editClinicSettings': return 'Edit Clinic Settings';
			case 'settings.changePassword': return 'Change Password';
			case 'settings.logout': return 'Logout';
			case 'settings.billing': return 'Billing';
			case 'settings.totalVisits': return 'Total Visits';
			case 'settings.pricePerVisit': return 'Price Per Visit';
			case 'settings.totalPrice': return 'Total Price';
			case 'lab.name': return 'Name';
			case 'lab.phone': return 'Phone';
			case 'labView.labs': return 'Labs';
			case 'labView.edit': return 'Edit Lab';
			case 'labView.add': return 'Add Lab';
			case 'labForm.edit': return 'Edit Lab';
			case 'labForm.add': return 'Add Lab';
			case 'labRequestView.edit': return 'Edit Lab Request';
			case 'labRequestView.add': return 'Add Lab Request';
			case 'labRequestView.delete': return 'Delete Lab Request';
			case 'labRequestView.requests': return 'Requests';
			case 'labRequestForm.generalInfo': return 'General Info';
			case 'labRequestForm.details': return 'Details';
			case 'labRequest.misc': return 'Miscellaneous';
			case 'labRequest.price': return 'Price';
			case 'labRequest.notes': return 'Notes';
			case 'labRequest.nextStep': return 'Next Step';
			case 'labRequest.ponticDesign': return 'Pontic Design';
			case 'labRequest.implantWork': return 'Implant Work';
			case 'labRequest.zro2': return 'ZrO2';
			case 'labRequest.threeDPrinting': return '3D Printing';
			case 'labRequest.fullArchImplant': return 'Full Arch Implant';
			case 'labRequest.emax': return 'Emax';
			case 'labRequest.customAbutment': return 'Custom Abutment';
			case 'labRequest.pmma': return 'PMMA';
			case 'labRequest.patientId': return 'Patient';
			case 'labRequest.sendingBranch': return 'Sending Branch';
			case 'labRequest.receivingBranch': return 'Receiving Branch';
			case 'labRequest.clinicLab': return 'Clinic Lab';
			case 'labRequest.expectedDeliveryDate': return 'Expected Delivery Date';
			case 'labRequest.sendDate': return 'Send Date';
			case 'labRequest.actualDeliveryDate': return 'Actual Delivery Date';
			case 'labRequest.shadeDetails': return 'Shade Details';
			case 'overduePatients.title': return 'Overdue Patients';
			case 'overduePatients.noOverduePatients': return 'No Overdue Patients';
			case 'patientGroups.name': return 'Name';
			case 'patientGroups.nameInList': return 'Grouped In';
			case 'patientGroups.newGroup': return 'New Group';
			case 'patientGroups.title': return 'Patient Groups';
			case 'patientGroups.singleTitle': return 'Groups';
			default: return null;
		}
	}
}

