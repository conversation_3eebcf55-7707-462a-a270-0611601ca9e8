{"skip": "<PERSON><PERSON>", "delete": "Delete", "search": "Search", "none": "None", "yes": "Yes", "no": "No", "noData": "No Data", "edit": "Edit", "finish": "Finish", "error": "Error", "blankFields": "Blank Fields", "pleaseFillRequiredFields": "Please fill the required fields $field", "createdAt": "Created At", "subEnded": "Your Subscription Ended", "switchView": "Switch View", "from": "From", "to": "To", "cancel": "Cancel", "confirm": "Confirm", "english": "English", "arabic": "Arabic", "locales(map)": {"en": "English", "ar": "Arabic"}, "buttonTxt": {"add": "Add", "save": "Save", "cancel": "Cancel"}, "clinic": {"name": "Clinic Name", "language": "Languange", "daysPrior": "Days Prior"}, "formErrors": {"invalid": "Invalid Input", "shouldBeNumber": "You must type a number"}, "login": {"clinicName": "Clinic Name", "username": "Username", "password": "Password", "login": "<PERSON><PERSON>"}, "nav": {"dashboard": "Dashboard", "data": "Data", "administration": "Administration", "home": "Home", "analysis": "Analysis", "insurance": "Insurance", "patients": "Patients", "expeenses": "Expenses", "inventory": "Inventory", "payments": "Payments", "specialities": "Specialities", "labs": "Labs", "users": "Users", "branches": "Branches", "communication": "Communication"}, "analysis": {"title": "Analysis", "visitsDiagram": "Visits Diagram", "visits": "Visits", "patients": "Patients", "noData": "No Data", "expenses": "Expenses", "patientsReachChannel": "Patients Reach Channel", "unknown": "Unknown"}, "branches": {"title": "Branches", "add": "Add Branch", "edit": "Edit Branch", "name": "Name", "selectColor": "Select Color", "googleMapURL": "Google Map URL", "clear": "Clear", "googleLink": "Google Link", "mapView": "Map View", "roomsCount": "Rooms Count", "branchUpdated": "Branch Updated Successfully", "branchAdded": "Branch Added Successfully", "branchDeleted": "Branch Deleted Successfully", "deleteBranch": "Delete Branch", "listBranches": "List Branches"}, "patientsList": {"title": "Patients List", "add": "Add Patient", "edit": "<PERSON>"}, "patientForm": {"title": "Patient Form", "name": "Name", "email": "Email", "phone": "Phone", "age": "Age", "balance": "Balance", "reachChannel": "Reach Channel", "address": "Address", "birthdate": "Birthdate", "job": "Job", "fileNumber": "File Number", "dentalHistory": "Dental History", "medicalHistory": "Medical History", "treatmentPlan": "Treatment Plan", "reachChannels": {"facebook": "Facebook", "instagram": "Instagram", "twitter": "Twitter", "google": "Google", "wordOfMouth": "Word Of Mouth", "other": "Other"}}, "userForm": {"title": "User Form", "name": "Name", "username": "Username", "password": "Password", "role": "Role", "isDentist": "Is Dentist", "percentage": "Percentage"}, "users": {"list": "Users List", "add": "Add User", "userUpdated": "User Updated Successfully", "userAdded": "User Added Successfully", "userDeleted": "User Deleted Successfully", "confirmDelete": "Confirm Delete", "confirmDeleteUser": "Are you sure you want to delete user?", "roles": {"master": "Master", "admin": "Admin", "doctor": "Doctor", "secretary": "Secretary", "basic": "Limited Access", "external": "External Dentist"}}, "expenseForm": {"title": "Expense Form", "name": "Name", "amount": "Amount", "description": "Description", "type": "Type", "types": {"salary": "Salary", "materials": "Materials", "fees": "Fees", "others": "Others"}}, "expensesList": {"title": "Expenses List", "add": "Add Expense", "edit": "Edit Expense"}, "inventoryTransactionForm": {"changeQuantity": "Change Quantity", "label": "Put the current inventory quantity", "quantity": "Quantity"}, "patient": {"addNewPatient": "Add New Patient", "askToCreatePatient": "Do you want to create a new patient with name: `$name`?"}, "appointment": {"addNewAppointment": "New Appointment", "branch": "Branch", "patient": "Patient", "dentist": "Dentist", "room": "Room", "roomNumber": "Room $number", "startTime": "Start Time", "endTime": "End Time", "duration": "Duration", "from": "From $time", "to": "To $time", "chooseFutureDate": "You should select a day after today", "cancel": {"title": "<PERSON>cel Appointment", "message": "Are you sure you want to cancel $patientName appointment? This action cannot be undone."}}, "patientProfile": {"appointments": "Appointments", "insuranceClaims": "Insurance Claims", "visitsHistory": "Visits History", "visits": "Visits", "patientProfile": "Patient Profile", "edit": "Edit Profile", "switchTeethView": "Switch Teeth View", "paymentsList": "Payments List", "noTreatmentPlan": "No Treatment Plan", "payments": "Payments", "about": "About"}, "paymentsView": {"list": "Payments List", "newPayment": "New Payment", "add": "Add Payment", "edit": "Edit Payment", "paidAmount": "<PERSON><PERSON>", "cancel": "Cancel", "cancelTitle": "Cancel Payment", "cancelFormReason": "Cancellation Reason", "cancelFormValidation": "Please fill the cancellation reason", "cancelled": "Cancelled"}, "payment": {"amount": "Amount", "type": "Type", "branch": "Branch", "patient": "Patient", "createdAt": "Created At", "createdBy": "Action By", "cancelled": "Cancelled", "cancelledBy": "Cancelled By", "cancellationReason": "Cancellation Reason", "types": {"cash": "Cash", "valu": "ValU", "card": "Card", "custom": "Custom"}}, "profileVisitItem": {"discount": "Discount", "toothNumber": "Tooth Number", "procedures": "Procedures", "noProcedures": "No Procedures", "diagnosis": "Diagnosis", "noDiagnosis": "No Diagnosis", "treatments": "Treatments", "noTreatments": "No Treatments", "comment": "Comment", "noComment": "No Comment"}, "specialitiesList": {"title": "Specialities List", "add": "Add Speciality", "edit": "Edit Speciality"}, "speciality": {"name": "Name"}, "procedureTemplate": {"procedures": "Procedures", "editProcedure": "Edit Procedure", "addProcedure": "Add Procedure", "name": "Name", "price": "Price", "teethRemoved": "<PERSON><PERSON>moved", "crown": "Crown", "endo": "<PERSON><PERSON>", "implant": "Implant", "operative": "Operative", "speciality": "Speciality"}, "procedureTemplateForm": {"saveAndAddOther": "Save and Add Other"}, "visitForm": {"createVisit": "Create Visit", "viewPatientProfile": "View Patient Profile", "noProcedures": "No Procedures", "noFiles": "No Files"}, "visit": {"comments": "Comments", "diagnosis": "Diagnosis", "nextVisit": "Next Visit", "treatments": "Treatments", "procedures": "Procedures", "files": "Files"}, "visitProcedure": {"procedure": "Procedure", "price": "Price", "discount": "Discount", "total": "Total", "teethRemoved": "<PERSON><PERSON>moved", "crown": "Crown", "endo": "<PERSON><PERSON>", "implant": "Implant", "operative": "Operative", "speciality": "Speciality", "dentist": "Dentist", "notes": "Notes", "nextVisit": "Next Visit"}, "visitProcedureForm": {"create": "Create Procedure", "price": "Price", "discount": "Discount", "total": "Total", "teethRemoved": "<PERSON><PERSON>moved", "crown": "Crown", "endo": "<PERSON><PERSON>", "implant": "Implant", "operative": "Operative", "speciality": "Speciality", "affectedTeeth": "Affected Teeth", "chooseTeeth": "<PERSON><PERSON>", "chooseSpecialityAndProcedure": "Choose Speciality and Procedure"}, "inventoryView": {"list": "Inventory List", "add": "Add Item", "edit": "<PERSON>em"}, "inventory": {"name": "Name", "currentQuantity": "Current Quantity", "unit": "Unit", "quantity": "Quantity", "warningQuantity": "Warning Quantity"}, "insuranceView": {"edit": "Edit Insurance", "add": "Add Insurance", "delete": "Delete Insurance", "claims": "<PERSON><PERSON><PERSON>"}, "insuranceClaim": {"date": "Date", "patient": "Patient", "company": "Company"}, "insuranceClaimView": {"edit": "<PERSON>", "add": "<PERSON><PERSON>", "delete": "Delete Claim"}, "insuranceClaimForm": {"treatments": "Treatments", "status": "Status"}, "insuranceCompany": {"name": "Name", "defaultCoverage": "Default Coverage"}, "insuranceCompanyView": {"edit": "Edit Insurance Company", "add": "Add Insurance Company", "delete": "Delete Insurance Company"}, "custom_payment_methods": {"title": "Custom Payment Methods", "add": "Add Custom Payment Method", "edit": "Edit Custom Payment Method", "delete": "Delete Custom Payment Method", "name": "Name", "percentage": "Percentage", "noCustomPaymentMethods": "No Custom Payment Methods"}, "settings": {"settings": "Settings", "changeLanguage": "Change Language", "noPhoneNumber": "No Phone Number", "editClinicSettings": "Edit Clinic Settings", "changePassword": "Change Password", "logout": "Logout", "billing": "Billing", "totalVisits": "Total Visits", "pricePerVisit": "Price Per Visit", "totalPrice": "Total Price"}, "lab": {"name": "Name", "phone": "Phone"}, "labView": {"labs": "Labs", "edit": "Edit Lab", "add": "Add Lab"}, "labForm": {"edit": "Edit Lab", "add": "Add Lab"}, "labRequestView": {"edit": "Edit Lab Request", "add": "Add Lab Request", "delete": "Delete Lab Request", "requests": "Requests"}, "labRequestForm": {"generalInfo": "General Info", "details": "Details"}, "labRequest": {"misc": "Miscellaneous", "price": "Price", "notes": "Notes", "nextStep": "Next Step", "ponticDesign": "Pontic Design", "implantWork": "Implant Work", "zro2": "ZrO2", "threeDPrinting": "3D Printing", "fullArchImplant": "Full Arch Implant", "emax": "Emax", "customAbutment": "Custom Abutment", "pmma": "PMMA", "patientId": "Patient", "sendingBranch": "Sending Branch", "receivingBranch": "Receiving Branch", "clinicLab": "Clinic Lab", "expectedDeliveryDate": "Expected Delivery Date", "sendDate": "Send Date", "actualDeliveryDate": "Actual Delivery Date", "shadeDetails": "Shade Details"}, "overduePatients": {"title": "Overdue Patients", "noOverduePatients": "No Overdue Patients"}, "patientGroups": {"name": "Name", "nameInList": "Grouped In", "newGroup": "New Group", "title": "Patient Groups", "singleTitle": "Groups"}}