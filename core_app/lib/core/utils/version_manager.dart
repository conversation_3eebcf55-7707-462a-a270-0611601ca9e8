import 'package:flutter/services.dart';
import 'package:get_storage/get_storage.dart';
import 'dart:convert';
import 'package:flutter/foundation.dart';

class VersionManager {
  static const String _storageKey = 'last_shown_feature_version';
  static const String _featuresPath = 'assets/features_promo/';

  static final GetStorage _storage = GetStorage('version_manager');

  /// Get the latest version file from assets
  static Future<String?> getLatestVersion() async {
    try {
      final manifestContent = await rootBundle.loadString('AssetManifest.json');
      final Map<String, dynamic> manifestMap = Map<String, dynamic>.from(
        json.decode(manifestContent),
      );

      // Filter for feature promo files
      final featureFiles = manifestMap.keys
          .where((String key) => key.startsWith(_featuresPath))
          .where((String key) => key.endsWith('.md'))
          .toList();

      if (featureFiles.isEmpty) return null;

      // Extract version numbers and find the latest
      String? latestVersion;
      String? latestVersionFile;

      for (final file in featureFiles) {
        final fileName = file.split('/').last.replaceAll('.md', '');
        if (_isValidVersion(fileName)) {
          if (latestVersion == null ||
              _compareVersions(fileName, latestVersion) > 0) {
            latestVersion = fileName;
            latestVersionFile = file;
          }
        }
      }

      if (latestVersionFile != null) {
        return await rootBundle.loadString(latestVersionFile);
      }

      return null;
    } catch (e) {
      debugPrint('Error loading latest version: $e');
      return null;
    }
  }

  /// Check if a version should be shown
  static bool shouldShowVersion(String version) {
    final lastShown = _storage.read<String>(_storageKey);
    if (lastShown == null) return true;

    print('version: $version');
    print('lastShown: $lastShown');
    print(_compareVersions(version, lastShown));
    print(_compareVersions(version, lastShown) > 0);

    return _compareVersions(version, lastShown) > 0;
  }

  /// Mark a version as shown
  static void markVersionAsShown(String version) {
    _storage.write(_storageKey, version);
  }

  /// Get the last shown version
  static String? getLastShownVersion() {
    return _storage.read<String>(_storageKey);
  }

  /// Check if version string is valid
  static bool _isValidVersion(String version) {
    final regex = RegExp(r'^\d+(\.\d+)*$');
    return regex.hasMatch(version);
  }

  /// Compare two version strings
  /// Returns: 1 if v1 > v2, -1 if v1 < v2, 0 if equal
  static int _compareVersions(String v1, String v2) {
    final parts1 = v1.split('.').map(int.parse).toList();
    final parts2 = v2.split('.').map(int.parse).toList();

    final maxLength =
        parts1.length > parts2.length ? parts1.length : parts2.length;

    for (int i = 0; i < maxLength; i++) {
      final part1 = i < parts1.length ? parts1[i] : 0;
      final part2 = i < parts2.length ? parts2[i] : 0;

      if (part1 > part2) return 1;
      if (part1 < part2) return -1;
    }

    return 0;
  }

  /// Extract version from markdown content (first line with #)
  static String? extractVersionFromContent(String content) {
    final lines = content.split('\n');
    for (final line in lines) {
      if (line.trim().startsWith('# ')) {
        // Look for version pattern in the title
        final versionMatch = RegExp(r'(\d+(\.\d+)*)').firstMatch(line);
        if (versionMatch != null) {
          return versionMatch.group(1);
        }
      }
    }
    return null;
  }
}
