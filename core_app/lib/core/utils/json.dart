// ignore_for_file: constant_identifier_names

import 'package:json_annotation/json_annotation.dart';

typedef Json = Map<String, dynamic>;

const JsonDateTime = JsonKey(fromJson: dateTimeFromJson, toJson: dateTimeToJson);
DateTime? dateTimeFromJson(Json? j) {
  if (j == null) return null;
  if (!j['Valid']) return null;

  final String date = j['String'] ?? j['Time'];
  return DateTime.parse(date);
}

String? dateTimeToJson(DateTime? dateTime) {
  return dateTime?.toUtc().toIso8601String();
}

//////////////////////////////////////////////
/////////////////////////////////////////////

const JsonString = JsonKey(fromJson: stringFromJson, toJson: stringToJson);

String? stringFromJson(Map<String, dynamic>? id) {
  if (id == null) return null;
  if ((id['Valid'] ?? false) == false) return null;
  return id['String'];
}

Map<String, dynamic> stringToJson(String? string) {
  return {'String': string, 'Valid': string != null};
}

//////////////////////////////////////////////
/////////////////////////////////////////////

const JsonCoordinate = JsonKey(fromJson: coordinateFromJson, toJson: coordinateToJson);

double? coordinateFromJson(Map<String, dynamic>? id) {
  if (id == null) return null;
  if (!id['Valid']) return null;
  return id['Float64'];
}

Map<String, dynamic> coordinateToJson(double? string) {
  return {'Float64': string, 'Valid': string != null};
}
