import 'package:flutter/material.dart';
import 'package:get/get_utils/get_utils.dart';

extension Responsive on BuildContext {
  int get mobileSize => 490;
  int get tabletSize => 700;
  int get desktopSize => 1100;

  bool get mobileView => width <= mobileSize;
  bool get tabletView => width > mobileSize && width <= tabletSize;
  bool get desktopView => width > tabletSize; // && width <= kDesktopSize;
}
