import 'dart:convert';
import 'dart:io';

import 'package:core_app/core/api/users.dart';
import 'package:core_app/services/auth.dart';
import 'package:core_app/services/config.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:package_info_plus/package_info_plus.dart';

import 'constants/configuration.dart';
import '../firebase_options.dart';
import 'package:flutter/foundation.dart';

class FCMInitializer {
  static Future<void> init() async {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    FirebaseMessaging.onMessage.listen(handleFCMessage);
    FirebaseMessaging.onBackgroundMessage(handleFCMessage);

    // ignore: unused_local_variable
    FirebaseAnalytics analytics = FirebaseAnalytics.instance;
    analytics.logAppOpen();
    analytics.setAnalyticsCollectionEnabled(true);
    if (AuthService.loggedIn) {
      analytics.setUserId(id: AuthService.to.user.value!.id.toString());
    }
    initRemoteConfig();
  }

  static Future<void> setToken() async {
    FirebaseMessaging messaging = FirebaseMessaging.instance;
    await messaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );

    // For Apple platforms, ensure the APNS token is available before making any FCM plugin API calls
    try {
      if (Platform.isIOS || Platform.isMacOS) {
        await _waitForAPNSToken();
      }

      final fcmToken =
          await FirebaseMessaging.instance.getToken(vapidKey: VAPID_KEY);
      if (fcmToken != null) {
        await UsersAPI.updateMe(null, fcmToken);
      }
      FirebaseMessaging.instance.onTokenRefresh.listen((token) async {
        await UsersAPI.updateMe(null, token);
      });
    } on Exception catch (e) {
      if (kDebugMode) print(e);
    }
  }

  /// Waits for APNS token to become available on Apple platforms
  static Future<void> _waitForAPNSToken() async {
    const maxRetries = 10;
    const retryDelay = Duration(milliseconds: 500);

    for (int i = 0; i < maxRetries; i++) {
      final apnsToken = await FirebaseMessaging.instance.getAPNSToken();
      if (apnsToken != null) {
        if (kDebugMode) print('APNS token is now available');
        return;
      }

      if (i < maxRetries - 1) {
        await Future.delayed(retryDelay);
      }
    }

    if (kDebugMode) {
      print(
          'Warning: APNS token is still not available after waiting. FCM functionality may not work properly.');
    }
  }

  static Future<void> handleFCMessage(RemoteMessage msg) async {
    // final title = msg.notification?.title;
    // final body = msg.notification?.body;
  }

  static Future<void> initRemoteConfig() async {
    final remoteConfig = FirebaseRemoteConfig.instance;
    await remoteConfig.setConfigSettings(RemoteConfigSettings(
      fetchTimeout: const Duration(minutes: 1),
      minimumFetchInterval: const Duration(hours: 1),
    ));

    remoteConfig.fetchAndActivate();
    if (kIsWeb) {
      ConfigService.to.versionStatus = VersionStatus.upToDate;
      return;
    }
    final versionData = remoteConfig.getString('version');
    if (versionData.isEmpty) {
      ConfigService.to.versionStatus = VersionStatus.upToDate;
      return;
    }

    final data = jsonDecode(versionData);
    final platform = Platform.isIOS ? 'ios' : 'android';
    final minimumVersionString = data[platform]['minimum'] as String;
    final currentVersionString = data[platform]['current'] as String;

    final minimumVersion =
        minimumVersionString.split('.').fold(0, (previousValue, element) {
      // 0 * 1000 + 1 = 1
      // 1 * 1000 + 2 = 1002
      // 102 * 1000 + 3 = 1002003
      return previousValue * 1000 + int.parse(element);
    });
    final currentVersion =
        currentVersionString.split('.').fold(0, (previousValue, element) {
      // 0 * 1000 + 1 = 1
      // 1 * 1000 + 2 = 1002
      // 102 * 1000 + 3 = 1002003
      return previousValue * 1000 + int.parse(element);
    });

    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    final currentAppVersion =
        packageInfo.version.split('.').fold(0, (previousValue, element) {
      // 0 * 1000 + 1 = 1
      // 1 * 1000 + 2 = 1002
      // 102 * 1000 + 3 = 1002003
      return previousValue * 1000 + int.parse(element);
    });

    if (currentAppVersion < minimumVersion) {
      ConfigService.to.versionStatus = VersionStatus.needForceUpdate;
    } else if (currentAppVersion < currentVersion) {
      ConfigService.to.versionStatus = VersionStatus.needUpdate;
    } else {
      ConfigService.to.versionStatus = VersionStatus.upToDate;
    }
  }

  // get molarmind_min_confedence from remote config
  static double molarmind_min_confedence() {
    final remote = FirebaseRemoteConfig.instance;
    return remote.getDouble('molarmind_min_confedence');
  }
}
