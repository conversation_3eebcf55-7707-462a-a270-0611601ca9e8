import 'package:flutter/material.dart';

class PredictionColors {
  // Color mapping for different dental conditions/classes
  static final Map<String, Color> classColors = {
    'cavity': Colors.red,
    'caries': Colors.red,
    'decay': Colors.red,
    'filling': Colors.blue,
    'crown': Colors.green,
    'implant': Colors.purple,
    'missing': Colors.grey,
    'healthy': Colors.lightGreen,
    'plaque': Colors.yellow,
    'tartar': Colors.orange,
    'gingivitis': Colors.pink,
    'periodontitis': Colors.brown,
    'abscess': Colors.red.shade800,
    'crack': Colors.blue.shade800,
    'fracture': Colors.blue.shade800,
    'root_canal': Colors.teal,
    'extraction': Colors.black,
    'whitening': Colors.cyan,
    'orthodontic': Colors.indigo,
    'prosthetic': Colors.amber,
  };

  // Default color for unknown classes
  static const Color defaultColor = Colors.grey;

  // Get color for a specific class
  static Color getColorForClass(String className) {
    final lowerClassName = className.toLowerCase();

    // Check for exact matches first
    if (classColors.containsKey(lowerClassName)) {
      return classColors[lowerClassName]!;
    }

    // Check for partial matches
    for (final entry in classColors.entries) {
      if (lowerClassName.contains(entry.key) ||
          entry.key.contains(lowerClassName)) {
        return entry.value;
      }
    }

    return defaultColor;
  }

  // Get color name for display
  static String getColorName(Color color) {
    if (color == Colors.red) return 'Red - Cavity/Caries';
    if (color == Colors.blue) return 'Blue - Filling';
    if (color == Colors.green) return 'Green - Crown';
    if (color == Colors.purple) return 'Purple - Implant';
    if (color == Colors.grey) return 'Grey - Missing Tooth';
    if (color == Colors.lightGreen) return 'Light Green - Healthy';
    if (color == Colors.yellow) return 'Yellow - Plaque';
    if (color == Colors.orange) return 'Orange - Tartar';
    if (color == Colors.pink) return 'Pink - Gingivitis';
    if (color == Colors.brown) return 'Brown - Periodontitis';
    if (color == Colors.red.shade800) return 'Dark Red - Abscess';
    if (color == Colors.blue.shade800) return 'Dark Blue - Crack/Fracture';
    if (color == Colors.teal) return 'Teal - Root Canal';
    if (color == Colors.black) return 'Black - Extraction';
    if (color == Colors.cyan) return 'Cyan - Whitening';
    if (color == Colors.indigo) return 'Indigo - Orthodontic';
    if (color == Colors.amber) return 'Amber - Prosthetic';

    return 'Unknown';
  }

  // Get all unique colors used in predictions
  static List<Color> getUniqueColors(List<String> classes) {
    final Set<Color> uniqueColors = {};
    for (final className in classes) {
      uniqueColors.add(getColorForClass(className));
    }
    return uniqueColors.toList();
  }

  // Get color legend items with class names
  static List<MapEntry<Color, String>> getColorLegendItems(
      List<String> classes) {
    final Map<Color, String> colorMap = {};
    for (final className in classes) {
      final color = getColorForClass(className);
      if (!colorMap.containsKey(color)) {
        colorMap[color] = className;
      }
    }
    return colorMap.entries.toList();
  }
}
