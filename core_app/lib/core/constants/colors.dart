import 'package:flutter/material.dart';

class ThemeColors {
  static const Color primary = Color(0xFF2298F2);
  static const Color primaryDark = Color(0xFF1E96E6);
  static const Color primaryDarker = Color(0xFF156DBF);
  static const Color primaryLight = Color(0xFF91CCFA);
  static const Color primaryLighter = Color(0xFFE3F2FC);
  // background
  static const Color bg = Colors.white;
  static const Color bgDark = Color(0xFFEDF2F5);
  // card
  static const Color card = Color(0xFF316F9E);
  static const Color cardLight = Color(0xFF3879AB);
  static const Color cardDark = Color(0xFF286594);
  // side nav
  static const Color sideNav = Colors.white;
  // text
  static const Color text = Color(0xFF121D26);
  static const Color textLight = Colors.white;
  static const Color notion = Color(0xFF969FA6);
  // button/icons
  static const Color iconBG = Color(0xFF2D5A8C);
  // success
  static const Color success = Color(0xFF69F0AE);
  static const Color successLight = Color(0xFF69F0AE);
  static const Color successLighter = Color(0xFFB9F6CA);
  static const Color successDark = Color(0xFF00C853);
  // warning
  static const Color warning = Color(0xFFFFE57F);
  static const Color warningLight = Color(0xFFB9F6CA);
  static const Color warningDark = Color(0xFFFFC107);
  // error
  static const Color error = Color(0xFFF44336);
  static const Color errorLight = Color(0xFFEF9A9A);
  static const Color errorDark = Color(0xFFC62828);
}
