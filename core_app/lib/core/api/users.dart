import 'package:core_app/services/api.dart';
import 'package:core_app/core/models/clinic.dart';
import 'package:core_app/core/models/user.dart';

typedef UserMeRepo = ({
  UserModel user,
  ClinicModel clinic,
  PatientNotificationConfigModel patientNotificationConfig,
});

class UsersAPI {
  static Future<UserMeRepo> me() async {
    final DioRes response = await ApiService.get('/users/me');
    return (
      user: UserModel.from<PERSON>son(response.data!['user']),
      clinic: ClinicModel.fromJson(response.data!['clinic']),
      patientNotificationConfig: PatientNotificationConfigModel.fromJson(
          response.data!['clinicPatientNotificationConfig']),
    );
  }

  static Future<void> updateMe(String? pwd, String? fcmToken) async {
    await ApiService.patch('/users/me', data: {
      if (pwd != null) 'password': pwd,
      if (fcmToken != null) 'fcmToken': fcmToken,
    });
  }

  static Future<List<UserModel>> list() async {
    final DioRes response = await ApiService.get('/users');
    var users = (response.data!['users'] as List)
        .map<UserModel>((e) => UserModel.fromJson(e))
        .toList();
    return users;
  }

  static Future<UserModel> create({
    required String username,
    required String name,
    required String password,
    required String phoneNumber,
    required String role,
    required bool isDentist,
    required double percentage,
    double? hourlyRate,
  }) async {
    final DioRes response = await ApiService.post(
      '/users',
      data: {
        "phoneNumber": phoneNumber,
        "username": username,
        "name": name,
        "password": password,
        "role": role,
        "isDentist": isDentist,
        "percentage": percentage,
        if (hourlyRate != null) "hourlyRate": hourlyRate,
      },
    );
    return UserModel.fromJson(response.data!['user']);
  }

  static Future<UserModel> update(
    String id, {
    String? username,
    String? name,
    String? password,
    String? phoneNumber,
    String? role,
    bool? isDentist,
    String? fcmToken,
    double? percentage,
    double? hourlyRate,
  }) async {
    final DioRes response = await ApiService.patch(
      '/users/$id',
      data: {
        if (phoneNumber != null) "phoneNumber": phoneNumber,
        if (username != null) "username": username,
        if (name != null) "name": name,
        if (role != null) "role": role,
        if (isDentist != null) "isDentist": isDentist,
        if (fcmToken != null) "fcmToken": fcmToken,
        if (percentage != null) "percentage": percentage,
        if (hourlyRate != null) "hourlyRate": hourlyRate,
      },
    );
    return UserModel.fromJson(response.data!['user']);
  }

  static Future<void> delete(String id) async {
    await ApiService.delete('/users/$id');
  }
}
