import 'package:core_app/core/models/timesheet.dart';
import 'package:core_app/core/models/timesheet_summary.dart';
import 'package:core_app/services/api.dart';

class TimesheetAPI {
  static Future<TimesheetSummaryModel> list({
    String? dentistId,
    DateTime? startDate,
    DateTime? endDate,
    int page = 1,
    int limit = 50,
  }) async {
    final DioRes response = await ApiService.get(
      '/timesheet/entries',
      queryParameters: {
        "page": page,
        "limit": limit,
        if (dentistId != null) "dentistId": dentistId,
        if (startDate != null) "startDate": startDate.toUtc().toIso8601String(),
        if (endDate != null) "endDate": endDate.toUtc().toIso8601String(),
      },
    );

    // Check if the response contains the summary data
    if (response.data!.containsKey('summary')) {
      return TimesheetSummaryModel(
        entries: (response.data!['entries'] as List)
            .map<TimesheetEntryModel>((e) => TimesheetEntryModel.fromJson(e))
            .toList(),
        totalHours: (response.data!['summary']['totalHours'] as num).toDouble(),
        totalSalary:
            (response.data!['summary']['totalSalary'] as num).toDouble(),
      );
    } else {
      // Fallback for backward compatibility
      final entries = (response.data!['entries'] as List)
          .map<TimesheetEntryModel>((e) => TimesheetEntryModel.fromJson(e))
          .toList();

      // Calculate total hours manually
      double totalHours = 0;
      for (var entry in entries) {
        if (entry.endTime != null) {
          final duration = entry.endTime!.difference(entry.startTime);
          totalHours += duration.inMinutes / 60;
        }
      }

      return TimesheetSummaryModel(
        entries: entries,
        totalHours: totalHours,
        totalSalary: 0, // Default to 0 if not provided by the API
      );
    }
  }

  static Future<TimesheetEntryModel> create({
    required String userId,
    required String branchId,
    required DateTime startTime,
    DateTime? endTime,
  }) async {
    final DioRes response = await ApiService.post(
      '/timesheet/entries',
      data: {
        "user": userId,
        "startAt": startTime.toUtc().toIso8601String(),
        if (endTime != null) "endAt": endTime.toUtc().toIso8601String(),
        "branchId": branchId,
      },
    );
    return TimesheetEntryModel.fromJson(response.data!['timesheetEntry']);
  }

  static Future<TimesheetEntryModel> update(
    String id, {
    DateTime? startTime,
    DateTime? endTime,
  }) async {
    final DioRes response = await ApiService.patch(
      '/timesheet/entries/$id',
      data: {
        if (startTime != null) "startAt": startTime.toUtc().toIso8601String(),
        if (endTime != null) "endAt": endTime.toUtc().toIso8601String(),
      },
    );
    return TimesheetEntryModel.fromJson(response.data!['timesheetEntry']);
  }

  static Future<void> delete(String id) async {
    await ApiService.delete('/timesheet/entries/$id');
  }
}
