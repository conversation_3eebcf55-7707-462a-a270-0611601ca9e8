import 'package:core_app/services/api.dart';

Future clinicUpdate({
  String? displayName,
  String? language,
  int? daysPrior,
  bool? includeLocation,
}) async {
  await ApiService.patch(
    '/clinics',
    data: {
      if (displayName != null) "displayName": displayName,
      'patientNotificationConfig': {
        if (language != null) 'language': language,
        if (daysPrior != null) 'daysPrior': daysPrior,
        if (includeLocation != null) 'includeLocation': includeLocation,
      }
    },
  );
}
