import 'package:core_app/core/models/expense.dart';
import 'package:core_app/core/utils/datetime.dart';
import 'package:core_app/services/api.dart';

class ExpensesAPI {
  static Future<List<ExpenseModel>> list({
    int page = 1,
    String? branchId,
    DateTime? from,
    DateTime? to,
  }) async {
    final Map<String, dynamic> queryParams = {"page": page};

    if (branchId != null) {
      queryParams["branchId"] = branchId;
    }

    if (from != null) {
      queryParams["from"] = from.toDate();
    }

    if (to != null) {
      queryParams["to"] = to.toDate();
    }

    final DioRes response =
        await ApiService.get("/expenses", queryParameters: queryParams);
    return (response.data!['expenses'] as List)
        .map((e) => ExpenseModel.fromJson(e))
        .toList();
  }

  static Future<ExpenseModel> create(
    double amount,
    String description,
    String type, {
    String? branchId,
  }) async {
    final Map<String, dynamic> data = {
      "description": description,
      "type": type,
      "amount": amount,
    };

    if (branchId != null) {
      data["branchId"] = branchId;
    }

    final DioRes response = await ApiService.post("/expenses", data: data);

    return ExpenseModel.fromJson(response.data!['expense']);
  }

  static Future<ExpenseModel> update(
    String id,
    double amount,
    String description,
    String type,
  ) async {
    final DioRes response = await ApiService.put("/expenses/$id", data: {
      "description": description,
      "type": type,
      "amount": amount,
    });

    return ExpenseModel.fromJson(response.data!['expense']);
  }
}
