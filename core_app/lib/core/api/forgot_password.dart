import 'package:core_app/services/api.dart';

class ForgotPasswordAPI {
  static Future<String> requestPasswordReset({
    required String clinicName,
    required String username,
  }) async {
    final response = await ApiService.post(
      '/forgot-password',
      data: {
        'clinicName': clinicName,
        'username': username,
      },
    );

    return response.data?['code'] as String;
  }

  static Future<String> verifyOTP({
    required String clinicName,
    required String username,
    required String otp,
  }) async {
    final response = await ApiService.post(
      '/verify-otp',
      data: {
        'clinicName': clinicName,
        'username': username,
        'otp': otp,
      },
    );

    return response.data?['token'] as String;
  }

  static Future<void> resetPassword({
    required String token,
    required String newPassword,
  }) async {
    await ApiService.post(
      '/reset-password',
      data: {
        'token': token,
        'newPassword': newPassword,
      },
    );
  }
}
