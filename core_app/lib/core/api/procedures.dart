import 'package:core_app/core/models/procedure.dart';
import 'package:core_app/core/models/inventory_link.dart';
import 'package:core_app/services/api.dart';

class ProcedureTemplatesAPI {
  static Future<List<ProcedureTemplateModel>> list() async {
    final DioRes response = await ApiService.get('/procedure-templates');
    return (response.data!['procedureTemplates'] as List)
        .map<ProcedureTemplateModel>((e) => ProcedureTemplateModel.fromJson(e))
        .toList();
  }

  static Future<ProcedureTemplateModel> create({
    required String specialityId,
    required String procedure,
    required double price,
    required bool toothRemoved,
    required bool crown,
    required bool endo,
    required bool implant,
    required String operative,
  }) async {
    final DioRes response = await ApiService.post(
      '/procedure-templates',
      data: {
        "procedure": procedure,
        "specialityId": specialityId,
        "price": price,
        "toothRemoved": toothRemoved,
        "crown": crown,
        "endo": endo,
        "implant": implant,
        "operative": operative,
      },
    );
    return ProcedureTemplateModel.fromJson(response.data!['procedure']);
  }

  static Future<ProcedureTemplateModel> edit(
    String id, {
    required String procedure,
    required double price,
    required bool toothRemoved,
    required bool crown,
    required bool endo,
    required bool implant,
    required String operative,
  }) async {
    final DioRes response = await ApiService.patch(
      '/procedure-templates/$id',
      data: {
        "procedure": procedure,
        "price": price,
        "toothRemoved": toothRemoved,
        "crown": crown,
        "endo": endo,
        "implant": implant,
        "operative": operative,
      },
    );
    return ProcedureTemplateModel.fromJson(response.data!['procedure']);
  }

  static Future<void> delete(String id) async {
    await ApiService.delete('/procedure-templates/$id');
  }
}

class ProceduresAPI {
  /// limit per page = 20
  static Future<List<VisitProcedureModel>> list({
    int page = 1,
    int perPage = 20,
    String? patientId,
  }) async {
    final DioRes response = await ApiService.get(
      '/visits',
      queryParameters: {
        "page": page,
        "count": perPage,
        if (patientId != null) "patientId": patientId,
      },
    );
    return response.data!['visits']
        .map<VisitProcedureModel>((e) => VisitProcedureModel.fromJson(e))
        .toList();
  }

  static Future<VisitProcedureModel> create(
    String visitId, {
    required String dentstId,
    required String toothNumber,
    required String speciality,
    required String procedure,
    required String nextVisit,
    required String procedureTemplatId,
    required double finalPrice,
    required bool? toothRemoved,
    required String notes,
    List<InventoryConsumption>? inventoryConsumptions,
  }) async {
    final data = {
      "dentistId": dentstId,
      "toothNumber": toothNumber,
      "speciality": speciality,
      "procedure": procedure,
      "nextVisit": nextVisit,
      "procedureTemplatId": procedureTemplatId,
      "finalPrice": finalPrice,
      "toothRemoved": toothRemoved,
      "notes": notes,
    };

    if (inventoryConsumptions != null && inventoryConsumptions.isNotEmpty) {
      data["inventoryConsumptions"] =
          inventoryConsumptions.map((e) => e.toJson()).toList();
    }

    final DioRes response =
        await ApiService.post('/visits/$visitId/procedures', data: data);

    return VisitProcedureModel.fromJson(response.data!['procedure']);
  }
}
