import 'package:core_app/core/models/insurance.dart';
import 'package:core_app/services/api.dart';

typedef ClaimItemCreate = ({
  String treatment,
  double price,
  String status,
  double percentageCoverage,
});

class InsurancesAPI {
  static Future<List<InsuranceCompanyModel>> listCompanies() async {
    final DioRes response = await ApiService.get("/insurance-companies");
    return (response.data!['insuranceCompanies'] as List)
        .map((e) => InsuranceCompanyModel.fromJson(e))
        .toList();
  }

  static Future<InsuranceCompanyModel> createCompany({
    required String name,
    required double defaultPercentageCoverage,
  }) async {
    final DioRes response = await ApiService.post("/insurance-companies", data: {
      "name": name,
      "defaultPercentageCoverage": defaultPercentageCoverage,
    });
    return InsuranceCompanyModel.fromJson(response.data!['insuranceCompany']);
  }

  static Future<InsuranceCompanyModel> updateCompany({
    required String id,
    required String name,
    required double defaultPercentageCoverage,
  }) async {
    final DioRes response = await ApiService.patch("/insurance-companies/$id", data: {
      "name": name,
      "defaultPercentageCoverage": defaultPercentageCoverage,
    });
    return InsuranceCompanyModel.fromJson(response.data!['insuranceCompany']);
  }

  static Future<void> deleteCompany({
    required String id,
  }) async {
    await ApiService.delete("/insurance-companies/$id");
  }

  static Future<List<InsuranceClaimModel>> listClaims(String companyId) async {
    final DioRes response = await ApiService.get("/insurance-companies/$companyId/claims");
    return (response.data!['claims'] as List).map((e) => InsuranceClaimModel.fromJson(e)).toList();
  }

  static Future<(InsuranceClaimModel, List<ClaimItemModel>)> createClaim({
    required String visitId,
    required String companyId,
    required List<ClaimItemCreate> items,
  }) async {
    final DioRes response = await ApiService.post("/insurance-companies/$companyId/claims", data: {
      "visitId": visitId,
      "items": items
          .map((e) => {
                "treatment": e.treatment,
                "price": e.price,
                "status": e.status,
                "percentageCoverage": e.percentageCoverage,
              })
          .toList(),
    });
    return (
      InsuranceClaimModel.fromJson(response.data!['claim']),
      (response.data!['items'] as List).map((e) => ClaimItemModel.fromJson(e)).toList()
    );
  }

  static Future<(InsuranceClaimModel, List<ClaimItemModel>)> updateClaim({
    required String id,
    required String companyId,
    required List<ClaimItemCreate> items,
  }) async {
    final DioRes response =
        await ApiService.put("/insurance-companies/$companyId/claims/$id", data: {
      "items": items
          .map((e) => {
                "treatment": e.treatment,
                "price": e.price,
                "status": e.status,
                "percentageCoverage": e.percentageCoverage,
              })
          .toList(),
    });
    return (
      InsuranceClaimModel.fromJson(response.data!['claim']),
      (response.data!['items'] as List).map((e) => ClaimItemModel.fromJson(e)).toList()
    );
  }

  static Future<void> deleteClaim({
    required String id,
    required String companyId,
  }) async {
    await ApiService.delete("/insurance-companies/$companyId/claims/$id");
  }

  static Future<(InsuranceClaimModel, List<ClaimItemModel>)> listClaimItems(
      String companyId, String claimId) async {
    final DioRes response = await ApiService.get("/insurance-companies/$companyId/claims/$claimId");
    return (
      InsuranceClaimModel.fromJson(response.data!['claim']),
      (response.data!['items'] as List).map((e) => ClaimItemModel.fromJson(e)).toList()
    );
  }
}
