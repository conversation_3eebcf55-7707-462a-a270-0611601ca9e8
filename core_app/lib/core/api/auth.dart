import 'package:core_app/services/api.dart';

class AuthAPI {
  static Future<String> login({
    required String username,
    required String password,
    required String clinicName,
  }) async {
    final response = await ApiService.post(
      '/login',
      data: {
        "clinicName": clinicName,
        "username": username,
        "password": password,
      },
    );
    return (response.data!['token'] as String);
  }
}
