import 'package:core_app/core/models/lab.dart';
import 'package:core_app/core/utils/datetime.dart';
import 'package:core_app/services/api.dart';

class LabRequestsAPI {
  static Future<List<LabRequestModel>> list(String? labId, [int page = 1]) async {
    final response = await ApiService.get('/clinic-lab-requests', queryParameters: {
      if (labId != null) 'labId': labId,
      'page': page,
    });
    return response.data!['labRequests']
        .map<LabRequestModel>((e) => LabRequestModel.fromJson(e))
        .toList();
  }

  static Future<LabRequestModel> create({
    required List<String> miscellaneous,
    required double? price,
    required String patientId,
    required String? notes,
    required String labId,
    required String? nextStep,
    required String? ponticDesign,
    required String? implantWork,
    required String? zro2,
    required String? threeDPrinting,
    required String? emax,
    required String? customAbutment,
    required String? pmma,
    required String? fullArchImplant,
    required String? sendingBranchId,
    required String? receivingBranchId,
    required DateTime? expectedDeliveryDate,
    required DateTime? sendDate,
    required List<ShadePoint> shadeDetails,
  }) async {
    final response = await ApiService.post('/clinic-lab-requests', data: {
      'price': price,
      'notes': notes,
      'labId': labId,
      'patientId': patientId,
      'shadeDetails': shadeDetails.toList(),
      'nextStep': nextStep,
      'ponticDesign': ponticDesign,
      'implantWork': implantWork,
      'zro2': zro2,
      '3dPrinting': threeDPrinting,
      'fullArchImplant': fullArchImplant,
      'emax': emax,
      'pmma': pmma,
      'customAbutment': customAbutment,
      'sendingBranchId': sendingBranchId,
      'receivingBranchId': receivingBranchId,
      'expectedDeliveryDate': expectedDeliveryDate?.toDate(),
      'sendDate': sendDate?.toDate(),
      'misc': miscellaneous,
    });
    return LabRequestModel.fromJson(response.data!['labRequest']);
  }

  static Future<LabRequestModel> update(
    String id, {
    required List<String> miscellaneous,
    required double price,
    required String patientId,
    required String notes,
    required String labId,
    required String nextStep,
    required String ponticDesign,
    required String implantWork,
    required String zro2,
    required String threeDPrinting,
    required String emax,
    required String customAbutment,
    required String pmma,
    required String fullArchImplant,
    required String sendingBranchId,
    required String receivingBranchId,
    required DateTime expectedDeliveryDate,
    required DateTime sendDate,
    required DateTime? actualDeliveryDate,
    required List<ShadePoint> shadeDetails,
  }) async {
    final response = await ApiService.patch('/clinic-lab-requests/$id', data: {
      'price': price,
      'notes': notes,
      'labId': labId,
      'patientId': patientId,
      'shadeDetails': shadeDetails.toList(),
      'nextStep': nextStep,
      'ponticDesign': ponticDesign,
      'implantWork': implantWork,
      'zro2': zro2,
      if (actualDeliveryDate != null) 'actualDeliveryDate': actualDeliveryDate.toDate(),
      '3dPrinting': threeDPrinting,
      'fullArchImplant': fullArchImplant,
      'emax': emax,
      'pmma': pmma,
      'customAbutment': customAbutment,
      'sendingBranchId': sendingBranchId,
      'receivingBranchId': receivingBranchId,
      'expectedDeliveryDate': expectedDeliveryDate.toDate(),
      'sendDate': sendDate.toDate(),
      'misc': miscellaneous,
    });

    return LabRequestModel.fromJson(response.data!['labRequest']);
  }

  static Future<void> delete(String id) async {
    await ApiService.delete('/clinic-lab-requests/$id');
  }
}
