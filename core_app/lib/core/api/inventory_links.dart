import 'package:core_app/core/models/inventory_link.dart';
import 'package:core_app/services/api.dart';

class InventoryLinksAPI {
  static Future<List<InventoryLink>> list(String procedureTemplateId) async {
    final DioRes response = await ApiService.get('/procedure-templates/$procedureTemplateId/inventory-links');
    return (response.data!['links'] as List)
        .map<InventoryLink>((e) => InventoryLink.fromJson(e))
        .toList();
  }

  static Future<InventoryLink> create(
    String procedureTemplateId, {
    required String itemId,
    required int quantity,
  }) async {
    final DioRes response = await ApiService.post(
      '/procedure-templates/$procedureTemplateId/inventory-links',
      data: {
        "itemId": itemId,
        "quantity": quantity,
      },
    );
    return InventoryLink.fromJson(response.data!['link']);
  }

  static Future<InventoryLink> update(
    String procedureTemplateId,
    String linkId, {
    required int quantity,
  }) async {
    final DioRes response = await ApiService.patch(
      '/procedure-templates/$procedureTemplateId/inventory-links/$linkId',
      data: {
        "quantity": quantity,
      },
    );
    return InventoryLink.from<PERSON><PERSON>(response.data!['link']);
  }

  static Future<void> delete(String procedureTemplateId, String linkId) async {
    await ApiService.delete('/procedure-templates/$procedureTemplateId/inventory-links/$linkId');
  }
}
