import 'package:core_app/core/models/payment.dart';
import 'package:core_app/services/api.dart';

class CustomPaymentsAPI {
  static Future<CustomPaymentModel> create({
    required String name,
    required double percentageFee,
    required double flatFee,
  }) async {
    final DioRes response = await ApiService.post('/custom-payment-methods', data: {
      'name': name,
      'percentageFee': percentageFee,
      'flatFee': flatFee,
    });
    return CustomPaymentModel.fromJson(response.data!['customPaymentMethod']);
  }

  static Future<CustomPaymentModel> update({
    required String id,
    required String name,
    required double percentageFee,
    required double flatFee,
  }) async {
    final DioRes response = await ApiService.put('/custom-payment-methods/$id', data: {
      'name': name,
      'percentageFee': percentageFee,
      'flatFee': flatFee,
    });
    return CustomPaymentModel.fromJson(response.data!['customPaymentMethod']);
  }

  static Future delete({required String id}) async {
    await ApiService.delete('/custom-payment-methods/$id');
  }

  static Future<List<CustomPaymentModel>> list() async {
    final DioRes response = await ApiService.get('/custom-payment-methods');
    return (response.data!['customPaymentMethods'] as List)
        .map((e) => CustomPaymentModel.fromJson(e))
        .toList();
  }
}
