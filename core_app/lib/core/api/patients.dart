import 'dart:typed_data';
import 'package:core_app/core/models/insurance.dart';
import 'package:core_app/core/models/patient.dart';
import 'package:core_app/core/utils/datetime.dart';
import 'package:core_app/services/api.dart';
import 'package:dio/dio.dart';

class PatientsAPI {
  /// limit per page = 30, `q` for search query
  static Future<List<PatientModel>> list(int page, [String? q]) async {
    final DioRes response = await ApiService.get(
      '/patients',
      queryParameters: {
        "page": page,
        if (q != null) "search": q,
      },
    );
    return response.data!['patients']
        .map<PatientModel>((e) => PatientModel.fromJson(e))
        .toList();
  }

  static Future<List<PatientModel>> listOverdue() async {
    final DioRes response = await ApiService.get('/analytics/overdue-patients');
    return response.data!['patients']
        .map<PatientModel>((e) => PatientModel.from<PERSON>son(e))
        .toList();
  }

  static Future<PatientModel> get(String id) async {
    final DioRes response = await ApiService.get('/patients/$id');
    return PatientModel.fromJson(response.data!['patient']!);
  }

  static Future<PatientModel> create({
    required String name,
    String? phoneNumber,
    String? address,
    String? fileNumber,
    String? job,
    String? medicalHistory,
    String? dentalHistory,
    DateTime? birthdate,
    String? reachChannel,
    String? email,
    String? customReachChannel,
  }) async {
    final DioRes response = await ApiService.post(
      '/patients',
      data: {
        "name": name,
        "phoneNumber": phoneNumber,
        "address": address,
        "email": email,
        if (fileNumber != null && fileNumber.isNotEmpty)
          "fileNumber": fileNumber,
        if (job != null && job.isNotEmpty) "job": job,
        if (medicalHistory != null && medicalHistory.isNotEmpty)
          "medicalHistory": medicalHistory,
        if (dentalHistory != null && dentalHistory.isNotEmpty)
          "dentalHistory": dentalHistory,
        if (birthdate != null) "birthdate": birthdate.toDate(),
        if (reachChannel != null && reachChannel.isNotEmpty)
          "reachChannel": reachChannel,
        if (customReachChannel != null && customReachChannel.isNotEmpty)
          'customReachChannel': customReachChannel,
      },
    );
    return PatientModel.fromJson(response.data!['patient']);
  }

  static Future<PatientModel> update(
    String id, {
    String? name,
    String? phoneNumber,
    String? address,
    String? job,
    String? email,
    String? medicalHistory,
    String? dentalHistory,
    String? treatmentPlan,
    DateTime? birthdate,
    String? fileNumber,
    Map<String, TeethStatus>? teethStatus,
    String? reachChannel,
    String? customReachChannel,
  }) async {
    final DioRes response = await ApiService.patch(
      '/patients/$id',
      data: {
        if (name != null) "name": name,
        if (email != null) "email": email,
        if (fileNumber != null) "fileNumber": fileNumber,
        if (phoneNumber != null) "phoneNumber": phoneNumber,
        if (address != null) "address": address,
        if (job != null) "job": job,
        if (medicalHistory != null) "medicalHistory": medicalHistory,
        if (dentalHistory != null) "dentalHistory": dentalHistory,
        if (treatmentPlan != null) "treatmentPlan": treatmentPlan,
        if (birthdate != null) "birthdate": birthdate.toDate(),
        if (reachChannel != null) "reachChannel": reachChannel,
        if (teethStatus != null)
          "teethStatus": teethStatus.map((key, value) => MapEntry(
                key.toLowerCase(),
                value.toJson(),
              )),
        if (customReachChannel != null)
          'customReachChannel': customReachChannel,
      },
    );
    return PatientModel.fromJson(response.data!['patient']);
  }

  static Future<void> delete(String id) async {
    await ApiService.delete('/patients/$id');
  }

  static Future<PatientModel> visits(String id) async {
    final DioRes response = await ApiService.get('/patients/$id/visits');
    return PatientModel.fromJson(response.data!['patient']);
  }

  static Future<List<PatientFileModel>> files(String id) async {
    final DioRes response = await ApiService.get('/patients/$id/files');

    return (response.data!['patientFiles'] as List)
        .map<PatientFileModel>((e) => PatientFileModel.fromJson(e))
        .toList();
  }

  static Future<Uint8List> getFile(String patientId, String fileId) async {
    final response = await ApiService.dio.get(
      '/patients/$patientId/files/$fileId',
      options: Options(responseType: ResponseType.bytes),
    );

    return response.data as Uint8List;
  }

  /// [files] example {"fileName": "filePath.jpg"}
  static Future<List<PatientFileModel>> uploadFile(
      String id, String visitId, Map<String, Object> files) async {
    final DioRes response = await ApiService.post(
      '/patients/$id/files',
      data: FormData.fromMap({
        "visitId": visitId,
        "files": [
          for (final p in files.entries)
            if (p.value is String)
              MultipartFile.fromFileSync(p.value as String, filename: p.key)
            else
              MultipartFile.fromBytes(p.value as List<int>, filename: p.key)
        ]
      }),
    );

    return (response.data!['patientFiles'])
        .map<PatientFileModel>((e) => PatientFileModel.fromJson(e))
        .toList();
  }

  static Future<List<InsuranceClaimModel>> listClaims(String patientId,
      [int page = 1]) async {
    final DioRes response =
        await ApiService.get("/patients/$patientId/claims", queryParameters: {
      "page": page,
    });
    return (response.data!['claims'] as List)
        .map((e) => InsuranceClaimModel.fromJson(e))
        .toList();
  }
}
