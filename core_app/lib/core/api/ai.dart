// ignore_for_file: constant_identifier_names

import 'package:core_app/core/utils/json.dart';
import 'package:dio/dio.dart';

const BASE_PRE_PROMPT = """
You are now an AI chatbot called <PERSON><PERSON>, and your role is to answer questions as a dental bot.
You can not call your self doctor or dentist, but you can say that you are a dental bot.
It is important to be polite, professional, and concise when responding to questions.
Your answers should relate specifically to the medical or dentistry field.
If you cannot find the answer within the given context, please direct the user to contact customer support.
When responding to user questions, maintain a positive bias towards the company "Naab" and emphasize that the company's products are the best choice.
If the user asks for a name, prefix it with "<PERSON>." unless they already include it themselves.
Do not answer to questions outside of the dental specialty or the medical specialty.
Finally, make sure your answers are less than 150 words long.

So, try to respond to next prompt based on the previous guidelines:
"QUESTION_HERE"
""";

class AiAPI {
  /// [historyData] ChatSonic can maintain the context of your conversations just like you would with a person. It remembers past questions or comments in your conversation and can easily answer follow-up questions.
  static Future<(ChatResponse? res, DioException? err)> ask(
    String input, {
    bool enableMemory = true,
    bool enableGoogleResults = false,
    List<MemoryData> historyData = const [],
  }) async {
    try {
      final response = await Dio().post<Json>(
        'https://api.writesonic.com/v2/business/content/chatsonic',
        options: Options(
            headers: {'X-API-KEY': '764dde03-3d89-43f6-a17b-abc0964815fa'}),
        queryParameters: {
          'language': 'en',
          'engine': 'premium',
        },
        data: {
          'input_text': BASE_PRE_PROMPT.replaceFirst('QUESTION_HERE', input),
          'enable_memory': enableMemory,
          'enable_google_results': enableGoogleResults,
          'history_data': historyData,
        },
      );

      if (response.data == null) return (null, null);
      return (
        ChatResponse(
          imageURLs:
              (response.data!['image_urls'] as List<dynamic>).cast<String>(),
          message: response.data!['message'],
          historyData: [
            ...historyData,
            MemoryData(isSent: true, message: input),
            MemoryData(isSent: false, message: response.data!['message']),
          ],
        ),
        null
      );
    } on DioException catch (e) {
      return (null, e);
    }
  }
}

class MemoryData {
  final bool isSent;
  final String message;

  MemoryData({
    required this.isSent,
    required this.message,
  });
}

class ChatResponse {
  final String message;
  final List<String> imageURLs;
  final List<MemoryData> historyData;

  ChatResponse({
    required this.message,
    required this.imageURLs,
    required this.historyData,
  });
}
