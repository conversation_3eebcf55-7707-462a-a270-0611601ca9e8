import 'package:core_app/services/api.dart';

class CommunicationAPI {
  static Future<String> sendSMS({
    required bool all,
    required List<String> recipients,
    required String message,
  }) async {
    final res = await ApiService.post(
      '/communication/sms',
      data: {
        'all': all,
        'recipients': recipients,
        'message': message,
      },
    );

    return res.data![
        'message']; // strconv.Itoa(counter) + " SMS(s) sent successfully"
  }

  /// Fetches the birthday wish configuration
  static Future<String?> getBirthdayWish() async {
    try {
      final response = await ApiService.get('/communication/birthday_wish');
      final birthdayWish = response.data?['birthday_wish'] as String?;

      // Return null for empty strings to be consistent
      if (birthdayWish == null || birthdayWish.isEmpty) {
        return null;
      }

      return birthdayWish;
    } catch (e) {
      // If the configuration doesn't exist yet, return null
      return null;
    }
  }

  /// Toggles the birthday wish settings
  static Future<String> toggleBirthdayWish({
    required bool enabled,
    required String language,
  }) async {
    final response = await ApiService.post(
      '/communication/birthday_wish/toggle',
      data: {
        'enabled': enabled,
        'language': language,
      },
    );

    return response.data?['message'] ?? 'Birthday wish settings updated';
  }
}
