import 'package:core_app/services/api.dart';

typedef BillingState = ({
  double bill,
  String currency,
  int visitsCount,
});

class BillingAPI {
  static Future<BillingState> current() async {
    final DioRes response = await ApiService.get('/billing/current');
    return (
      bill: (response.data!['bill'] as num).toDouble(),
      currency: response.data!['currency'] as String,
      visitsCount: response.data!['visitsCount'] as int,
    );
  }
}
