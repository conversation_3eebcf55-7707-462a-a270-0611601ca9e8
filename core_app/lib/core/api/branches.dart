import 'package:core_app/services/api.dart';
import 'package:core_app/core/models/branch.dart';

class BranchesAPI {
  static Future<List<BranchModel>> list() async {
    final DioRes response = await ApiService.get('/branches');
    return (response.data!['branches']! as List)
        .map<BranchModel>((e) => BranchModel.fromJson(e))
        .toList();
  }

  static Future<BranchModel> create({
    required String name,
    String? hexColor,
    double? longitude,
    double? latitude,
    int rooms = 1,
  }) async {
    final DioRes response = await ApiService.post(
      '/branches',
      data: {
        "name": name,
        'color': hexColor,
        'rooms': rooms,
        if (longitude != null && latitude != null) "longitude": longitude,
        if (longitude != null && latitude != null) "latitude": latitude,
      },
    );
    return BranchModel.fromJson(response.data!['branch']);
  }

  static Future<BranchModel> update(
    String id, {
    required String name,
    required String? hexColor,
    required double? longitude,
    required double? latitude,
    required int rooms,
  }) async {
    final DioRes response = await ApiService.patch(
      '/branches/$id',
      data: {
        "name": name,
        "rooms": rooms,
        'color': hexColor,
        "longitude": longitude,
        "latitude": latitude,
      },
    );
    return BranchModel.fromJson(response.data!['branch']);
  }

  static Future<void> delete(String id) async {
    await ApiService.delete('/branches/$id');
  }
}
