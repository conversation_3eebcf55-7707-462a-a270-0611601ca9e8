import 'package:core_app/core/models/appointment.dart';
import 'package:core_app/core/utils/datetime.dart';
import 'package:core_app/core/utils/json.dart';
import 'package:core_app/services/api.dart';

class AppointmentsAPI {
  static Future<List<AppointmentModel>> list({
    String? dentistId,
    String? branchId,
    String? patientId,
    DateTime? from,
    DateTime? to,
  }) async {
    final DioRes response = await ApiService.get('/appointments', queryParameters: {
      if (branchId != null) "branchId": branchId,
      if (dentistId != null) "dentistId": dentistId,
      if (patientId != null) "patientId": patientId,
      if (from != null) "from": from.toDate(),
      if (to != null) "to": to.toDate(),
    });
    return ((response.data!['appointments'] as List).cast<Json>())
        .map<AppointmentModel>((e) => AppointmentModel.fromJson(e))
        .toList();
  }

  static Future<AppointmentModel> create({
    required String patientId,
    required DateTime startTime,
    required DateTime endTime,
    required String branchId,
    required String dentistId,
    required int room,
  }) async {
    final DioRes response = await ApiService.post(
      '/appointments',
      data: {
        "patientId": patientId,
        "startTime": startTime.toUtc().toIso8601String(),
        "endTime": endTime.toUtc().toIso8601String(),
        "branchId": branchId,
        "room": room,
        "dentistId": dentistId,
      },
    );
    final e = response.data!;
    return AppointmentModel.fromJson(e['appointment']);
  }

  static Future<void> cancel(String id) async {
    await ApiService.post('/appointments/$id/cancel');
  }
}
