import 'package:core_app/core/utils/datetime.dart';
import 'package:core_app/services/api.dart';
import 'package:dio/dio.dart';
import 'package:json_annotation/json_annotation.dart';

part 'generated/analysis.g.dart';

class AnalyticsAPI {
  static Future<ClinicAnalytics> clinic(DateTime from, DateTime to) async {
    Response res = await ApiService.get('/analytics/clinic', queryParameters: {
      'from': from.toDate(),
      'to': to.toDate(),
    });
    return ClinicAnalytics.fromJson(res.data!);
  }
}

@JsonSerializable()
class ExpensesGroup {
  final double sum;
  final String type;
  ExpensesGroup(this.sum, this.type);
  factory ExpensesGroup.fromJson(Map<String, dynamic> json) => _$ExpensesGroupFromJson(json);
  Map<String, dynamic> toJson() => _$ExpensesGroupToJson(this);
}

@JsonSerializable()
class AppointmentDentistCount {
  final int count;
  final String id;
  final String name;
  AppointmentDentistCount({
    required this.count,
    required this.id,
    required this.name,
  });
  factory AppointmentDentistCount.fromJson(Map<String, dynamic> json) =>
      _$AppointmentDentistCountFromJson(json);
  Map<String, dynamic> toJson() => _$AppointmentDentistCountToJson(this);
}

@JsonSerializable()
class NewPatients {
  final int count;
  NewPatients(this.count);

  factory NewPatients.fromJson(Map<String, dynamic> json) => _$NewPatientsFromJson(json);
  Map<String, dynamic> toJson() => _$NewPatientsToJson(this);
}

@JsonSerializable()
class RecurringPatients {
  final int count;
  RecurringPatients(this.count);
  factory RecurringPatients.fromJson(Map<String, dynamic> json) =>
      _$RecurringPatientsFromJson(json);
  Map<String, dynamic> toJson() => _$RecurringPatientsToJson(this);
}

@JsonSerializable(fieldRename: FieldRename.snake)
class ProcedureTotal {
  final int count;
  final int discountSum;
  final int priceSum;
  final String procedureId;
  final String procedureName;
  final int teethOperated;
  final int totalSum;

  ProcedureTotal(
    this.count,
    this.discountSum,
    this.priceSum,
    this.procedureId,
    this.procedureName,
    this.teethOperated,
    this.totalSum,
  );
  factory ProcedureTotal.fromJson(Map<String, dynamic> json) => _$ProcedureTotalFromJson(json);
  Map<String, dynamic> toJson() => _$ProcedureTotalToJson(this);
}

@JsonSerializable()
class VisitsGroup {
  final int count;
  final DateTime date;
  VisitsGroup(this.count, this.date);
  factory VisitsGroup.fromJson(Map<String, dynamic> json) => _$VisitsGroupFromJson(json);
  Map<String, dynamic> toJson() => _$VisitsGroupToJson(this);
}

@JsonSerializable()
class ReachChannelAnalytics {
  final int count;
  @JsonKey(name: 'channel')
  final String reachChannel;
  ReachChannelAnalytics(this.count, this.reachChannel);
  factory ReachChannelAnalytics.fromJson(Map<String, dynamic> json) =>
      _$ReachChannelAnalyticsFromJson(json);
  Map<String, dynamic> toJson() => _$ReachChannelAnalyticsToJson(this);
}

@JsonSerializable()
class ClinicAnalytics {
  final List<AppointmentDentistCount> appointmentDentistCount;
  final List<ExpensesGroup> expensesGrouped;
  final List<ProcedureTotal> proceduresTotal;
  final List<VisitsGroup> visitsGrouped;
  final NewPatients? newPatients;
  final RecurringPatients? recurringPatients;
  final List<ReachChannelAnalytics>? patientReachChannel;
  const ClinicAnalytics({
    this.appointmentDentistCount = const [],
    this.expensesGrouped = const [],
    required this.newPatients,
    this.proceduresTotal = const [],
    required this.recurringPatients,
    this.visitsGrouped = const [],
    this.patientReachChannel,
  });
  factory ClinicAnalytics.fromJson(Map<String, dynamic> json) => _$ClinicAnalyticsFromJson(json);
  Map<String, dynamic> toJson() => _$ClinicAnalyticsToJson(this);
}
