import 'package:core_app/core/models/procedure.dart';
import 'package:core_app/core/models/speciality.dart';
import 'package:core_app/services/api.dart';

class SpecialitiesAPI {
  static Future<List<SpecialityTemplateModel>> list() async {
    final DioRes response = await ApiService.get('/speciality-templates');
    return (response.data!['specialities'] as List)
        .map<SpecialityTemplateModel>((e) => SpecialityTemplateModel.fromJson(e))
        .toList();
  }

  static Future<SpecialityTemplateModel> create(String speciality) async {
    final DioRes response = await ApiService.post(
      '/speciality-templates',
      data: {"speciality": speciality},
    );
    return SpecialityTemplateModel.fromJson(response.data!['speciality']);
  }

  static Future<SpecialityTemplateModel> edit(String id, String speciality) async {
    final DioRes response = await ApiService.patch(
      '/speciality-templates/$id',
      data: {"speciality": speciality},
    );
    return SpecialityTemplateModel.fromJson(response.data!['speciality']);
  }

  static Future<void> delete(String id) async {
    await ApiService.delete('/speciality-templates/$id');
  }

  static Future<(SpecialityTemplateModel, List<ProcedureTemplateModel>)> get(String id) async {
    final DioRes response = await ApiService.get('/speciality-templates/$id');
    final specialityTemplateRepo = SpecialityTemplateModel.fromJson(response.data!['speciality']);
    final procedures = (response.data!['procedures'] as List)
        .map<ProcedureTemplateModel>((e) => ProcedureTemplateModel.fromJson(e))
        .toList();
    return (specialityTemplateRepo, procedures);
  }
}
