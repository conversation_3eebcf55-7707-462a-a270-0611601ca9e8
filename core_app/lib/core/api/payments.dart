import 'package:core_app/core/models/payment.dart';
import 'package:core_app/core/utils/datetime.dart';
import 'package:core_app/services/api.dart';

class PaymentsAPI {
  /// 20 per page
  static Future<List<PaymentModel>> list({
    int page = 1,
    String? patientId,
    String? branchId,
    String? paymentType,
    DateTime? from,
    DateTime? to,
  }) async {
    final DioRes response = await ApiService.get('/payments', queryParameters: {
      "page": page,
      if (patientId != null) 'patientId': patientId,
      if (branchId != null) 'branchId': branchId,
      if (paymentType != null) 'paymentType': paymentType,
      if (from != null) 'from': from.toDate(),
      if (to != null) 'to': to.toDate(),
    });
    return (response.data!['payments'] as List)
        .map<PaymentModel>((e) => PaymentModel.fromJson(e))
        .toList();
  }

  static Future<PaymentModel> create({
    required String visitId,
    required String patientId,
    required String branchId,
    required double amount,
    required String type,
    required String customPaymentMethodId,
  }) async {
    final DioRes response = await ApiService.post(
      '/payments',
      data: {
        "visitId": visitId,
        "patientId": patientId,
        "branchId": branchId,
        "amount": amount,
        "type": type,
        "customPaymentMethodId": customPaymentMethodId,
      },
    );
    return PaymentModel.fromJson(response.data!['payment']);
  }
}
