// GENERATED CODE - DO NOT MODIFY BY HAND

part of '../analysis.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ExpensesGroup _$ExpensesGroupFromJson(Map<String, dynamic> json) =>
    ExpensesGroup(
      (json['sum'] as num).toDouble(),
      json['type'] as String,
    );

Map<String, dynamic> _$ExpensesGroupToJson(ExpensesGroup instance) =>
    <String, dynamic>{
      'sum': instance.sum,
      'type': instance.type,
    };

AppointmentDentistCount _$AppointmentDentistCountFromJson(
        Map<String, dynamic> json) =>
    AppointmentDentistCount(
      count: (json['count'] as num).toInt(),
      id: json['id'] as String,
      name: json['name'] as String,
    );

Map<String, dynamic> _$AppointmentDentistCountToJson(
        AppointmentDentistCount instance) =>
    <String, dynamic>{
      'count': instance.count,
      'id': instance.id,
      'name': instance.name,
    };

NewPatients _$NewPatientsFromJson(Map<String, dynamic> json) => NewPatients(
      (json['count'] as num).toInt(),
    );

Map<String, dynamic> _$NewPatientsToJson(NewPatients instance) =>
    <String, dynamic>{
      'count': instance.count,
    };

RecurringPatients _$RecurringPatientsFromJson(Map<String, dynamic> json) =>
    RecurringPatients(
      (json['count'] as num).toInt(),
    );

Map<String, dynamic> _$RecurringPatientsToJson(RecurringPatients instance) =>
    <String, dynamic>{
      'count': instance.count,
    };

ProcedureTotal _$ProcedureTotalFromJson(Map<String, dynamic> json) =>
    ProcedureTotal(
      (json['count'] as num).toInt(),
      (json['discount_sum'] as num).toInt(),
      (json['price_sum'] as num).toInt(),
      json['procedure_id'] as String,
      json['procedure_name'] as String,
      (json['teeth_operated'] as num).toInt(),
      (json['total_sum'] as num).toInt(),
    );

Map<String, dynamic> _$ProcedureTotalToJson(ProcedureTotal instance) =>
    <String, dynamic>{
      'count': instance.count,
      'discount_sum': instance.discountSum,
      'price_sum': instance.priceSum,
      'procedure_id': instance.procedureId,
      'procedure_name': instance.procedureName,
      'teeth_operated': instance.teethOperated,
      'total_sum': instance.totalSum,
    };

VisitsGroup _$VisitsGroupFromJson(Map<String, dynamic> json) => VisitsGroup(
      (json['count'] as num).toInt(),
      DateTime.parse(json['date'] as String),
    );

Map<String, dynamic> _$VisitsGroupToJson(VisitsGroup instance) =>
    <String, dynamic>{
      'count': instance.count,
      'date': instance.date.toIso8601String(),
    };

ReachChannelAnalytics _$ReachChannelAnalyticsFromJson(
        Map<String, dynamic> json) =>
    ReachChannelAnalytics(
      (json['count'] as num).toInt(),
      json['channel'] as String,
    );

Map<String, dynamic> _$ReachChannelAnalyticsToJson(
        ReachChannelAnalytics instance) =>
    <String, dynamic>{
      'count': instance.count,
      'channel': instance.reachChannel,
    };

ClinicAnalytics _$ClinicAnalyticsFromJson(Map<String, dynamic> json) =>
    ClinicAnalytics(
      appointmentDentistCount: (json['appointmentDentistCount']
                  as List<dynamic>?)
              ?.map((e) =>
                  AppointmentDentistCount.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      expensesGrouped: (json['expensesGrouped'] as List<dynamic>?)
              ?.map((e) => ExpensesGroup.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      newPatients: json['newPatients'] == null
          ? null
          : NewPatients.fromJson(json['newPatients'] as Map<String, dynamic>),
      proceduresTotal: (json['proceduresTotal'] as List<dynamic>?)
              ?.map((e) => ProcedureTotal.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      recurringPatients: json['recurringPatients'] == null
          ? null
          : RecurringPatients.fromJson(
              json['recurringPatients'] as Map<String, dynamic>),
      visitsGrouped: (json['visitsGrouped'] as List<dynamic>?)
              ?.map((e) => VisitsGroup.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      patientReachChannel: (json['patientReachChannel'] as List<dynamic>?)
          ?.map(
              (e) => ReachChannelAnalytics.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$ClinicAnalyticsToJson(ClinicAnalytics instance) =>
    <String, dynamic>{
      'appointmentDentistCount': instance.appointmentDentistCount,
      'expensesGrouped': instance.expensesGrouped,
      'proceduresTotal': instance.proceduresTotal,
      'visitsGrouped': instance.visitsGrouped,
      'newPatients': instance.newPatients,
      'recurringPatients': instance.recurringPatients,
      'patientReachChannel': instance.patientReachChannel,
    };
