import 'package:core_app/core/models/lab.dart';
import 'package:core_app/services/api.dart';

class LabsAPI {
  static Future<List<LabModel>> list() async {
    final response = await ApiService.get('/clinic-labs');
    return response.data!['labs'].map<LabModel>((e) => LabModel.fromJson(e)).toList();
  }

  static Future<LabModel> create(String name, String phoneNumber) async {
    final response = await ApiService.post('/clinic-labs', data: {
      'name': name,
      'phoneNumber': phoneNumber,
    });

    return LabModel.fromJson(response.data!['lab']);
  }

  static Future<LabModel> edit(String id, String name, String phoneNumber) async {
    final response = await ApiService.patch('/clinic-labs/$id', data: {
      'name': name,
      'phoneNumber': phoneNumber,
    });

    return LabModel.fromJson(response.data!['lab']);
  }

  static Future<void> delete(String id) async {
    await ApiService.delete('/clinic-labs/$id');
  }
}
