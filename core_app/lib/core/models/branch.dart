import 'package:core_app/core/helpers/hex_convertor.dart';
import 'package:core_app/core/utils/json.dart';
import 'package:flutter/material.dart';
import 'package:json_annotation/json_annotation.dart';

part 'generated/branch.g.dart';

@JsonSerializable()
class BranchModel {
  final String id;
  final String name;
  @JsonCoordinate
  final double? latitude;
  @JsonCoordinate
  final double? longitude;
  final String color;
  final int rooms;
  @JsonDateTime
  final DateTime? archivedAt;

  Color get getColor => color.isEmpty ? Colors.blue : HexConvertor.fromHex(color);

  const BranchModel({
    required this.id,
    required this.name,
    required this.latitude,
    required this.longitude,
    required this.color,
    required this.rooms,
    required this.archivedAt,
  });

  factory BranchModel.fromJson(Map<String, dynamic> json) => _$BranchModelFromJson(json);
  Map<String, dynamic> toJson() => _$BranchModelToJson(this);
}
