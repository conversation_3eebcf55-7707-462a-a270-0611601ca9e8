import 'package:core_app/core/models/branch.dart';
import 'package:core_app/core/models/patient.dart';
import 'package:core_app/core/utils/json.dart';
import 'package:json_annotation/json_annotation.dart';

import 'procedure.dart';

part 'generated/visit.g.dart';

@JsonSerializable()
class VisitModel {
  final String id;
  final String patientId;
  final String branchId;
  final String createdById;
  final String appointmentId;
  @JsonString
  final String? createdBy;
  String diagnosis;
  String nextVisit;
  String? comments;
  List<String>? treatments;
  final DateTime createdAt;
  // Relations
  final PatientModel? patient;
  final BranchModel? branch;
  @<PERSON><PERSON><PERSON>ey(defaultValue: [])
  final List<VisitProcedureModel> procedures;

  VisitModel({
    required this.id,
    required this.patientId,
    required this.branchId,
    required this.createdById,
    required this.diagnosis,
    required this.nextVisit,
    required this.treatments,
    required this.createdAt,
    required this.appointmentId,
    this.comments,
    this.patient,
    this.procedures = const [],
    this.branch,
    this.createdBy,
  });

  factory VisitModel.fromJson(Map<String, dynamic> json) =>
      _$VisitModelFromJson(json);
  Map<String, dynamic> toJson() => _$VisitModelToJson(this);
}
