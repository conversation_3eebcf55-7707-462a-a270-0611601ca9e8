import 'package:core_app/core/models/branch.dart';
import 'package:core_app/core/models/user.dart';
import 'package:core_app/core/utils/json.dart';
import 'package:json_annotation/json_annotation.dart';

part 'generated/expense.g.dart';

@JsonSerializable()
class ExpenseModel {
  final String id;
  final double amount;
  final String description;
  final ExpenseType type;
  final DateTime createdAt;
  @JsonString
  final String? branchId;
  final BranchModel? branch;
  final String? createdById;
  final UserModel? createdBy;

  const ExpenseModel({
    required this.id,
    required this.amount,
    required this.description,
    required this.type,
    required this.createdAt,
    required this.branchId,
    this.branch,
    this.createdById,
    this.createdBy,
  });

  factory ExpenseModel.fromJson(Map<String, dynamic> json) =>
      _$ExpenseModelFromJson(json);
  Map<String, dynamic> toJson() => _$ExpenseModelToJson(this);
}

enum ExpenseType {
  @JsonValue('salary')
  salary,
  @JsonValue('materials')
  materials,
  @JsonValue('fees')
  fees,
  @JsonValue('custom')
  custom,
  @JsonValue('others')
  others,
}
