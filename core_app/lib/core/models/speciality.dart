import 'package:json_annotation/json_annotation.dart';

import 'procedure.dart';
import 'user.dart';

part 'generated/speciality.g.dart';

@JsonSerializable()
class SpecialityTemplateModel {
  String id;
  @Json<PERSON>ey(name: 'speciality')
  String name;
  final String createdById;
  final String updatedById;
  // Relations
  // final UserModel? createdBy;
  final UserModel? updatedBy;
  final List<ProcedureTemplateModel>? procedureTemplates;

  SpecialityTemplateModel({
    required this.id,
    required this.name,
    required this.createdById,
    // this.createdBy,
    required this.updatedById,
    this.updatedBy,
    this.procedureTemplates,
  });

  factory SpecialityTemplateModel.fromJson(Map<String, dynamic> json) =>
      _$SpecialityTemplateModelFromJson(json);
  Map<String, dynamic> toJson() => _$SpecialityTemplateModelToJson(this);
}
