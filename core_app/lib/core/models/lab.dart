import 'package:core_app/core/utils/json.dart';
import 'package:json_annotation/json_annotation.dart';

import 'branch.dart';
import 'patient.dart';

part 'generated/lab.g.dart';

@JsonSerializable()
class LabModel {
  final String id;
  final String name;
  final String phoneNumber;
  final DateTime createdAt;

  LabModel({
    required this.id,
    required this.name,
    required this.phoneNumber,
    required this.createdAt,
  });

  factory LabModel.fromJson(Map<String, dynamic> json) => _$LabModelFromJson(json);
  Map<String, dynamic> toJson() => _$LabModelToJson(this);
}

@JsonSerializable()
class LabRequestModel {
  final String id;
  @JsonKey(defaultValue: [])
  final List<String> misc;
  final double price;
  final String notes;
  final String nextStep;
  final String ponticDesign;
  final String implantWork;
  final String zro2;
  @JsonKey(name: '3dPrinting')
  final String threeDPrinting;
  final String fullArchImplant;
  final String emax;
  final String customAbutment;
  final String pmma;
  final String patientId;
  final String sendingBranchId;
  final String receivingBranchId;
  final String clinicLabId;
  final DateTime expectedDeliveryDate;
  final DateTime sendDate;
  @JsonDateTime
  final DateTime? actualDeliveryDate;
  final List<ShadePoint> shadeDetails;
  // Relations
  final PatientModel? patient;
  final BranchModel? sendingBranch;
  final BranchModel? receivingBranch;

  LabRequestModel({
    required this.id,
    this.misc = const [],
    required this.price,
    required this.notes,
    required this.nextStep,
    required this.ponticDesign,
    required this.implantWork,
    required this.zro2,
    required this.threeDPrinting,
    required this.fullArchImplant,
    required this.emax,
    required this.customAbutment,
    required this.pmma,
    required this.patientId,
    required this.sendingBranchId,
    required this.receivingBranchId,
    required this.clinicLabId,
    required this.expectedDeliveryDate,
    required this.sendDate,
    this.actualDeliveryDate,
    required this.shadeDetails,
    this.patient,
    this.receivingBranch,
    this.sendingBranch,
  });

  factory LabRequestModel.fromJson(Map<String, dynamic> json) => _$LabRequestModelFromJson(json);
  Map<String, dynamic> toJson() => _$LabRequestModelToJson(this);
}

@JsonSerializable()
class ShadePoint {
  @JsonKey(name: 'x')
  final double dx;
  @JsonKey(name: 'y')
  final double dy;
  String notes;
  ShadePoint([this.dx = 0, this.dy = 0, this.notes = '']);

  factory ShadePoint.fromJson(Map<String, dynamic> json) => _$ShadePointFromJson(json);
  Map<String, dynamic> toJson() => _$ShadePointToJson(this);
}
