// GENERATED CODE - DO NOT MODIFY BY HAND

part of '../patient.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PatientModel _$PatientModelFromJson(Map<String, dynamic> json) => PatientModel(
      id: json['id'] as String,
      name: json['name'] as String,
      email: json['email'] as String?,
      phoneNumber: json['phoneNumber'] as String,
      address: json['address'] as String,
      birthdate: dateTimeFromJson(json['birthdate'] as Map<String, dynamic>?),
      balance: (json['balance'] as num).toDouble(),
      job: json['job'] as String,
      patientGroups: (json['patientGroups'] as List<dynamic>?)
          ?.map((e) => PatientGroup.fromJson(e as Map<String, dynamic>))
          .toList(),
      dentalHistory: json['dentalHistory'] as String,
      medicalHistory: json['medicalHistory'] as String,
      treatmentPlan: json['treatmentPlan'] as String,
      fileNumber: json['fileNumber'] as String,
      reachChannel: json['reachChannel'] as String,
      createdById: json['createdById'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      teethStatus: (json['teethStatus'] as Map<String, dynamic>?)?.map(
            (k, e) =>
                MapEntry(k, TeethStatus.fromJson(e as Map<String, dynamic>)),
          ) ??
          {},
      createdBy: json['createdBy'] == null
          ? null
          : UserModel.fromJson(json['createdBy'] as Map<String, dynamic>),
      visits: (json['visits'] as List<dynamic>?)
          ?.map((e) => VisitModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      customReachChannel:
          stringFromJson(json['customReachChannel'] as Map<String, dynamic>?),
    );

Map<String, dynamic> _$PatientModelToJson(PatientModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'email': instance.email,
      'phoneNumber': instance.phoneNumber,
      'address': instance.address,
      'birthdate': dateTimeToJson(instance.birthdate),
      'balance': instance.balance,
      'job': instance.job,
      'dentalHistory': instance.dentalHistory,
      'medicalHistory': instance.medicalHistory,
      'treatmentPlan': instance.treatmentPlan,
      'fileNumber': instance.fileNumber,
      'reachChannel': instance.reachChannel,
      'customReachChannel': stringToJson(instance.customReachChannel),
      'createdById': instance.createdById,
      'createdAt': instance.createdAt.toIso8601String(),
      'teethStatus': instance.teethStatus,
      'createdBy': instance.createdBy,
      'visits': instance.visits,
      'patientGroups': instance.patientGroups,
    };

TeethStatus _$TeethStatusFromJson(Map<String, dynamic> json) => TeethStatus(
      json['notes'] as String? ?? '',
      json['toothRemoved'] as bool? ?? false,
      json['crown'] as bool? ?? false,
      json['endo'] as bool? ?? false,
      json['implant'] as bool? ?? false,
      json['operative'] as String? ?? '',
    );

Map<String, dynamic> _$TeethStatusToJson(TeethStatus instance) =>
    <String, dynamic>{
      'notes': instance.notes,
      'toothRemoved': instance.removed,
      'endo': instance.endo,
      'crown': instance.crown,
      'operative': instance.operative,
      'implant': instance.implant,
    };

PatientFileModel _$PatientFileModelFromJson(Map<String, dynamic> json) =>
    PatientFileModel(
      id: json['id'] as String,
      patientId: json['patientId'] as String,
      patient: json['patient'] == null
          ? null
          : PatientModel.fromJson(json['patient'] as Map<String, dynamic>),
      createdById: json['createdById'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      url: json['url'] as String,
      issueDate: dateTimeFromJson(json['issueDate'] as Map<String, dynamic>?),
      createdBy: json['createdBy'] == null
          ? null
          : UserModel.fromJson(json['createdBy'] as Map<String, dynamic>),
      visitId: json['visitId'] as String,
      visit: json['visit'] == null
          ? null
          : VisitModel.fromJson(json['visit'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PatientFileModelToJson(PatientFileModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'patientId': instance.patientId,
      'patient': instance.patient,
      'createdById': instance.createdById,
      'createdBy': instance.createdBy,
      'createdAt': instance.createdAt.toIso8601String(),
      'url': instance.url,
      'visitId': instance.visitId,
      'visit': instance.visit,
      'issueDate': dateTimeToJson(instance.issueDate),
    };
