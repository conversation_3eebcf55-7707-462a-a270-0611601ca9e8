// GENERATED CODE - DO NOT MODIFY BY HAND

part of '../visit.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

VisitModel _$VisitModelFromJson(Map<String, dynamic> json) => VisitModel(
      id: json['id'] as String,
      patientId: json['patientId'] as String,
      branchId: json['branchId'] as String,
      createdById: json['createdById'] as String,
      diagnosis: json['diagnosis'] as String,
      nextVisit: json['nextVisit'] as String,
      treatments: (json['treatments'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      appointmentId: json['appointmentId'] as String,
      comments: json['comments'] as String?,
      patient: json['patient'] == null
          ? null
          : PatientModel.fromJson(json['patient'] as Map<String, dynamic>),
      procedures: (json['procedures'] as List<dynamic>?)
              ?.map((e) =>
                  VisitProcedureModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      branch: json['branch'] == null
          ? null
          : BranchModel.fromJson(json['branch'] as Map<String, dynamic>),
      createdBy: stringFromJson(json['createdBy'] as Map<String, dynamic>?),
    );

Map<String, dynamic> _$VisitModelToJson(VisitModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'patientId': instance.patientId,
      'branchId': instance.branchId,
      'createdById': instance.createdById,
      'appointmentId': instance.appointmentId,
      'createdBy': stringToJson(instance.createdBy),
      'diagnosis': instance.diagnosis,
      'nextVisit': instance.nextVisit,
      'comments': instance.comments,
      'treatments': instance.treatments,
      'createdAt': instance.createdAt.toIso8601String(),
      'patient': instance.patient,
      'branch': instance.branch,
      'procedures': instance.procedures,
    };
