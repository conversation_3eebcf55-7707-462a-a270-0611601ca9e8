// GENERATED CODE - DO NOT MODIFY BY HAND

part of '../insurance.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

InsuranceCompanyModel _$InsuranceCompanyModelFromJson(
        Map<String, dynamic> json) =>
    InsuranceCompanyModel(
      id: json['id'] as String,
      name: json['name'] as String,
      balance: (json['balance'] as num).toDouble(),
      defaultPercentageCoverage:
          (json['defaultPercentageCoverage'] as num).toDouble(),
    );

Map<String, dynamic> _$InsuranceCompanyModelToJson(
        InsuranceCompanyModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'balance': instance.balance,
      'defaultPercentageCoverage': instance.defaultPercentageCoverage,
    };

InsuranceClaimModel _$InsuranceClaimModelFromJson(Map<String, dynamic> json) =>
    InsuranceClaimModel(
      id: json['id'] as String,
      insuranceCompanyId: json['insuranceCompanyId'] as String,
      patientId: json['patientId'] as String,
      visitId: json['visitId'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      patient: json['patient'] == null
          ? null
          : PatientModel.fromJson(json['patient'] as Map<String, dynamic>),
      insuranceCompany: json['insuranceCompany'] == null
          ? null
          : InsuranceCompanyModel.fromJson(
              json['insuranceCompany'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$InsuranceClaimModelToJson(
        InsuranceClaimModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'insuranceCompanyId': instance.insuranceCompanyId,
      'patientId': instance.patientId,
      'visitId': instance.visitId,
      'createdAt': instance.createdAt.toIso8601String(),
      'patient': instance.patient,
      'insuranceCompany': instance.insuranceCompany,
    };

ClaimItemModel _$ClaimItemModelFromJson(Map<String, dynamic> json) =>
    ClaimItemModel(
      id: json['id'] as String,
      claimId: json['claimId'] as String,
      treatment: json['treatment'] as String,
      price: (json['price'] as num).toDouble(),
      status: json['status'] as String,
      percentageCoverage: (json['percentageCoverage'] as num).toDouble(),
    );

Map<String, dynamic> _$ClaimItemModelToJson(ClaimItemModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'claimId': instance.claimId,
      'treatment': instance.treatment,
      'price': instance.price,
      'status': instance.status,
      'percentageCoverage': instance.percentageCoverage,
    };
