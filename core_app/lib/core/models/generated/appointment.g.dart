// GENERATED CODE - DO NOT MODIFY BY HAND

part of '../appointment.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AppointmentModel _$AppointmentModelFromJson(Map<String, dynamic> json) =>
    AppointmentModel(
      id: json['id'] as String,
      createdById: json['createdById'] as String,
      patientId: json['patientId'] as String,
      branchId: json['branchId'] as String,
      startTime: DateTime.parse(json['startTime'] as String),
      endTime: DateTime.parse(json['endTime'] as String),
      dentistId: stringFromJson(json['dentistId'] as Map<String, dynamic>?),
      room: (json['room'] as num).toInt(),
      cancelledAt:
          dateTimeFromJson(json['cancelledAt'] as Map<String, dynamic>?),
      cancelledById:
          stringFromJson(json['cancelledById'] as Map<String, dynamic>?),
      createdAt: DateTime.parse(json['createdAt'] as String),
      createdBy: json['createdBy'] == null
          ? null
          : UserModel.fromJson(json['createdBy'] as Map<String, dynamic>),
      patient: json['patient'] == null
          ? null
          : PatientModel.fromJson(json['patient'] as Map<String, dynamic>),
      branch: json['branch'] == null
          ? null
          : BranchModel.fromJson(json['branch'] as Map<String, dynamic>),
      visit: json['visit'] == null
          ? null
          : VisitModel.fromJson(json['visit'] as Map<String, dynamic>),
      dentist: json['dentist'] == null
          ? null
          : UserModel.fromJson(json['dentist'] as Map<String, dynamic>),
      cancelledBy: json['cancelledBy'] == null
          ? null
          : UserModel.fromJson(json['cancelledBy'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$AppointmentModelToJson(AppointmentModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'createdById': instance.createdById,
      'patientId': instance.patientId,
      'branchId': instance.branchId,
      'startTime': instance.startTime.toIso8601String(),
      'endTime': instance.endTime.toIso8601String(),
      'dentistId': stringToJson(instance.dentistId),
      'room': instance.room,
      'cancelledAt': dateTimeToJson(instance.cancelledAt),
      'cancelledById': stringToJson(instance.cancelledById),
      'createdAt': instance.createdAt.toIso8601String(),
      'visit': instance.visit,
      'dentist': instance.dentist,
      'branch': instance.branch,
      'patient': instance.patient,
      'cancelledBy': instance.cancelledBy,
      'createdBy': instance.createdBy,
    };
