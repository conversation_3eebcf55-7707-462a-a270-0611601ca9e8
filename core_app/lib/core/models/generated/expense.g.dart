// GENERATED CODE - DO NOT MODIFY BY HAND

part of '../expense.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ExpenseModel _$ExpenseModelFromJson(Map<String, dynamic> json) => ExpenseModel(
      id: json['id'] as String,
      amount: (json['amount'] as num).toDouble(),
      description: json['description'] as String,
      type: $enumDecode(_$ExpenseTypeEnumMap, json['type']),
      createdAt: DateTime.parse(json['createdAt'] as String),
      branchId: stringFromJson(json['branchId'] as Map<String, dynamic>?),
      branch: json['branch'] == null
          ? null
          : BranchModel.fromJson(json['branch'] as Map<String, dynamic>),
      createdById: json['createdById'] as String?,
      createdBy: json['createdBy'] == null
          ? null
          : UserModel.fromJson(json['createdBy'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$ExpenseModelToJson(ExpenseModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'amount': instance.amount,
      'description': instance.description,
      'type': _$ExpenseTypeEnumMap[instance.type]!,
      'createdAt': instance.createdAt.toIso8601String(),
      'branchId': stringToJson(instance.branchId),
      'branch': instance.branch,
      'createdById': instance.createdById,
      'createdBy': instance.createdBy,
    };

const _$ExpenseTypeEnumMap = {
  ExpenseType.salary: 'salary',
  ExpenseType.materials: 'materials',
  ExpenseType.fees: 'fees',
  ExpenseType.custom: 'custom',
  ExpenseType.others: 'others',
};
