// GENERATED CODE - DO NOT MODIFY BY HAND

part of '../clinic.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ClinicModel _$ClinicModelFromJson(Map<String, dynamic> json) => ClinicModel(
      displayName: json['displayName'] as String,
      name: json['name'] as String,
      currency: json['currency'] as String,
      country: json['country'] as String,
      clinicCount: (json['clinicCount'] as num).toInt(),
      subscriptionRenewedAt:
          DateTime.parse(json['subscriptionRenewedAt'] as String),
      subscriptionEndDate:
          DateTime.parse(json['subscriptionEndDate'] as String),
      subscriptionPlanId:
          stringFromJson(json['subscriptionPlanId'] as Map<String, dynamic>?),
      credit: (json['credit'] as num).toDouble(),
      creditCurrency: json['creditCurrency'] as String,
    );

Map<String, dynamic> _$ClinicModelToJson(ClinicModel instance) =>
    <String, dynamic>{
      'displayName': instance.displayName,
      'name': instance.name,
      'currency': instance.currency,
      'country': instance.country,
      'clinicCount': instance.clinicCount,
      'subscriptionRenewedAt': instance.subscriptionRenewedAt.toIso8601String(),
      'subscriptionEndDate': instance.subscriptionEndDate.toIso8601String(),
      'subscriptionPlanId': stringToJson(instance.subscriptionPlanId),
      'credit': instance.credit,
      'creditCurrency': instance.creditCurrency,
    };

PatientNotificationConfigModel _$PatientNotificationConfigModelFromJson(
        Map<String, dynamic> json) =>
    PatientNotificationConfigModel(
      clinicId: json['clinicId'] as String,
      language: $enumDecodeNullable(_$LanguageEnumMap, json['language'],
              unknownValue: Language.en) ??
          Language.en,
      daysPrior: (json['daysPrior'] as num).toInt(),
      includeLocation: json['includeLocation'] as bool,
      clinic: json['clinic'] == null
          ? null
          : ClinicModel.fromJson(json['clinic'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PatientNotificationConfigModelToJson(
        PatientNotificationConfigModel instance) =>
    <String, dynamic>{
      'clinicId': instance.clinicId,
      'language': _$LanguageEnumMap[instance.language]!,
      'daysPrior': instance.daysPrior,
      'includeLocation': instance.includeLocation,
      'clinic': instance.clinic,
    };

const _$LanguageEnumMap = {
  Language.en: 'en',
  Language.ar: 'ar',
};
