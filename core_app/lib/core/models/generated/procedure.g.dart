// GENERATED CODE - DO NOT MODIFY BY HAND

part of '../procedure.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ProcedureTemplateModel _$ProcedureTemplateModelFromJson(
        Map<String, dynamic> json) =>
    ProcedureTemplateModel(
      id: json['id'] as String,
      createdById: json['createdById'] as String,
      createdBy: json['createdBy'] == null
          ? null
          : UserModel.fromJson(json['createdBy'] as Map<String, dynamic>),
      updatedById: json['updatedById'] as String,
      updatedBy: json['updatedBy'] == null
          ? null
          : UserModel.fromJson(json['updatedBy'] as Map<String, dynamic>),
      specialityId: json['specialityId'] as String,
      name: json['procedure'] as String,
      price: (json['price'] as num).toDouble(),
      toothRemoved: json['toothRemoved'] as bool,
      crown: json['crown'] as bool,
      endo: json['endo'] as bool,
      implant: json['implant'] as bool,
      operative: json['operative'] as String,
      speciality: SpecialityTemplateModel.fromJson(
          json['speciality'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$ProcedureTemplateModelToJson(
        ProcedureTemplateModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'createdById': instance.createdById,
      'createdBy': instance.createdBy,
      'updatedById': instance.updatedById,
      'updatedBy': instance.updatedBy,
      'specialityId': instance.specialityId,
      'speciality': instance.speciality,
      'procedure': instance.name,
      'price': instance.price,
      'toothRemoved': instance.toothRemoved,
      'crown': instance.crown,
      'endo': instance.endo,
      'implant': instance.implant,
      'operative': instance.operative,
    };

VisitProcedureModel _$VisitProcedureModelFromJson(Map<String, dynamic> json) =>
    VisitProcedureModel(
      id: json['id'] as String,
      createdById: json['createdById'] as String,
      createdBy: json['createdBy'] == null
          ? null
          : UserModel.fromJson(json['createdBy'] as Map<String, dynamic>),
      dentistId: json['dentistId'] as String,
      dentist: json['dentist'] == null
          ? null
          : UserModel.fromJson(json['dentist'] as Map<String, dynamic>),
      visitId: json['visitId'] as String,
      visit: json['visit'] == null
          ? null
          : VisitModel.fromJson(json['visit'] as Map<String, dynamic>),
      procedureTamplateId:
          stringFromJson(json['procedureTamplateId'] as Map<String, dynamic>?),
      procedureTemplate: json['procedureTemplate'] == null
          ? null
          : ProcedureTemplateModel.fromJson(
              json['procedureTemplate'] as Map<String, dynamic>),
      toothNumber: json['toothNumber'] as String,
      speciality: json['speciality'] as String,
      procedure: json['procedure'] as String,
      nextVisit: json['nextVisit'] as String,
      notes: json['notes'] as String,
      discount: (json['discount'] as num).toDouble(),
      price: (json['price'] as num).toDouble(),
      toothRemoved: json['toothRemoved'] as bool? ?? false,
    );

Map<String, dynamic> _$VisitProcedureModelToJson(
        VisitProcedureModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'createdById': instance.createdById,
      'createdBy': instance.createdBy,
      'dentistId': instance.dentistId,
      'dentist': instance.dentist,
      'visitId': instance.visitId,
      'visit': instance.visit,
      'procedureTamplateId': stringToJson(instance.procedureTamplateId),
      'procedureTemplate': instance.procedureTemplate,
      'toothNumber': instance.toothNumber,
      'speciality': instance.speciality,
      'procedure': instance.procedure,
      'nextVisit': instance.nextVisit,
      'notes': instance.notes,
      'discount': instance.discount,
      'price': instance.price,
      'toothRemoved': instance.toothRemoved,
    };
