// GENERATED CODE - DO NOT MODIFY BY HAND

part of '../speciality.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SpecialityTemplateModel _$SpecialityTemplateModelFromJson(
        Map<String, dynamic> json) =>
    SpecialityTemplateModel(
      id: json['id'] as String,
      name: json['speciality'] as String,
      createdById: json['createdById'] as String,
      updatedById: json['updatedById'] as String,
      updatedBy: json['updatedBy'] == null
          ? null
          : UserModel.fromJson(json['updatedBy'] as Map<String, dynamic>),
      procedureTemplates: (json['procedureTemplates'] as List<dynamic>?)
          ?.map(
              (e) => ProcedureTemplateModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$SpecialityTemplateModelToJson(
        SpecialityTemplateModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'speciality': instance.name,
      'createdById': instance.createdById,
      'updatedById': instance.updatedById,
      'updatedBy': instance.updatedBy,
      'procedureTemplates': instance.procedureTemplates,
    };
