// GENERATED CODE - DO NOT MODIFY BY HAND

part of '../payment.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PaymentModel _$PaymentModelFromJson(Map<String, dynamic> json) => PaymentModel(
      id: json['id'] as String,
      createdById: json['createdById'] as String,
      branchId: json['branchId'] as String,
      patientId: json['patientId'] as String,
      visitId: json['visitId'] as String,
      type: $enumDecode(_$PaymentTypeEnumMap, json['type']),
      customPaymentMethodId: stringFromJson(
          json['customPaymentMethodId'] as Map<String, dynamic>?),
      amount: (json['amount'] as num).toDouble(),
      cancelledAt:
          dateTimeFromJson(json['cancelledAt'] as Map<String, dynamic>?),
      cancellationReason: json['cancellationReason'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      createdBy: json['createdBy'] == null
          ? null
          : UserModel.fromJson(json['createdBy'] as Map<String, dynamic>),
      branch: json['branch'] == null
          ? null
          : BranchModel.fromJson(json['branch'] as Map<String, dynamic>),
      patient: json['patient'] == null
          ? null
          : PatientModel.fromJson(json['patient'] as Map<String, dynamic>),
      visit: json['visit'] == null
          ? null
          : VisitModel.fromJson(json['visit'] as Map<String, dynamic>),
      customPaymentMethod: json['customPaymentMethod'] == null
          ? null
          : CustomPaymentModel.fromJson(
              json['customPaymentMethod'] as Map<String, dynamic>),
      paymentLink: json['paymentLink'] as String?,
    );

Map<String, dynamic> _$PaymentModelToJson(PaymentModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'createdById': instance.createdById,
      'branchId': instance.branchId,
      'patientId': instance.patientId,
      'visitId': instance.visitId,
      'type': _$PaymentTypeEnumMap[instance.type]!,
      'customPaymentMethodId': stringToJson(instance.customPaymentMethodId),
      'amount': instance.amount,
      'cancelledAt': dateTimeToJson(instance.cancelledAt),
      'cancellationReason': instance.cancellationReason,
      'createdAt': instance.createdAt.toIso8601String(),
      'createdBy': instance.createdBy,
      'branch': instance.branch,
      'patient': instance.patient,
      'visit': instance.visit,
      'customPaymentMethod': instance.customPaymentMethod,
      'paymentLink': instance.paymentLink,
    };

const _$PaymentTypeEnumMap = {
  PaymentType.cash: 'cash',
  PaymentType.card: 'card',
  PaymentType.naab: 'naab',
  PaymentType.custom: 'custom',
  PaymentType.valu: 'valu',
};

CustomPaymentModel _$CustomPaymentModelFromJson(Map<String, dynamic> json) =>
    CustomPaymentModel(
      id: json['id'] as String,
      name: json['name'] as String,
      percentageFee: (json['percentageFee'] as num).toDouble(),
      flatFee: (json['flatFee'] as num).toDouble(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      deletedAt: json['deletedAt'] == null
          ? null
          : DateTime.parse(json['deletedAt'] as String),
    );

Map<String, dynamic> _$CustomPaymentModelToJson(CustomPaymentModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'percentageFee': instance.percentageFee,
      'flatFee': instance.flatFee,
      'createdAt': instance.createdAt.toIso8601String(),
      'deletedAt': instance.deletedAt?.toIso8601String(),
    };

PaymentLinkModel _$PaymentLinkModelFromJson(Map<String, dynamic> json) =>
    PaymentLinkModel(
      id: json['id'] as String,
      patientPaymentId: json['patientPaymentId'] as String,
      provider: json['provider'] as String,
      paymentLink: DateTime.parse(json['paymentLink'] as String),
      amount: (json['amount'] as num).toDouble(),
      status: json['status'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$PaymentLinkModelToJson(PaymentLinkModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'patientPaymentId': instance.patientPaymentId,
      'provider': instance.provider,
      'paymentLink': instance.paymentLink.toIso8601String(),
      'amount': instance.amount,
      'status': instance.status,
      'createdAt': instance.createdAt.toIso8601String(),
    };
