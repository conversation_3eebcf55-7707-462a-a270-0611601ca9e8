// GENERATED CODE - DO NOT MODIFY BY HAND

part of '../lab.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LabModel _$LabModelFromJson(Map<String, dynamic> json) => LabModel(
      id: json['id'] as String,
      name: json['name'] as String,
      phoneNumber: json['phoneNumber'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$LabModelToJson(LabModel instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'phoneNumber': instance.phoneNumber,
      'createdAt': instance.createdAt.toIso8601String(),
    };

LabRequestModel _$LabRequestModelFromJson(Map<String, dynamic> json) =>
    LabRequestModel(
      id: json['id'] as String,
      misc:
          (json['misc'] as List<dynamic>?)?.map((e) => e as String).toList() ??
              [],
      price: (json['price'] as num).toDouble(),
      notes: json['notes'] as String,
      nextStep: json['nextStep'] as String,
      ponticDesign: json['ponticDesign'] as String,
      implantWork: json['implantWork'] as String,
      zro2: json['zro2'] as String,
      threeDPrinting: json['3dPrinting'] as String,
      fullArchImplant: json['fullArchImplant'] as String,
      emax: json['emax'] as String,
      customAbutment: json['customAbutment'] as String,
      pmma: json['pmma'] as String,
      patientId: json['patientId'] as String,
      sendingBranchId: json['sendingBranchId'] as String,
      receivingBranchId: json['receivingBranchId'] as String,
      clinicLabId: json['clinicLabId'] as String,
      expectedDeliveryDate:
          DateTime.parse(json['expectedDeliveryDate'] as String),
      sendDate: DateTime.parse(json['sendDate'] as String),
      actualDeliveryDate:
          dateTimeFromJson(json['actualDeliveryDate'] as Map<String, dynamic>?),
      shadeDetails: (json['shadeDetails'] as List<dynamic>)
          .map((e) => ShadePoint.fromJson(e as Map<String, dynamic>))
          .toList(),
      patient: json['patient'] == null
          ? null
          : PatientModel.fromJson(json['patient'] as Map<String, dynamic>),
      receivingBranch: json['receivingBranch'] == null
          ? null
          : BranchModel.fromJson(
              json['receivingBranch'] as Map<String, dynamic>),
      sendingBranch: json['sendingBranch'] == null
          ? null
          : BranchModel.fromJson(json['sendingBranch'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$LabRequestModelToJson(LabRequestModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'misc': instance.misc,
      'price': instance.price,
      'notes': instance.notes,
      'nextStep': instance.nextStep,
      'ponticDesign': instance.ponticDesign,
      'implantWork': instance.implantWork,
      'zro2': instance.zro2,
      '3dPrinting': instance.threeDPrinting,
      'fullArchImplant': instance.fullArchImplant,
      'emax': instance.emax,
      'customAbutment': instance.customAbutment,
      'pmma': instance.pmma,
      'patientId': instance.patientId,
      'sendingBranchId': instance.sendingBranchId,
      'receivingBranchId': instance.receivingBranchId,
      'clinicLabId': instance.clinicLabId,
      'expectedDeliveryDate': instance.expectedDeliveryDate.toIso8601String(),
      'sendDate': instance.sendDate.toIso8601String(),
      'actualDeliveryDate': dateTimeToJson(instance.actualDeliveryDate),
      'shadeDetails': instance.shadeDetails,
      'patient': instance.patient,
      'sendingBranch': instance.sendingBranch,
      'receivingBranch': instance.receivingBranch,
    };

ShadePoint _$ShadePointFromJson(Map<String, dynamic> json) => ShadePoint(
      (json['x'] as num?)?.toDouble() ?? 0,
      (json['y'] as num?)?.toDouble() ?? 0,
      json['notes'] as String? ?? '',
    );

Map<String, dynamic> _$ShadePointToJson(ShadePoint instance) =>
    <String, dynamic>{
      'x': instance.dx,
      'y': instance.dy,
      'notes': instance.notes,
    };
