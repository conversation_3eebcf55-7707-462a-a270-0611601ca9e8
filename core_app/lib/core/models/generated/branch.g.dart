// GENERATED CODE - DO NOT MODIFY BY HAND

part of '../branch.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BranchModel _$BranchModelFromJson(Map<String, dynamic> json) => BranchModel(
      id: json['id'] as String,
      name: json['name'] as String,
      latitude: coordinateFromJson(json['latitude'] as Map<String, dynamic>?),
      longitude: coordinateFromJson(json['longitude'] as Map<String, dynamic>?),
      color: json['color'] as String,
      rooms: (json['rooms'] as num).toInt(),
      archivedAt: dateTimeFromJson(json['archivedAt'] as Map<String, dynamic>?),
    );

Map<String, dynamic> _$BranchModelToJson(BranchModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'latitude': coordinateToJson(instance.latitude),
      'longitude': coordinateToJson(instance.longitude),
      'color': instance.color,
      'rooms': instance.rooms,
      'archivedAt': dateTimeToJson(instance.archivedAt),
    };
