// GENERATED CODE - DO NOT MODIFY BY HAND

part of '../timesheet.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TimesheetEntryModel _$TimesheetEntryModelFromJson(Map<String, dynamic> json) =>
    TimesheetEntryModel(
      id: json['id'] as String,
      userId: json['userId'] as String,
      branchId: json['branchId'] as String,
      clinicId: json['clinicId'] as String,
      createdById: json['createdById'] as String,
      startTime: DateTime.parse(json['startTime'] as String),
      endTime: dateTimeFromJson(json['endTime'] as Map<String, dynamic>?),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      user: json['user'] == null
          ? null
          : UserModel.fromJson(json['user'] as Map<String, dynamic>),
      branch: json['branch'] == null
          ? null
          : BranchModel.fromJson(json['branch'] as Map<String, dynamic>),
      createdBy: json['createdBy'] == null
          ? null
          : UserModel.fromJson(json['createdBy'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$TimesheetEntryModelToJson(
        TimesheetEntryModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'branchId': instance.branchId,
      'clinicId': instance.clinicId,
      'createdById': instance.createdById,
      'startTime': instance.startTime.toIso8601String(),
      'endTime': dateTimeToJson(instance.endTime),
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'user': instance.user,
      'branch': instance.branch,
      'createdBy': instance.createdBy,
    };
