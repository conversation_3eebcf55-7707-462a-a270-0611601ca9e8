// GENERATED CODE - DO NOT MODIFY BY HAND

part of '../inventory_link.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

InventoryLink _$InventoryLinkFromJson(Map<String, dynamic> json) =>
    InventoryLink(
      id: json['id'] as String,
      clinicId: json['clinicId'] as String,
      procedureTemplateId: json['procedureTemplateId'] as String,
      inventoryItemId: json['inventoryItemId'] as String,
      quantity: (json['quantity'] as num).toInt(),
      item: InventoryItemModel.fromJson(json['item'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$InventoryLinkToJson(InventoryLink instance) =>
    <String, dynamic>{
      'id': instance.id,
      'clinicId': instance.clinicId,
      'procedureTemplateId': instance.procedureTemplateId,
      'inventoryItemId': instance.inventoryItemId,
      'quantity': instance.quantity,
      'item': instance.item,
    };

InventoryConsumption _$InventoryConsumptionFromJson(
        Map<String, dynamic> json) =>
    InventoryConsumption(
      itemId: json['itemId'] as String,
      quantity: (json['quantity'] as num).toInt(),
    );

Map<String, dynamic> _$InventoryConsumptionToJson(
        InventoryConsumption instance) =>
    <String, dynamic>{
      'itemId': instance.itemId,
      'quantity': instance.quantity,
    };
