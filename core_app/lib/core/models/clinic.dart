import 'package:core_app/core/utils/json.dart';
import 'package:json_annotation/json_annotation.dart';

part 'generated/clinic.g.dart';

@JsonSerializable()
class ClinicModel {
  final String displayName;
  final String name;
  final String currency;
  final String country;
  final int clinicCount;
  final DateTime subscriptionRenewedAt;
  final DateTime subscriptionEndDate;
  @JsonString
  final String? subscriptionPlanId;
  final double credit;
  final String creditCurrency;

  const ClinicModel({
    required this.displayName,
    required this.name,
    required this.currency,
    required this.country,
    required this.clinicCount,
    required this.subscriptionRenewedAt,
    required this.subscriptionEndDate,
    required this.subscriptionPlanId,
    required this.credit,
    required this.creditCurrency,
  });

  factory ClinicModel.fromJson(Map<String, dynamic> json) =>
      _$ClinicModelFromJson(json);
  Map<String, dynamic> toJson() => _$ClinicModelToJson(this);
}

@JsonSerializable()
class PatientNotificationConfigModel {
  final String clinicId;
  @JsonKey(unknownEnumValue: Language.en, defaultValue: Language.en)
  final Language language;
  final int daysPrior;
  final bool includeLocation;

  final ClinicModel? clinic;

  const PatientNotificationConfigModel({
    required this.clinicId,
    required this.language,
    required this.daysPrior,
    required this.includeLocation,
    this.clinic,
  });

  factory PatientNotificationConfigModel.fromJson(Map<String, dynamic> json) =>
      _$PatientNotificationConfigModelFromJson(json);
  Map<String, dynamic> toJson() => _$PatientNotificationConfigModelToJson(this);
}

enum Language {
  @JsonValue('en')
  en,
  @JsonValue('ar')
  ar,
}
