import 'package:core_app/core/utils/json.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:path/path.dart' as path;

import 'patient_group.dart';
import 'user.dart';
import 'visit.dart';

part 'generated/patient.g.dart';

@JsonSerializable()
class PatientModel {
  final String id;
  final String name;
  final String? email;
  final String phoneNumber;
  final String address;
  @JsonDateTime
  final DateTime? birthdate;
  double balance;
  final String job;
  String dentalHistory;
  String medicalHistory;
  String treatmentPlan;
  final String fileNumber;
  final String reachChannel;
  @JsonString
  final String? customReachChannel;
  final String createdById;
  final DateTime createdAt;
  @JsonKey(defaultValue: {})
  final Map<String, TeethStatus> teethStatus;
  // Relations
  final UserModel? createdBy;
  final List<VisitModel>? visits;
  final List<PatientGroup>? patientGroups;

  PatientModel({
    required this.id,
    required this.name,
    required this.email,
    required this.phoneNumber,
    required this.address,
    required this.birthdate,
    required this.balance,
    required this.job,
    required this.patientGroups,
    required this.dentalHistory,
    required this.medicalHistory,
    required this.treatmentPlan,
    required this.fileNumber,
    required this.reachChannel,
    required this.createdById,
    required this.createdAt,
    required this.teethStatus,
    this.createdBy,
    this.visits,
    required this.customReachChannel,
  });

  factory PatientModel.fromJson(Map<String, dynamic> json) =>
      _$PatientModelFromJson(json);
  Map<String, dynamic> toJson() => _$PatientModelToJson(this);

  @override
  String toString() => name;
}

@JsonSerializable()
class TeethStatus {
  @JsonKey(defaultValue: '')
  String notes;
  @JsonKey(defaultValue: false, name: 'toothRemoved')
  bool removed;
  @JsonKey(defaultValue: false)
  bool endo;
  @JsonKey(defaultValue: false)
  bool crown;
  @JsonKey(defaultValue: '')
  String operative;
  @JsonKey(defaultValue: false)
  bool implant;

  TeethStatus([
    this.notes = '',
    this.removed = false,
    this.crown = false,
    this.endo = false,
    this.implant = false,
    this.operative = '',
  ]);
  factory TeethStatus.fromJson(Map<String, dynamic> json) =>
      _$TeethStatusFromJson(json);
  Map<String, dynamic> toJson() => _$TeethStatusToJson(this);
}

@JsonSerializable()
class PatientFileModel {
  final String id;
  final String patientId;
  final PatientModel? patient;
  final String createdById;
  final UserModel? createdBy;
  final DateTime createdAt;
  final String url;
  final String visitId;
  final VisitModel? visit;
  @JsonDateTime
  final DateTime? issueDate;

  String get title => path.basename(Uri.decodeComponent(url)).split('___')[1];

  const PatientFileModel({
    required this.id,
    required this.patientId,
    required this.patient,
    required this.createdById,
    required this.createdAt,
    required this.url,
    required this.issueDate,
    this.createdBy,
    required this.visitId,
    this.visit,
  });

  factory PatientFileModel.fromJson(Map<String, dynamic> json) =>
      _$PatientFileModelFromJson(json);
  Map<String, dynamic> toJson() => _$PatientFileModelToJson(this);
}
