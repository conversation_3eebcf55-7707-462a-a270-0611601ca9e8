import 'package:core_app/core/models/branch.dart';
import 'package:core_app/core/models/patient.dart';
import 'package:core_app/core/models/user.dart';
import 'package:core_app/core/models/visit.dart';
import 'package:core_app/core/utils/json.dart';
import 'package:json_annotation/json_annotation.dart';

part 'generated/payment.g.dart';

@JsonSerializable()
class PaymentModel {
  final String id;
  final String createdById;
  final String branchId;
  final String patientId;
  final String visitId;
  final PaymentType type;
  @JsonString
  final String? customPaymentMethodId;
  final double amount;
  @JsonDateTime
  final DateTime? cancelledAt;
  final String? cancellationReason;
  final DateTime createdAt;
  // Relations
  final UserModel? createdBy;
  final BranchModel? branch;
  final PatientModel? patient;
  final VisitModel? visit;
  final CustomPaymentModel? customPaymentMethod;
  // Other
  final String? paymentLink;

  const PaymentModel({
    required this.id,
    required this.createdById,
    required this.branchId,
    required this.patientId,
    required this.visitId,
    required this.type,
    required this.customPaymentMethodId,
    required this.amount,
    required this.cancelledAt,
    required this.cancellationReason,
    required this.createdAt,
    this.createdBy,
    this.branch,
    this.patient,
    this.visit,
    this.customPaymentMethod,
    required this.paymentLink,
  });

  factory PaymentModel.fromJson(Map<String, dynamic> json) =>
      _$PaymentModelFromJson(json);
  Map<String, dynamic> toJson() => _$PaymentModelToJson(this);
}

enum PaymentType {
  @JsonValue('cash')
  cash,
  @JsonValue('card')
  card,
  @JsonValue('naab')
  naab,
  @JsonValue('custom')
  custom,
  @JsonValue('valu')
  valu,
}

@JsonSerializable()
class CustomPaymentModel {
  final String id;
  final String name;
  final double percentageFee;
  final double flatFee;
  final DateTime createdAt;
  // @JsonDateTime
  final DateTime? deletedAt;

  const CustomPaymentModel({
    required this.id,
    required this.name,
    required this.percentageFee,
    required this.flatFee,
    required this.createdAt,
    required this.deletedAt,
  });

  factory CustomPaymentModel.fromJson(Map<String, dynamic> json) =>
      _$CustomPaymentModelFromJson(json);
  Map<String, dynamic> toJson() => _$CustomPaymentModelToJson(this);
}

@JsonSerializable()
class PaymentLinkModel {
  final String id;
  final String patientPaymentId;
  final String provider;
  final DateTime paymentLink;
  final double amount;
  final String status;
  final DateTime createdAt;

  const PaymentLinkModel({
    required this.id,
    required this.patientPaymentId,
    required this.provider,
    required this.paymentLink,
    required this.amount,
    required this.status,
    required this.createdAt,
  });

  factory PaymentLinkModel.fromJson(Map<String, dynamic> json) =>
      _$PaymentLinkModelFromJson(json);
  Map<String, dynamic> toJson() => _$PaymentLinkModelToJson(this);
}
