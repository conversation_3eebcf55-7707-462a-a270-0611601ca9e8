import 'package:core_app/core/models/inventory.dart';
import 'package:json_annotation/json_annotation.dart';

part 'generated/inventory_link.g.dart';

@JsonSerializable()
class InventoryLink {
  final String id;
  final String clinicId;
  final String procedureTemplateId;
  final String inventoryItemId;
  final int quantity;
  final InventoryItemModel item;

  const InventoryLink({
    required this.id,
    required this.clinicId,
    required this.procedureTemplateId,
    required this.inventoryItemId,
    required this.quantity,
    required this.item,
  });

  factory InventoryLink.fromJson(Map<String, dynamic> json) =>
      _$InventoryLinkFromJson(json);
  Map<String, dynamic> toJson() => _$InventoryLinkToJson(this);
}

@JsonSerializable()
class InventoryConsumption {
  final String itemId;
  final int quantity;

  const InventoryConsumption({
    required this.itemId,
    required this.quantity,
  });

  factory InventoryConsumption.fromJson(Map<String, dynamic> json) =>
      _$InventoryConsumptionFromJson(json);
  Map<String, dynamic> toJson() => _$InventoryConsumptionToJson(this);
}
