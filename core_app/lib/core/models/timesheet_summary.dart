import 'package:core_app/core/models/timesheet.dart';
import 'package:json_annotation/json_annotation.dart';

part 'generated/timesheet_summary.g.dart';

@JsonSerializable()
class TimesheetSummaryModel {
  final List<TimesheetEntryModel> entries;
  final double totalHours;
  final double totalSalary;

  const TimesheetSummaryModel({
    required this.entries,
    required this.totalHours,
    required this.totalSalary,
  });

  factory TimesheetSummaryModel.fromJson(Map<String, dynamic> json) => _$TimesheetSummaryModelFromJson(json);
  Map<String, dynamic> toJson() => _$TimesheetSummaryModelToJson(this);
}
