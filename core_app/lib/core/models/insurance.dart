import 'package:json_annotation/json_annotation.dart';

import 'patient.dart';

part 'generated/insurance.g.dart';

@JsonSerializable()
class InsuranceCompanyModel {
  final String id;
  final String name;
  final double balance;
  final double defaultPercentageCoverage;

  const InsuranceCompanyModel({
    required this.id,
    required this.name,
    required this.balance,
    required this.defaultPercentageCoverage,
  });

  factory InsuranceCompanyModel.fromJson(Map<String, dynamic> json) =>
      _$InsuranceCompanyModelFromJson(json);
  Map<String, dynamic> toJson() => _$InsuranceCompanyModelToJson(this);
}

@JsonSerializable()
class InsuranceClaimModel {
  final String id;
  final String insuranceCompanyId;
  final String patientId;
  final String visitId;
  final DateTime createdAt;
  // Relations
  final PatientModel? patient;
  final InsuranceCompanyModel? insuranceCompany;

  const InsuranceClaimModel({
    required this.id,
    required this.insuranceCompanyId,
    required this.patientId,
    required this.visitId,
    required this.createdAt,
    this.patient,
    this.insuranceCompany,
  });

  factory InsuranceClaimModel.fromJson(Map<String, dynamic> json) =>
      _$InsuranceClaimModelFromJson(json);
  Map<String, dynamic> toJson() => _$InsuranceClaimModelToJson(this);
}

@JsonSerializable()
class ClaimItemModel {
  final String id;
  final String claimId;
  String treatment;
  double price;
  String status;
  double percentageCoverage;

  ClaimItemModel({
    required this.id,
    required this.claimId,
    required this.treatment,
    required this.price,
    required this.status,
    required this.percentageCoverage,
  });

  factory ClaimItemModel.fromJson(Map<String, dynamic> json) => _$ClaimItemModelFromJson(json);
  Map<String, dynamic> toJson() => _$ClaimItemModelToJson(this);
}
