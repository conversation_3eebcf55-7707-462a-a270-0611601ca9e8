import 'package:core_app/core/models/branch.dart';
import 'package:core_app/core/models/user.dart';
import 'package:core_app/core/utils/json.dart';
import 'package:json_annotation/json_annotation.dart';

part 'generated/timesheet.g.dart';

@JsonSerializable()
class TimesheetEntryModel {
  final String id;
  final String userId;
  final String branchId;
  final String clinicId;
  final String createdById;
  final DateTime startTime;
  @JsonDateTime
  final DateTime? endTime;
  final DateTime createdAt;
  final DateTime updatedAt;
  
  // Relations
  final UserModel? user;
  final BranchModel? branch;
  final UserModel? createdBy;

  const TimesheetEntryModel({
    required this.id,
    required this.userId,
    required this.branchId,
    required this.clinicId,
    required this.createdById,
    required this.startTime,
    required this.endTime,
    required this.createdAt,
    required this.updatedAt,
    this.user,
    this.branch,
    this.createdBy,
  });

  factory TimesheetEntryModel.fromJson(Map<String, dynamic> json) => _$TimesheetEntryModelFromJson(json);
  Map<String, dynamic> toJson() => _$TimesheetEntryModelToJson(this);
}
