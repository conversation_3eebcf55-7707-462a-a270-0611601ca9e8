import 'package:core_app/core/fcm_init.dart';
import 'package:flutter/material.dart';

class MolarMindResponse {
  final String inferenceId;
  final double time;
  final MolarMindImage image;
  final List<MolarMindPrediction> predictions;
  final String? imagePath; // Local path to the uploaded image

  MolarMindResponse({
    required this.inferenceId,
    required this.time,
    required this.image,
    required this.predictions,
    this.imagePath,
  });

  factory MolarMindResponse.fromJson(Map<String, dynamic> json) {
    int index = -1;
    var predictions = (json['predictions'] as List<dynamic>?)?.map((e) {
          index++;
          return MolarMindPrediction.fromJson(e, index);
        }).toList() ??
        [];

    predictions = predictions
        .where((element) =>
            element.confidence >= FCMInitializer.molarmind_min_confedence())
        .toList();
    return MolarMindResponse(
      inferenceId: json['inference_id'] ?? '',
      time: (json['time'] ?? 0.0).toDouble(),
      image: MolarMindImage.fromJson(json['image'] ?? {}),
      predictions: predictions,
      imagePath: json['image_path'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'inference_id': inferenceId,
      'time': time,
      'image': image.toJson(),
      'predictions': predictions.map((e) => e.toJson()).toList(),
      'image_path': imagePath,
    };
  }
}

class MolarMindImage {
  final int width;
  final int height;

  MolarMindImage({
    required this.width,
    required this.height,
  });

  factory MolarMindImage.fromJson(Map<String, dynamic> json) {
    return MolarMindImage(
      width: json['width'] ?? 0,
      height: json['height'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'width': width,
      'height': height,
    };
  }
}

class MolarMindPrediction {
  final double x;
  final double y;
  final double width;
  final double height;
  final double confidence;
  final String className;
  final int classId;
  final String detectionId;
  final Color? tempColor; // Temporary random color for this prediction

  MolarMindPrediction({
    required this.x,
    required this.y,
    required this.width,
    required this.height,
    required this.confidence,
    required this.className,
    required this.classId,
    required this.detectionId,
    this.tempColor,
  });

  factory MolarMindPrediction.fromJson(Map<String, dynamic> json, int index) {
    return MolarMindPrediction(
      x: (json['x'] ?? 0.0).toDouble(),
      y: (json['y'] ?? 0.0).toDouble(),
      width: (json['width'] ?? 0.0).toDouble(),
      height: (json['height'] ?? 0.0).toDouble(),
      confidence: (json['confidence'] ?? 0.0).toDouble(),
      className: json['class'] ?? '',
      classId: json['class_id'] ?? 0,
      detectionId: json['detection_id'] ?? '',
      tempColor: _generateRandomColor(
          json['class_id']), // Assign random color when created
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'x': x,
      'y': y,
      'width': width,
      'height': height,
      'confidence': confidence,
      'class': className,
      'class_id': classId,
      'detection_id': detectionId,
    };
  }

  // Generate a random color for this prediction
  static Color _generateRandomColor(int index) {
    final random = index;
    final colors = [
      Colors.red,
      Colors.blue,
      Colors.green,
      Colors.purple,
      Colors.orange,
      Colors.pink,
      Colors.teal,
      Colors.indigo,
      Colors.amber,
      Colors.cyan,
      Colors.lime,
      Colors.brown,
      Colors.deepPurple,
      Colors.deepOrange,
      Colors.lightBlue,
      Colors.lightGreen,
    ];
    return colors[random % colors.length];
  }
}

class DentalClinicMolarChatImageResponse {
  final String id;
  final String clinicId;
  final String imageUrl;
  final String userId;
  final String inferenceId;
  final MolarMindResponse data;
  final DateTime createdAt;

  DentalClinicMolarChatImageResponse({
    required this.id,
    required this.clinicId,
    required this.imageUrl,
    required this.userId,
    required this.inferenceId,
    required this.data,
    required this.createdAt,
  });

  factory DentalClinicMolarChatImageResponse.fromJson(
      Map<String, dynamic> json) {
    json['data']['image_path'] = "/ai/image_recognition/${json['id']}/file";
    return DentalClinicMolarChatImageResponse(
      id: json['id'] ?? '',
      clinicId: json['clinic_id'] ?? '',
      imageUrl: json['image_url'] ?? '',
      userId: json['user_id'] ?? '',
      inferenceId: json['inference_id'] ?? '',
      data: MolarMindResponse.fromJson(json['data'] ?? {}),
      createdAt: DateTime.parse(json['created_at'] ??
          json['createdAt'] ??
          DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'clinic_id': clinicId,
      'image_url': imageUrl,
      'user_id': userId,
      'inference_id': inferenceId,
      'data': data.toJson(),
      'created_at': createdAt.toIso8601String(),
    };
  }
}
