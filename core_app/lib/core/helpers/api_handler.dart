import 'dart:convert';
import 'package:core_app/components/dialogs/basic.dart';
import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/pages/auth/login/login.dart';

import 'package:core_app/services/auth.dart';
import 'package:core_app/services/notify/messenger.dart';
import 'package:dio/dio.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart' hide Response;

Future<void> tryAPI(
  Function() f, {
  Function(DioException e)? onCatch,
}) async {
  try {
    await f();
  } on DioException catch (e) {
    if (e.response == null && e.type != DioExceptionType.unknown) {
      Get.dialog(Reconnect(() async {
        try {
          await f();
          return true;
        } on DioException catch (e) {
          if (e.response == null) {
            return false;
          }
          if (e.response!.statusCode == 401) {
            AuthService.to.logout();
            Get.offAll(() => const LoginPage());
            return false;
          }
          NotifyService.notice(
            title: e.response!.statusMessage ?? "Error",
            body: e.response!.error,
          );
        }
        return true;
      }));
      return;
    }
    if (e.response?.statusCode == 401) {
      AuthService.to.logout();
      Get.offAll(() => const LoginPage());
      return;
    }
    try {
      if (onCatch != null) {
        await onCatch(e);
        return;
      }
      NotifyService.notice(
        title: e.response?.statusMessage ?? "Error",
        body: e.response?.error,
      );
      FirebaseAnalytics.instance.logEvent(name: "API Error", parameters: {
        'error': e.response?.error ?? "Unknown error",
      });
    } catch (_) {
      if (kDebugMode) {
        rethrow;
      }
      NotifyService.notice(title: e.response?.statusMessage ?? "Error");
    }
  }
}

extension TransformData on Response {
  Map<String, dynamic> get json {
    if (data is String) return jsonDecode(data);
    return Map<String, dynamic>.of(data);
  }

  String get error {
    if (data is String) return jsonDecode(data);
    return Map<String, dynamic>.of(data)['error'];
  }
}

class Reconnect extends StatefulWidget {
  const Reconnect(this.retry, {super.key});

  final Future<bool> Function() retry;

  @override
  State<Reconnect> createState() => _ReconnectState();
}

class _ReconnectState extends State<Reconnect> {
  @override
  void initState() {
    tryTo();
    super.initState();
  }

  void tryTo() async {
    final f = await widget.retry();
    if (f) {
      Get.back();
      return;
    }

    await Future.delayed(const Duration(seconds: 1));
    tryTo();
  }

  @override
  Widget build(BuildContext context) {
    return const BasicDialog(
      title: "No Connection",
      children: [
        SpinKitCircle(
          color: ThemeColors.primary,
        ),
      ],
    );
  }
}
