import 'package:flutter/material.dart';

// Copied from https://stackoverflow.com/a/50081214/9449426
class HexConvertor {
  /// String is in the format "aabbcc" or "ffaabbcc" with an optional leading "#".
  static Color fromHex(String hexString) {
    final buffer = StringBuffer();
    if (hexString.length == 6 || hexString.length == 7) buffer.write('ff');
    buffer.write(hexString.replaceFirst('#', ''));
    return Color(int.parse(buffer.toString(), radix: 16));
  }

  /// Prefixes a hash sign if [leadingHashSign] is set to `true` (default is `true`).
  static String toHex(Color color, {bool leadingHashSign = true}) =>
      '${leadingHashSign ? '#' : ''}'
      '${(color.a * 255.0).round().toRadixString(16).padLeft(2, '0')}'
      '${(color.r * 255.0).round().toRadixString(16).padLeft(2, '0')}'
      '${(color.g * 255.0).round().toRadixString(16).padLeft(2, '0')}'
      '${(color.b * 255.0).round().toRadixString(16).padLeft(2, '0')}';
}
