import 'package:core_app/core/api/branches.dart';
import 'package:core_app/core/api/expenses.dart';
import 'package:core_app/core/helpers/api_handler.dart';
import 'package:core_app/core/models/branch.dart';
import 'package:core_app/core/models/expense.dart';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ExpensesFormController extends GetxController {
  TextEditingController amount = TextEditingController();
  TextEditingController description = TextEditingController();
  final type = ExpenseType.others.obs;
  final selectedBranch = Rx<BranchModel?>(null);
  final branches = <BranchModel>[].obs;

  final ExpenseModel? repo;
  ExpensesFormController(this.repo);

  @override
  void onInit() {
    super.onInit();
    loadBranches();

    if (repo != null) {
      amount.text = repo!.amount.toString();
      description.text = repo!.description;
      type.value = repo!.type;
    }
  }

  void loadBranches() {
    tryAPI(() async {
      branches.value = await BranchesAPI.list();
      if (repo?.branchId != null) {
        final branchIndex = branches.indexWhere((b) => b.id == repo!.branchId);
        if (branchIndex >= 0) {
          selectedBranch.value = branches[branchIndex];
        }
      }
    });
  }

  void create() {
    tryAPI(() async {
      var i = await ExpensesAPI.create(
        double.parse(amount.text),
        description.text,
        type.value.name,
        branchId: selectedBranch.value?.id,
      );
      Get.back(result: i);
    });
  }

  void updateAPI() {
    tryAPI(() async {
      // Note: We can't update the branchId in the current backend API
      // The backend doesn't support updating the branch ID yet
      var i = await ExpensesAPI.update(
        repo!.id,
        double.parse(amount.text),
        description.text,
        type.value.name,
      );
      Get.back(result: i);
    });
  }
}
