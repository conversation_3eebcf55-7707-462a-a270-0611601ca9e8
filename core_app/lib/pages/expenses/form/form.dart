import 'package:core_app/components/dialogs/basic.dart';
import 'package:core_app/components/fields/text.dart';
import 'package:core_app/core/models/expense.dart';
import 'package:core_app/core/utils/i18n/translations.g.dart';
import 'package:core_app/pages/expenses/form/controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ExpenseForm extends StatelessWidget {
  const ExpenseForm(this.repo, {super.key});

  final ExpenseModel? repo;

  @override
  Widget build(BuildContext context) {
    final c = Get.put(ExpensesFormController(repo));
    return BasicDialog(
      title: t.expenseForm.title,
      children: [
        TextFormField(
          controller: c.amount,
          decoration: InputDecoration(labelText: t.expenseForm.amount),
          inputFormatters: [DecimalTextInputFormatter(2)],
        ),
        const SizedBox(height: 11),
        TextFormField(
          controller: c.description,
          decoration: InputDecoration(labelText: t.expenseForm.description),
        ),
        const SizedBox(height: 11),
        DropdownButtonFormField<ExpenseType>(
          value: c.type.value,
          decoration: InputDecoration(labelText: t.expenseForm.type),
          onChanged: c.type,
          items: [
            DropdownMenuItem<ExpenseType>(
              value: ExpenseType.salary,
              child: Text(t.expenseForm.types.salary),
            ),
            DropdownMenuItem<ExpenseType>(
              value: ExpenseType.materials,
              child: Text(t.expenseForm.types.materials),
            ),
            DropdownMenuItem<ExpenseType>(
              value: ExpenseType.fees,
              child: Text(t.expenseForm.types.fees),
            ),
            DropdownMenuItem<ExpenseType>(
              value: ExpenseType.others,
              child: Text(t.expenseForm.types.others),
            ),
          ],
        ),
        const SizedBox(height: 11),
        Obx(() => DropdownButtonFormField<String?>(
              value: c.selectedBranch.value?.id.isEmpty ?? true
                  ? null
                  : c.selectedBranch.value?.id,
              decoration: const InputDecoration(
                labelText: 'Branch (Optional)',
                hintText: 'Select a branch (optional)',
              ),
              onChanged: (value) {
                if (value != null) {
                  final branch = c.branches.firstWhere((b) => b.id == value);
                  c.selectedBranch.value = branch;
                } else {
                  c.selectedBranch.value = null;
                }
              },
              items: [
                const DropdownMenuItem<String?>(
                  key: Key('noBranchSelected'),
                  value: null,
                  child: Text('No branch selected'),
                ),
                ...c.branches.map((branch) {
                  debugPrint(branch.id);
                  debugPrint(c.selectedBranch.value?.id);
                  return DropdownMenuItem<String?>(
                    key: Key(branch.id),
                    value: branch.id,
                    child: Text(branch.name),
                  );
                }),
              ],
            )),
        const SizedBox(height: 13),
        Align(
          alignment: Get.locale?.languageCode == 'ar'
              ? Alignment.centerLeft
              : Alignment.centerRight,
          child: ElevatedButton(
            onPressed: c.repo != null ? c.updateAPI : c.create,
            child: Text(t.buttonTxt.save),
          ),
        )
      ],
    );
  }
}
