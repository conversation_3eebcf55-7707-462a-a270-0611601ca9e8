import 'package:core_app/components/buttons/text_icon.dart';
import 'package:core_app/components/tables/table.dart';
import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/core/models/expense.dart';
import 'package:core_app/core/models/user.dart';
import 'package:core_app/core/utils/i18n/translations.g.dart';
import 'package:core_app/layout/page_layout.dart';
import 'package:core_app/pages/expenses/form/form.dart';
import 'package:core_app/pages/expenses/list/filter_dialog.dart';

import 'package:core_app/services/auth.dart';
import 'package:core_app/services/config.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:jiffy/jiffy.dart';

import 'controller.dart';

class ExpensesListPage extends StatelessWidget {
  const ExpensesListPage({super.key});

  @override
  Widget build(BuildContext context) {
    var master = AuthService.getUser.role.index >= UserRole.master.index;
    final c = Get.find<ExpensesListController>();
    return Obx(() => PageLayout(
          children: [
            TableComponent<ExpenseModel>(
              title: t.expensesList.title,
              data: c.expenses[c.currentPage.value] ?? [],
              actions: [
                XTextIconButton(
                  title: t.expensesList.add,
                  icon: Iconsax.add_copy,
                  onPressed: () async {
                    final ExpenseModel? result =
                        await Get.dialog(const ExpenseForm(null));
                    if (result != null) {
                      c.expenses[c.currentPage.value] = await c.fetch();
                    }
                  },
                ),
                XTextIconButton(
                  title: c.isFiltered.value ? "Filtered" : "Filter",
                  icon: Iconsax.filter_copy,
                  onPressed: () async {
                    await Get.dialog(const ExpensesFilterDialog());
                  },
                ),
              ],
              columns: [
                TableColumn(
                  flex: 1,
                  title: t.expenseForm.amount,
                  minWidth: 100,
                  builder: (data) {
                    return TableColumn.stringBuilder(
                        '${data.amount} ${ConfigService.to.currency}');
                  },
                ),
                TableColumn(
                  flex: 1,
                  minWidth: 100,
                  title: t.expenseForm.type,
                  builder: (data) {
                    return TableColumn.stringBuilder(
                        data.type.name.capitalizeFirst!);
                  },
                ),
                TableColumn(
                  flex: 3,
                  minWidth: 300,
                  title: t.expenseForm.description,
                  builder: (data) {
                    return TableColumn.stringBuilder(data.description);
                  },
                ),
                TableColumn(
                  flex: 2,
                  title: 'Branch',
                  minWidth: 100,
                  builder: (data) {
                    return TableColumn.stringBuilder(
                        data.branch?.name ?? 'N/A');
                  },
                ),
                TableColumn(
                  flex: 2,
                  title: 'Action By',
                  minWidth: 100,
                  builder: (data) {
                    return TableColumn.stringBuilder(
                        data.createdBy?.name ?? 'N/A');
                  },
                ),
                TableColumn(
                  flex: 2,
                  title: t.createdAt,
                  minWidth: 100,
                  builder: (data) {
                    return TableColumn.stringBuilder(
                        Jiffy.parseFromDateTime(data.createdAt).MMMMEEEEd);
                  },
                ),
                if (master)
                  TableColumn(
                    title: '',
                    minWidth: 100,
                    builder: (data) {
                      return Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          IconButton(
                            iconSize: 18,
                            icon: const Icon(
                              Iconsax.edit_copy,
                              size: 18,
                            ),
                            onPressed: () async {
                              await Get.dialog(ExpenseForm(data));
                              c.expenses[c.currentPage.value] = await c.fetch();
                            },
                          ),
                          // IconButton(
                          //   iconSize: 18,
                          //   icon: const Icon(
                          //     Iconsax.trash_copy,
                          //     size: 18,
                          //   ),
                          //   onPressed: () {
                          //     tryAPI(() async {
                          //       await ExpensesAPI.delete(data.id);
                          //       c.expenses[c.currentPage.value]?.remove(data);
                          //       c.expenses.refresh();
                          //     });
                          //   },
                          // ),
                        ],
                      );
                    },
                  ),
              ],
            ),
            const SizedBox(height: 20),
            Obx(() {
              if (!c.next.value && c.currentPage.value <= 1) {
                return const SizedBox.shrink();
              }
              return Align(
                alignment: Alignment.centerRight,
                child: Container(
                  width: 200,
                  height: 50,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 11, vertical: 5),
                  decoration: BoxDecoration(
                    color: ThemeColors.bg,
                    borderRadius: BorderRadius.circular(13),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      if (c.currentPage.value > 1)
                        IconButton(
                          icon: const Icon(Icons.keyboard_arrow_left_rounded),
                          onPressed: c.loadPreviousPage,
                        ),
                      Text(
                        'Page ${c.currentPage.value}',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Color(0xFF364152),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      if (c.next.value)
                        IconButton(
                          icon: const Icon(Icons.keyboard_arrow_right_rounded),
                          onPressed: c.loadPage,
                        ),
                    ],
                  ),
                ),
              );
            }),
          ],
        ));
  }
}
