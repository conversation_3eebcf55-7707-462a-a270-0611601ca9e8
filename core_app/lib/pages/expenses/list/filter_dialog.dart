import 'package:core_app/components/dialogs/basic.dart';
import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/pages/expenses/list/controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:jiffy/jiffy.dart';

class ExpensesFilterDialog extends StatelessWidget {
  const ExpensesFilterDialog({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = ExpensesListController.to;

    return BasicDialog(
      title: 'Filter Expenses',
      children: [
        // Branch filter
        const Text('Branch'),
        const SizedBox(height: 8),
        Obx(() => DropdownButtonFormField<String?>(
              value: controller.selectedBranchId.value,
              hint: const Text('Select Branch'),
              isExpanded: true,
              decoration: const InputDecoration(
                filled: true,
                fillColor: Colors.white,
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                controller.selectedBranchId.value = value;
              },
              items: [
                const DropdownMenuItem<String?>(
                  value: null,
                  child: Text('All Branches'),
                ),
                ...controller.branches
                    .map((branch) => DropdownMenuItem<String?>(
                          value: branch.id,
                          child: Text(branch.name),
                        )),
              ],
            )),

        const SizedBox(height: 16),

        // Date range filters
        Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('From Date'),
                  const SizedBox(height: 8),
                  InkWell(
                    onTap: () async {
                      final result = await showDatePicker(
                        context: context,
                        initialDate: controller.fromDate.value ??
                            DateTime.now().subtract(const Duration(days: 30)),
                        firstDate:
                            DateTime.now().subtract(const Duration(days: 365)),
                        lastDate: controller.toDate.value ?? DateTime.now(),
                      );
                      if (result != null) {
                        controller.fromDate.value = result;
                      }
                    },
                    child: Container(
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 16),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Obx(() => Text(
                            controller.fromDate.value != null
                                ? Jiffy.parseFromDateTime(
                                        controller.fromDate.value!)
                                    .yMMMd
                                : 'Select Date',
                            style: TextStyle(
                              color: controller.fromDate.value != null
                                  ? ThemeColors.text
                                  : ThemeColors.notion,
                            ),
                          )),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('To Date'),
                  const SizedBox(height: 8),
                  InkWell(
                    onTap: () async {
                      final result = await showDatePicker(
                        context: context,
                        initialDate: controller.toDate.value ?? DateTime.now(),
                        firstDate: controller.fromDate.value ??
                            DateTime.now().subtract(const Duration(days: 365)),
                        lastDate: DateTime.now().add(const Duration(days: 1)),
                      );
                      if (result != null) {
                        controller.toDate.value = result;
                      }
                    },
                    child: Container(
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 16),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Obx(() => Text(
                            controller.toDate.value != null
                                ? Jiffy.parseFromDateTime(
                                        controller.toDate.value!)
                                    .yMMMd
                                : 'Select Date',
                            style: TextStyle(
                              color: controller.toDate.value != null
                                  ? ThemeColors.text
                                  : ThemeColors.notion,
                            ),
                          )),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),

        const SizedBox(height: 24),

        // Action buttons
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            TextButton(
              onPressed: () {
                controller.resetFilters();
                Get.back();
              },
              child: const Text('Reset'),
            ),
            const SizedBox(width: 16),
            ElevatedButton(
              onPressed: () {
                controller.applyFilters();
                Get.back();
              },
              child: const Text('Apply Filters'),
            ),
          ],
        ),
      ],
    );
  }
}
