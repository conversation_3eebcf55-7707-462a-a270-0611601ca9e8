import 'package:core_app/core/api/branches.dart';
import 'package:core_app/core/api/expenses.dart';
import 'package:core_app/core/helpers/api_handler.dart';
import 'package:core_app/core/models/branch.dart';
import 'package:core_app/core/models/expense.dart';
import 'package:get/get.dart';

class ExpensesListController extends GetxController {
  static ExpensesListController get to => Get.find();

  final next = true.obs;
  final currentPage = 0.obs; // limit per page = 20
  final expenses = <int, List<ExpenseModel>>{}.obs;

  // Filter variables
  final selectedBranchId = Rx<String?>(null);
  final fromDate = Rx<DateTime?>(null);
  final toDate = Rx<DateTime?>(null);
  final branches = <BranchModel>[].obs;
  final isFiltered = false.obs;

  void loadPage() {
    tryAPI(() async {
      currentPage.value++;
      final list = await fetch();
      if (list.isEmpty) {
        next.value = false;
        currentPage.value--;
      } else if (list.length < 20) {
        expenses[currentPage.value] = list;
        next.value = false;
      } else {
        expenses[currentPage.value] = list;
        next.value = true;
      }
    });
  }

  Future<List<ExpenseModel>> fetch() {
    return ExpensesAPI.list(
      page: currentPage.value,
      branchId: selectedBranchId.value,
      from: fromDate.value,
      to: toDate.value,
    );
  }

  loadPreviousPage() {
    currentPage.value--;
    next.value = true;
  }

  void applyFilters() {
    isFiltered.value = selectedBranchId.value != null ||
        fromDate.value != null ||
        toDate.value != null;

    // Reset pagination when applying filters
    currentPage.value = 0;
    expenses.clear();
    loadPage();
  }

  void resetFilters() {
    selectedBranchId.value = null;
    fromDate.value = null;
    toDate.value = null;
    isFiltered.value = false;

    // Reset pagination when clearing filters
    currentPage.value = 0;
    expenses.clear();
    loadPage();
  }

  void loadBranches() {
    tryAPI(() async {
      branches.value = await BranchesAPI.list();
    });
  }

  @override
  void onInit() {
    loadBranches();
    loadPage();
    super.onInit();
  }
}
