import 'package:core_app/components/dialogs/basic.dart';
import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/pages/home/<USER>';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AppointmentFilterDialog extends StatelessWidget {
  const AppointmentFilterDialog({super.key});

  @override
  Widget build(BuildContext context) {
    final c = Get.find<HomeController>();

    return BasicDialog(
      title: 'Filter Appointments',
      children: [
        const Text('Doctor'),
        const SizedBox(height: 8),
        Obx(() => DropdownButtonFormField<String?>(
              value: c.filterDentistId.value,
              hint: const Text('Select Doctor'),
              isExpanded: true,
              decoration: const InputDecoration(
                filled: true,
                fillColor: Colors.white,
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                c.filterDentistId.value = value;
              },
              items: [
                const DropdownMenuItem<String?>(
                  value: null,
                  child: Text('All Doctors'),
                ),
                ...c.allDentists.map((dentist) => DropdownMenuItem<String?>(
                      value: dentist.id,
                      child: Text(dentist.name),
                    )),
              ],
            )),
        const SizedBox(height: 16),
        const Text('Branch'),
        const SizedBox(height: 8),
        Obx(() => DropdownButtonFormField<String?>(
              value: c.filterBranchId.value,
              hint: const Text('Select Branch'),
              isExpanded: true,
              decoration: const InputDecoration(
                filled: true,
                fillColor: Colors.white,
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                c.filterBranchId.value = value;
              },
              items: [
                const DropdownMenuItem<String?>(
                  value: null,
                  child: Text('All Branches'),
                ),
                ...c.allBranches.map((branch) => DropdownMenuItem<String?>(
                      value: branch.id,
                      child: Text(branch.name),
                    )),
              ],
            )),
        const SizedBox(height: 16),
        const Text('Room'),
        const SizedBox(height: 8),
        Obx(() => DropdownButtonFormField<int?>(
              value: c.filterRoom.value,
              hint: const Text('Select Room'),
              isExpanded: true,
              decoration: const InputDecoration(
                filled: true,
                fillColor: Colors.white,
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                c.filterRoom.value = value;
              },
              items: [
                const DropdownMenuItem<int?>(
                  value: null,
                  child: Text('All Rooms'),
                ),
                ...c.availableRooms.map((room) => DropdownMenuItem<int?>(
                      value: room,
                      child: Text('Room $room'),
                    )),
              ],
            )),
        const SizedBox(height: 24),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            TextButton(
              onPressed: () {
                c.resetFilters();
                Get.back();
              },
              child: const Text('Reset'),
            ),
            const SizedBox(width: 8),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: ThemeColors.primary,
                foregroundColor: Colors.white,
              ),
              onPressed: () {
                c.isFiltered.value = true;
                Get.back();
              },
              child: const Text('Apply'),
            ),
          ],
        ),
      ],
    );
  }
}
