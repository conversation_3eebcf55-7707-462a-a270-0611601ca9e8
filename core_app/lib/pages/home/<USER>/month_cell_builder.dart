import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/core/models/appointment.dart';
import 'package:core_app/core/utils/responsive.dart';
import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_calendar/calendar.dart';

class _BranchAppointments {
  final Color color;
  final List<AppointmentModel> appointments;
  const _BranchAppointments(this.color, this.appointments);
}

class MonthCell extends StatelessWidget {
  const MonthCell(
    this.details, {
    Key? key,
  }) : super(key: key);

  final MonthCellDetails details;

  @override
  Widget build(BuildContext context) {
    final isVisible = DateTime.now().month == details.date.month;
    final appointmentMap = <String, _BranchAppointments>{};
    for (var repo in details.appointments.cast<AppointmentModel>()) {
      final i = appointmentMap[repo.branchId];
      if (i == null) {
        appointmentMap[repo.branchId] = _BranchAppointments(repo.branch!.getColor, [repo]);
      } else {
        i.appointments.add(repo);
      }
    }
    return Container(
      decoration: BoxDecoration(
        border: context.mobileView && appointmentMap.entries.isNotEmpty
            ? Border(
                right: const BorderSide(
                  width: 3,
                  color: ThemeColors.primaryLight,
                ),
                left: BorderSide(
                  width: .5,
                  color: Colors.grey.shade300,
                ),
                bottom: BorderSide(
                  width: .5,
                  color: Colors.grey.shade300,
                ),
                top: BorderSide(
                  width: .5,
                  color: Colors.grey.shade300,
                ),
              )
            : Border.all(
                width: .5,
                color: Colors.grey.shade300,
              ),
      ),
      child: Opacity(
        opacity: isVisible ? 1 : .51,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              details.date.day.toString(),
              style: const TextStyle(
                fontSize: 16,
                color: ThemeColors.text,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 5),
            if (context.mobileView == false)
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  for (final e in appointmentMap.entries.take(3))
                    if (e.value.appointments.isNotEmpty)
                      Container(
                        margin: const EdgeInsets.symmetric(horizontal: 2),
                        padding: const EdgeInsets.symmetric(vertical: 1, horizontal: 7),
                        decoration: BoxDecoration(
                          color: e.value.color,
                          borderRadius: BorderRadius.circular(13),
                        ),
                        child: Text(
                          e.value.appointments.length.toString(),
                          style: const TextStyle(
                            fontSize: 14,
                            color: Colors.white,
                          ),
                        ),
                      ),
                ],
              )
          ],
        ),
      ),
    );
  }
}
