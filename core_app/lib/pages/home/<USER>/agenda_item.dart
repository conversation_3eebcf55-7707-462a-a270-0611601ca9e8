import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/core/models/appointment.dart';
import 'package:core_app/core/models/user.dart';
import 'package:core_app/core/utils/responsive.dart';
import 'package:core_app/pages/appointments/cancel.dart';
import 'package:core_app/pages/appointments/create/create.dart';
import 'package:core_app/pages/home/<USER>';
import 'package:core_app/pages/visits/create/form.dart';
import 'package:core_app/routes/navigation.dart';

import 'package:core_app/routes/route_paths.dart';
import 'package:core_app/services/auth.dart';
import 'package:core_app/services/notify/messenger.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:jiffy/jiffy.dart';
import 'package:syncfusion_flutter_calendar/calendar.dart';

class AgendaItem extends StatelessWidget {
  const AgendaItem(this.repo, {Key? key}) : super(key: key);

  final AppointmentModel repo;

  @override
  Widget build(BuildContext context) {
    bool isMobile = context.mobileView;
    return Opacity(
      opacity: repo.visit != null ? .2 : 1,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 11, vertical: 3),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(7),
          color: repo.cancelledAt != null ? Colors.grey : repo.branch!.getColor,
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Obx(() {
                    final isTimelineView =
                        HomeController.to.calendarView.value ==
                            CalendarView.timelineMonth;
                    return Text.rich(
                      TextSpan(children: [
                        TextSpan(
                          text: repo.patient!.name.capitalize!,
                          style: TextStyle(
                            fontSize: isTimelineView ? 12 : 16,
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        TextSpan(
                          text:
                              "${isTimelineView ? ',\n' : !isMobile ? ', ' : '\n'}Dr. ${repo.dentist!.name.capitalize}",
                          style: TextStyle(
                            fontSize: isTimelineView ? 11 : 16,
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ]),
                      overflow: isTimelineView ? null : TextOverflow.ellipsis,
                    );
                  }),
                  Obx(() {
                    final isTimelineView =
                        HomeController.to.calendarView.value ==
                            CalendarView.timelineMonth;
                    return Text.rich(
                      TextSpan(children: [
                        TextSpan(
                          text:
                              Jiffy.parseFromDateTime(repo.startTime.toLocal())
                                  .jm,
                          style: TextStyle(
                            fontSize: isTimelineView ? 10 : 13,
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        TextSpan(
                          text: isTimelineView ? '\n' : ' - ',
                          style: TextStyle(
                            fontSize: isTimelineView ? 10 : 13,
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        TextSpan(
                          text: Jiffy.parseFromDateTime(repo.endTime.toLocal())
                              .jm,
                          style: TextStyle(
                            fontSize: isTimelineView ? 10 : 13,
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        TextSpan(
                          text: isTimelineView
                              ? '\n'
                              : (!isMobile ? '  |  ' : '\n'),
                          style: TextStyle(
                            fontSize: isTimelineView ? 10 : 13,
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        // if (!isTimelineView)
                        TextSpan(
                          text: isTimelineView
                              ? repo.branch!.name.capitalize!
                              : "Branch: ${repo.branch!.name.capitalize!}",
                          style: const TextStyle(
                            fontSize: 13,
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ]),
                      overflow: isTimelineView ? null : TextOverflow.ellipsis,
                    );
                  }),
                ],
              ),
            ),
            if (context.desktopView)
              IconButton(
                icon: Icon(
                  Icons.more_vert,
                  color: repo.visit == null ? Colors.white : Colors.black87,
                ),
                onPressed: () {
                  Get.bottomSheet(AgendaActions(repo), elevation: 0);
                },
              ),
          ],
        ),
      ),
    );
  }
}

class AgendaActions extends StatelessWidget {
  const AgendaActions(this.repo, {super.key});

  final AppointmentModel repo;

  @override
  Widget build(BuildContext context) {
    final container = Container(
      width: 480,
      margin: const EdgeInsets.all(13),
      padding: const EdgeInsets.symmetric(horizontal: 13, vertical: 13),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(21),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 7),
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 13),
            child: Text(
              'Actions',
              style: TextStyle(
                fontSize: 26,
                color: ThemeColors.text,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          if (AuthService.getUser.role != UserRole.basic)
            const SizedBox(height: 3),
          if (AuthService.getUser.role != UserRole.basic)
            ListTile(
              leading: const Icon(Icons.add),
              title: const Text('Add Visit'),
              onTap: () async {
                Get.back();
                final appointments =
                    HomeController.to.currentViewedAppointments;
                if (repo.cancelledAt != null) {
                  var stringDate =
                      Jiffy.parseFromDateTime(repo.cancelledAt!).yMMMEdjm;
                  NotifyService.notice(
                      title: 'Cancelled Appointment: $stringDate');
                  return;
                }
                if (repo.visit == null) {
                  var f = await Get.dialog(CreateVisitDialog(repo, true));
                  if (f != null) {
                    repo.visit = f;
                    appointments.refresh();
                  }
                } else {
                  var stringDate =
                      Jiffy.parseFromDateTime(repo.visit!.createdAt).yMMMEdjm;
                  NotifyService.notice(
                      title: 'Already has a visit at: $stringDate');
                }
              },
            ),
          ListTile(
            leading: const Icon(Icons.person_outline),
            title: const Text('View Profile'),
            onTap: () {
              Navigation.to(Routes.PatientProfile, {
                'patientID': repo.patientId,
              });
            },
          ),
          ListTile(
            leading: const Icon(Icons.schedule),
            title: const Text('Reschedule'),
            onTap: () async {
              Get.back();
              var c = HomeController.to;
              bool isCancelled =
                  (await Get.dialog(CancelAppointment(repo))) ?? false;
              if (isCancelled) {
                repo.cancelledAt = DateTime.now();
                c.currentViewedAppointments.refresh();
              }
              final AppointmentModel? createdAppointment =
                  await Get.dialog(CreateAppointmentDialog(
                patientId: repo.patientId,
                choosenDay: c.selectedDay.value,
                oldAppointment: repo,
              ));
              if (createdAppointment != null) {
                c.currentViewedAppointments.add(createdAppointment);
              }
            },
          ),
          ListTile(
            leading: const Icon(Icons.cancel_outlined),
            title: const Text('Cancel'),
            onTap: () async {
              Get.back();
              bool isCancelled =
                  (await Get.dialog(CancelAppointment(repo))) ?? false;
              if (isCancelled) {
                var appointments = HomeController.to.currentViewedAppointments;
                repo.cancelledAt = DateTime.now();
                appointments.refresh();
              }
            },
          ),
          const SizedBox(height: 7),
        ],
      ),
    );

    if (context.desktopView == false) return container;
    return UnconstrainedBox(
      child: container,
    );
  }
}
