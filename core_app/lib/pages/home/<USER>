import 'package:core_app/core/api/appointments.dart';
import 'package:core_app/core/helpers/api_handler.dart';
import 'package:core_app/core/models/appointment.dart';
import 'package:core_app/pages/home/<USER>';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:syncfusion_flutter_calendar/calendar.dart';

class AppointmentDataSource extends CalendarDataSource<AppointmentModel> {
  final Function? onRefresh;
  AppointmentDataSource(List<AppointmentModel> source, {this.onRefresh}) {
    final c = Get.find<HomeController>();
    if (c.isFiltered.value) {
      appointments = c.getFilteredAppointments();
    } else {
      appointments = source;
    }
  }

  @override
  String getSubject(int index) {
    var a = (appointments![index] as AppointmentModel);
    String name = a.patient!.name;
    String name2 = a.branch!.name;
    return "${name.capitalize!}, $name2";
  }

  @override
  String? getNotes(int index) {
    var a = (appointments![index] as AppointmentModel);
    String name = a.branch!.name;
    return name.capitalize;
  }

  @override
  DateTime getStartTime(int index) {
    var a = (appointments![index] as AppointmentModel);
    return a.startTime;
  }

  @override
  DateTime getEndTime(int index) {
    var a = (appointments![index] as AppointmentModel);
    return a.endTime;
  }

  @override
  Object? getId(int index) {
    var a = (appointments![index] as AppointmentModel);
    return a.id;
  }

  @override
  Object? getRecurrenceId(int index) {
    return null;
  }

  @override
  Color getColor(int index) {
    var a = (appointments![index] as AppointmentModel);
    return a.branch!.getColor;
  }

  @override
  List<DateTime>? getRecurrenceExceptionDates(int index) {
    return null;
  }

  @override
  String? getRecurrenceRule(int index) {
    return null;
  }

  @override
  bool isAllDay(int index) {
    return false;
  }

  @override
  Future<void> handleLoadMore(DateTime startDate, DateTime endDate) async {
    await tryAPI(() async {
      final a = await AppointmentsAPI.list(
        from: startDate,
        to: endDate,
      );
      if (onRefresh != null) onRefresh!();

      final c = Get.find<HomeController>();
      if (c.isFiltered.value) {
        // Store appointments in controller and apply filters
        c.currentViewedAppointments.clear();
        c.currentViewedAppointments.addAll(a);
        final filtered = c.getFilteredAppointments();
        appointments!.clear();
        appointments!.addAll(filtered);
        notifyListeners(CalendarDataSourceAction.reset, filtered);
      } else {
        appointments!.clear();
        appointments!.addAll(a);
        notifyListeners(CalendarDataSourceAction.reset, a);
      }
    });
  }
}
