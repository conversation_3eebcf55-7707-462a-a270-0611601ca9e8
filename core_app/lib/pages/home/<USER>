import 'package:core_app/core/api/branches.dart';
import 'package:core_app/core/api/users.dart';
import 'package:core_app/core/helpers/api_handler.dart';
import 'package:core_app/core/models/appointment.dart';
import 'package:core_app/core/models/branch.dart';
import 'package:core_app/core/models/user.dart';
import 'package:core_app/components/dialogs/feature_promo_dialog.dart';
import 'package:get/get.dart';
import 'package:syncfusion_flutter_calendar/calendar.dart';

class HomeController extends GetxController {
  static HomeController get to => Get.find();

  /// Used to show the agenda view in the calendar
  final showAgenda = true.obs;
  final calendarView = CalendarView.month.obs;
  final calendarController = CalendarController();

  /// Agenda Data (Current viewed appointments viewed in the calendar),
  /// The data is fetched from the API by the appointments source data [AppointmentDataSource]
  final currentViewedAppointments = <AppointmentModel>[].obs;

  /// Used to filter branch appointments and to set default day when openning
  /// new appointment dialog
  final selectedDay = Rx<DateTime>(DateTime.now());

  /// Used to filter appointments by dentist or branch in second sections
  final dentistId = Rx<String?>(null);
  final branchId = Rx<String?>(null);

  /// Used to filter appointments in calendar view
  final filterDentistId = Rx<String?>(null);
  final filterBranchId = Rx<String?>(null);
  final filterRoom = Rx<int?>(null);
  final isFiltered = false.obs;

  // Main Data - Branches & Dentists (for UI usage)
  final allDentists = <UserModel>[].obs;
  final allBranches = <BranchModel>[].obs;
  final isLoading = true.obs;
  final _hasShownPromo = false.obs;
  void fetchData() {
    tryAPI(() async {
      isLoading.value = true;
      allBranches.value = await BranchesAPI.list();
      final users = await UsersAPI.list();
      allDentists.value = users.where((e) => e.isDentist).toList();
      if (allBranches.isNotEmpty) {
        branchId.value = allBranches.first.id;
      }
      isLoading.value = false;

      // Show feature promo after data is loaded and page is fully rendered
      if (!_hasShownPromo.value) {
        _hasShownPromo.value = true;
        // Future.delayed(const Duration(milliseconds: 500), () {
        //   FeaturePromoService.checkAndShowFeaturePromo();
        // });
      }
    });
  }

  /// Get all available rooms from appointments
  List<int> get availableRooms {
    final rooms = <int>{};
    for (final appointment in currentViewedAppointments) {
      rooms.add(appointment.room);
    }
    return rooms.toList()..sort();
  }

  /// Apply filters to appointments
  List<AppointmentModel> getFilteredAppointments() {
    if (!isFiltered.value) return currentViewedAppointments;

    return currentViewedAppointments.where((appointment) {
      bool matchesDentist = filterDentistId.value == null ||
          appointment.dentistId == filterDentistId.value;
      bool matchesBranch = filterBranchId.value == null ||
          appointment.branchId == filterBranchId.value;
      bool matchesRoom =
          filterRoom.value == null || appointment.room == filterRoom.value;

      return matchesDentist && matchesBranch && matchesRoom;
    }).toList();
  }

  /// Reset all filters
  void resetFilters() {
    filterDentistId.value = null;
    filterBranchId.value = null;
    filterRoom.value = null;
    isFiltered.value = false;
  }

  @override
  void onInit() {
    super.onInit();
    fetchData();
  }
}
