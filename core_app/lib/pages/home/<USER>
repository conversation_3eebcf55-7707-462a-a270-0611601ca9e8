import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/core/models/appointment.dart';
import 'package:core_app/core/models/user.dart';
import 'package:core_app/core/utils/responsive.dart';
import 'package:core_app/layout/page_layout.dart';
import 'package:core_app/pages/appointments/create/create.dart';
import 'package:core_app/pages/visits/create/form.dart';
import 'package:core_app/components/dialogs/feature_promo_dialog.dart';

import 'package:core_app/services/auth.dart';
import 'package:core_app/services/config.dart';
import 'package:core_app/services/notify/messenger.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:jiffy/jiffy.dart';
import 'package:syncfusion_flutter_calendar/calendar.dart';

import 'appointment_source.dart';
import 'components/agenda_item.dart';
import 'components/filter_dialog.dart';
import 'components/month_cell_builder.dart';
import 'controller.dart';

class HomePage extends StatelessWidget {
  const HomePage({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final c = Get.find<HomeController>();

    return PageLayout(
      children: [
        if (ConfigService.to.versionStatus == VersionStatus.needUpdate)
          GestureDetector(
            onTap: () {
              // LaunchReview.launch(writeReview: false);
            },
            child: Container(
              clipBehavior: Clip.antiAlias,
              padding: const EdgeInsets.all(14),
              decoration: BoxDecoration(
                color: ThemeColors.bg,
                borderRadius: BorderRadius.circular(21),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const Row(
                    children: [
                      Icon(Icons.info_outline),
                      SizedBox(width: 10),
                      Text('New Version is availiable'),
                    ],
                  ),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 7, vertical: 3),
                    decoration: BoxDecoration(
                      color: ThemeColors.primary,
                      borderRadius: BorderRadius.circular(5),
                    ),
                    child: const Text(
                      'Update',
                      style: TextStyle(color: Colors.white),
                    ),
                  ),
                ],
              ),
            ),
          ),
        if (ConfigService.to.versionStatus == VersionStatus.needUpdate)
          const SizedBox(height: 16),
        Container(
          clipBehavior: Clip.antiAlias,
          padding: const EdgeInsets.all(14),
          decoration: BoxDecoration(
            color: ThemeColors.bg,
            borderRadius: BorderRadius.circular(21),
          ),
          child: Column(
            children: [
              SizedBox(
                width: double.infinity,
                child: Wrap(
                  spacing: 14,
                  runSpacing: 14,
                  alignment: WrapAlignment.end,
                  children: [
                    Obx(() => TextButton(
                          onPressed: () =>
                              c.showAgenda.value = !c.showAgenda.value,
                          child: Text(c.showAgenda.value
                              ? 'Hide Agenda'
                              : 'Show Agenda'),
                        )),
                    Obx(() => TextButton.icon(
                          icon: Icon(
                            Iconsax.filter_copy,
                            size: 16,
                            color:
                                c.isFiltered.value ? ThemeColors.primary : null,
                          ),
                          label: Text(
                            'Filter',
                            style: TextStyle(
                              color: c.isFiltered.value
                                  ? ThemeColors.primary
                                  : null,
                            ),
                          ),
                          onPressed: () async {
                            await Get.dialog(const AppointmentFilterDialog());
                            // Refresh the calendar to apply filters
                            if (c.isFiltered.value) {
                              // Force calendar to refresh with filtered data
                              c.calendarController.view =
                                  c.calendarController.view;
                            }
                            c.currentViewedAppointments.refresh();
                          },
                        )),
                    TextButton.icon(
                      icon: const Icon(Iconsax.add_copy),
                      label: const Text('New Appointment'),
                      onPressed: () async {
                        final AppointmentModel? r =
                            await Get.dialog(const CreateAppointmentDialog());
                        if (r != null) {
                          c.currentViewedAppointments.add(r);
                          c.currentViewedAppointments.refresh();
                        }
                      },
                    ),
                    // Test button for feature promo (remove in production)
                    if (kDebugMode)
                      TextButton.icon(
                        icon: const Icon(Icons.info_outline),
                        label: const Text('Test Feature Promo'),
                        onPressed: () async {
                          await FeaturePromoService.checkAndShowFeaturePromo();
                        },
                      ),
                  ],
                ),
              ),
              SizedBox(
                height: context.mobileView == false ? 680 : 480,
                child: Obx(() => SfCalendar(
                      showDatePickerButton: true,
                      allowViewNavigation: true,
                      allowedViews: const [
                        CalendarView.day,
                        CalendarView.week,
                        CalendarView.month,
                        CalendarView.timelineMonth,
                      ],
                      view: CalendarView.month,
                      onViewChanged: (viewChangedDetails) {
                        c.calendarView.value =
                            c.calendarController.view ?? CalendarView.month;
                      },
                      controller: c.calendarController,
                      key: const ValueKey('calendarView'),
                      initialSelectedDate: DateTime.now(),
                      dataSource: AppointmentDataSource(
                        c.currentViewedAppointments.value,
                        onRefresh: c.currentViewedAppointments.refresh,
                      ),
                      monthCellBuilder: (_, d) => MonthCell(d),
                      appointmentBuilder: (context, details) {
                        final AppointmentModel a = details.appointments.first;
                        return AgendaItem(a);
                      },
                      monthViewSettings: MonthViewSettings(
                        showAgenda: c.showAgenda.value,
                        agendaItemHeight:
                            context.mobileView == false ? 50 : 100,
                        appointmentDisplayMode:
                            MonthAppointmentDisplayMode.none,
                      ),
                      timeSlotViewSettings: const TimeSlotViewSettings(
                        timeIntervalHeight: 150,
                        timelineAppointmentHeight: 120,
                      ),
                      onSelectionChanged: (calendarSelectionDetails) {
                        final date =
                            calendarSelectionDetails.date ?? DateTime.now();
                        c.selectedDay.value =
                            DateTime(date.year, date.month, date.day);
                      },
                      loadMoreWidgetBuilder: (context, loadMoreAppointments) {
                        return FutureBuilder(
                          future: loadMoreAppointments(),
                          builder: (context, snapShot) {
                            return Container(
                              color: Colors.white54,
                              alignment: Alignment.center,
                              child: const SpinKitCircle(
                                size: 51,
                                color: ThemeColors.primary,
                              ),
                            );
                          },
                        );
                      },
                      onTap: (calendarTapDetails) async {
                        var name = calendarTapDetails.targetElement.name;
                        if (calendarTapDetails.appointments?.length == 1 &&
                            name == "appointment") {
                          final AppointmentModel i =
                              calendarTapDetails.appointments!.first;
                          if (context.desktopView == false) {
                            Get.bottomSheet(AgendaActions(i), elevation: 0);
                            return;
                          }
                          if (i.cancelledAt != null) {
                            var stringDate =
                                Jiffy.parseFromDateTime(i.cancelledAt!)
                                    .yMMMEdjm;
                            NotifyService.notice(
                                title: 'Cancelled Appointment: $stringDate');
                            return;
                          }
                          if (i.visit == null &&
                              AuthService.getUser.role != UserRole.basic) {
                            var f =
                                await Get.dialog(CreateVisitDialog(i, true));
                            if (f != null) {
                              i.visit = f;
                              c.currentViewedAppointments.refresh();
                            }
                          } else {
                            var stringDate =
                                Jiffy.parseFromDateTime(i.visit!.createdAt)
                                    .yMMMEdjm;
                            NotifyService.notice(
                                title: 'Already has a visit at: $stringDate');
                          }
                        }
                      },
                    )),
              ),
            ],
          ),
        ),
        const SizedBox(height: 13),
        Obx(() {
          return Wrap(
            spacing: 11,
            runSpacing: 11,
            children: [
              FractionallySizedBox(
                widthFactor: context.mobileView == false ? 0.45 : 1,
                child: DropdownButtonFormField<String>(
                  onChanged: c.branchId,
                  value: c.branchId.value,
                  hint: const Text('Branch'),
                  decoration: const InputDecoration(
                    filled: true,
                    fillColor: Colors.white,
                  ),
                  items: [
                    for (final branch in c.allBranches)
                      DropdownMenuItem(
                        value: branch.id,
                        child: Text(
                          branch.name,
                          style: const TextStyle(
                            fontSize: 14,
                            color: ThemeColors.text,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ],
          );
        }),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              clipBehavior: Clip.antiAlias,
              decoration: BoxDecoration(
                color: ThemeColors.bg,
                borderRadius: BorderRadius.circular(13),
              ),
              child: Obx(() {
                final value2 = c.branchId.value;
                final appointments = c.currentViewedAppointments.where((p0) {
                  if (value2 == null) false;
                  final inHours2 =
                      p0.startTime.difference(c.selectedDay.value).inHours;
                  return p0.branchId == value2 &&
                      inHours2 <= 25 &&
                      inHours2 >= 0;
                  // p0.appointment.cancelledAt == null &&
                  //     p0.appointment.denstistId == value3 ||
                  // p0.dentist?.getId == value3;
                }).toList();
                if (appointments.isEmpty) {
                  return const Padding(
                    padding: EdgeInsets.all(5),
                    child: Center(
                      child: Text(
                        'No Appointments',
                        style: TextStyle(
                          fontSize: 14,
                          color: ThemeColors.text,
                        ),
                      ),
                    ),
                  );
                }
                return ListView.builder(
                  itemCount: appointments.length,
                  shrinkWrap: true,
                  padding: const EdgeInsets.all(5),
                  physics: const NeverScrollableScrollPhysics(),
                  itemBuilder: (context, index) {
                    final appointment = appointments[index];
                    return Material(
                      clipBehavior: Clip.antiAlias,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(13),
                      ),
                      child: ListTile(
                        title: Text(
                          appointment.patient!.name,
                          style: const TextStyle(
                            fontSize: 14,
                            color: ThemeColors.text,
                          ),
                        ),
                        subtitle: Text.rich(TextSpan(
                          style: TextStyle(
                            fontSize: 11,
                            color: ThemeColors.text.withValues(alpha: 0.81),
                          ),
                          children: [
                            TextSpan(
                                text: "Branch: ${appointment.branch!.name}"),
                            const TextSpan(text: " | "),
                            TextSpan(
                                text:
                                    "From: ${Jiffy.parseFromDateTime(appointment.startTime).jm}"),
                            const TextSpan(text: " | "),
                            TextSpan(
                                text:
                                    "To: ${Jiffy.parseFromDateTime(appointment.endTime).jm}"),
                            const TextSpan(text: " | "),
                            TextSpan(text: appointment.dentist!.name),
                          ],
                        )),
                        trailing: IconButton(
                          icon: const Icon(Iconsax.more_copy),
                          onPressed: () {
                            Get.bottomSheet(AgendaActions(appointment),
                                elevation: 0);
                          },
                        ),
                        onTap: () async {
                          if (context.desktopView == false) {
                            Get.bottomSheet(AgendaActions(appointment),
                                elevation: 0);
                            return;
                          }
                          if (appointment.cancelledAt != null) {
                            var stringDate = Jiffy.parseFromDateTime(
                                    appointment.cancelledAt!)
                                .yMMMEdjm;
                            NotifyService.notice(
                                title: 'Cancelled Appointment: $stringDate');
                            return;
                          }
                          if (appointment.visit == null) {
                            // var f = await Get.dialog(CreateVisitForm(appointment, true));
                            // if (f != null) {
                            //   appointment.visit = f;
                            //   c.appointments.refresh();
                            // }
                          } else {
                            var stringDate = Jiffy.parseFromDateTime(
                                    appointment.visit!.createdAt)
                                .yMMMEdjm;
                            NotifyService.notice(
                                title: 'Already has a visit at: $stringDate');
                          }
                        },
                      ),
                    );
                  },
                );
              }),
            ),
          ],
        ),
      ],
    );
  }
}
