import 'package:core_app/core/api/branches.dart';
import 'package:core_app/core/api/payments.dart';
import 'package:core_app/core/helpers/api_handler.dart';
import 'package:core_app/core/models/branch.dart';
import 'package:core_app/core/models/payment.dart';
import 'package:core_app/services/api.dart';
import 'package:get/get.dart';

class PaymentsListController extends GetxController {
  RxInt currentPage = 0.obs; // limit per page = 20
  final payments = <int, List<PaymentModel>>{}.obs;
  RxBool next = true.obs;

  // Filter variables
  final selectedBranchId = Rx<String?>(null);
  final fromDate = Rx<DateTime?>(null);
  final toDate = Rx<DateTime?>(null);
  final branches = <BranchModel>[].obs;
  final paymentType = Rx<String?>(null);
  final isFiltered = false.obs;

  void loadPage() {
    currentPage++;
    tryAPI(() async {
      var list = await PaymentsAPI.list(
        page: currentPage.value,
        branchId: selectedBranchId.value,
        paymentType: paymentType.value,
        from: fromDate.value,
        to: toDate.value,
      );
      if (list.isEmpty) {
        next.value = false;
        currentPage--;
      } else if (list.length < 20) {
        payments[currentPage.value] = list;
        next.value = false;
      } else {
        payments[currentPage.value] = list;
        next.value = true;
      }
    });
  }

  Future<void> cancelPayment(String paymentId, String reason) async {
    try {
      await ApiService.post(
        '/payments/$paymentId/cancel',
        data: {
          'reason': reason,
        },
      );

      // Refresh the payments list after successful cancellation
      currentPage.value = 0;
      loadPage();
    } catch (err) {
      throw err.toString();
    }
  }

  loadPreviousPage() {
    currentPage--;
  }

  void applyFilters() {
    isFiltered.value = selectedBranchId.value != null ||
        fromDate.value != null ||
        toDate.value != null;

    // Reset pagination when applying filters
    currentPage.value = 0;
    payments.clear();
    loadPage();
  }

  void resetFilters() {
    selectedBranchId.value = null;
    fromDate.value = null;
    toDate.value = null;
    isFiltered.value = false;

    // Reset pagination when clearing filters
    currentPage.value = 0;
    payments.clear();
    loadPage();
  }

  void loadBranches() {
    tryAPI(() async {
      branches.value = await BranchesAPI.list();
    });
  }

  @override
  void onInit() {
    loadBranches();
    loadPage();
    super.onInit();
  }
}
