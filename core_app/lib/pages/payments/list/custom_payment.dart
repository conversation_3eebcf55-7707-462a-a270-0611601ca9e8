import 'package:core_app/components/dialogs/basic.dart';
import 'package:core_app/core/api/custom_payments.dart';
import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/core/models/payment.dart';
import 'package:core_app/core/utils/i18n/translations.g.dart';
import 'package:core_app/pages/custom_payment/form.dart';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

class _CustomPaymentsController extends GetxController {
  final customPaymentMethods = <CustomPaymentModel>[].obs;

  void fetchCustomPaymentMethods() async {
    final customPayments = await CustomPaymentsAPI.list();
    customPaymentMethods.value = customPayments;
  }

  deleteCustomPaymentMethod(String id) async {
    await CustomPaymentsAPI.delete(id: id);
    fetchCustomPaymentMethods();
  }

  @override
  void onInit() {
    fetchCustomPaymentMethods();
    super.onInit();
  }
}

class CustomPaymentDialog extends StatelessWidget {
  const CustomPaymentDialog({super.key});

  @override
  Widget build(BuildContext context) {
    final c = Get.put(_CustomPaymentsController());
    return BasicDialog(
      title: t.custom_payment_methods.title,
      dismissible: true,
      actions: [
        IconButton(
          icon: const Icon(Icons.add),
          onPressed: () async {
            final result = await Get.dialog(const CreateCustomPayment());
            if (result == true) {
              c.fetchCustomPaymentMethods();
            }
          },
        ),
      ],
      children: [
        Obx(
          () => (c.customPaymentMethods.isEmpty)
              ? Center(
                  child: Text(
                    t.custom_payment_methods.noCustomPaymentMethods,
                    style: TextStyle(
                      color: ThemeColors.text.withValues(alpha: 0.81),
                      fontWeight: FontWeight.w500,
                      fontSize: 14,
                    ),
                  ),
                )
              : ListView.builder(
                  shrinkWrap: true,
                  itemCount: c.customPaymentMethods.length,
                  physics: const NeverScrollableScrollPhysics(),
                  itemBuilder: (context, index) {
                    final customPaymentMethod = c.customPaymentMethods[index];
                    return ListTile(
                      onTap: () async {
                        final result = await Get.dialog(CreateCustomPayment(
                            customPayment: customPaymentMethod));
                        if (result == true) {
                          c.fetchCustomPaymentMethods();
                        }
                      },
                      leading: const Icon(Icons.circle_outlined),
                      title: Text(
                        customPaymentMethod.name,
                        style: TextStyle(
                          color: ThemeColors.text.withValues(alpha: 0.81),
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                      ),
                      subtitle: Text(
                        '${customPaymentMethod.percentageFee}% + ${customPaymentMethod.flatFee}',
                        style: TextStyle(
                          color: ThemeColors.text.withValues(alpha: 0.81),
                          fontWeight: FontWeight.w400,
                          fontSize: 13,
                        ),
                      ),
                      trailing: IconButton(
                        icon: const Icon(Icons.delete_outline),
                        onPressed: () async {
                          final result = await Get.dialog(
                            BasicDialog(
                              title: 'Delete Custom Payment Method',
                              children: [
                                const Text(
                                    'Are you sure you want to delete this custom payment method?'),
                                Row(
                                  children: [
                                    const Spacer(),
                                    const SizedBox(width: 16),
                                    ElevatedButton(
                                      onPressed: () async {
                                        await c.deleteCustomPaymentMethod(
                                            customPaymentMethod.id);
                                        Get.back(result: true);
                                      },
                                      child: const Text('Delete'),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          );
                          if (result == true) {
                            c.fetchCustomPaymentMethods();
                          }
                        },
                      ),
                    );
                  },
                ),
        )
      ],
    );
  }
}
