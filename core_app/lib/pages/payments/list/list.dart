import 'package:core_app/components/buttons/text_icon.dart';
import 'package:core_app/components/tables/table.dart';
import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/core/models/payment.dart';
import 'package:core_app/core/utils/i18n/translations.g.dart';
import 'package:core_app/layout/page_layout.dart';
import 'package:core_app/pages/payments/list/custom_payment.dart';
import 'package:core_app/pages/payments/list/cancel_payment_dialog.dart';
import 'package:core_app/pages/payments/list/filter_dialog.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:core_app/services/config.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:jiffy/jiffy.dart';

import 'controller.dart';

class PaymentsListPage extends StatelessWidget {
  const PaymentsListPage({super.key});

  @override
  Widget build(BuildContext context) {
    final c = Get.find<PaymentsListController>();
    return PageLayout(
      children: [
        Obx(() => TableComponent<PaymentModel>(
              title: t.paymentsView.list,
              data: c.payments.value[c.currentPage.value] ?? [],
              actions: [
                XTextIconButton(
                  icon: Icons.payment_outlined,
                  title: 'Custom Payments',
                  onPressed: () {
                    Get.dialog(const CustomPaymentDialog());
                  },
                ),
                XTextIconButton(
                  title: c.isFiltered.value ? "Filtered" : "Filter",
                  icon: Iconsax.filter_copy,
                  onPressed: () async {
                    await Get.dialog(const PaymentsFilterDialog());
                  },
                ),
              ],
              getColor: (data) {
                if (data.cancelledAt != null) {
                  return ThemeColors.error.withValues(alpha: 0.05);
                }
                return null;
              },
              columns: [
                TableColumn(
                  flex: 1,
                  title: t.payment.amount,
                  minWidth: 150,
                  builder: (data) {
                    final amountText =
                        '${data.amount} ${ConfigService.to.currency}';

                    if (data.cancelledAt != null) {
                      return Tooltip(
                        message:
                            '${t.paymentsView.cancelled}: ${data.cancellationReason ?? ''}',
                        child: Text(
                          amountText,
                          style: const TextStyle(
                            decoration: TextDecoration.lineThrough,
                            color: ThemeColors.error,
                          ),
                        ),
                      );
                    }

                    return TableColumn.stringBuilder(amountText, clip: false);
                  },
                ),
                TableColumn(
                  flex: 1,
                  title: t.payment.type,
                  minWidth: 100,
                  builder: (data) {
                    final alignment = Get.locale?.languageCode == 'ar'
                        ? Alignment.centerRight
                        : Alignment.centerLeft;

                    if (data.type == PaymentType.cash) {
                      return Align(
                        alignment: alignment,
                        child: const Icon(
                          Icons.attach_money,
                          color: ThemeColors.text,
                        ),
                      );
                    }
                    if (data.type == PaymentType.card) {
                      return Align(
                        alignment: alignment,
                        child: const Icon(
                          Icons.credit_card,
                          color: ThemeColors.text,
                        ),
                      );
                    }
                    if (data.type == PaymentType.valu) {
                      return Image.asset(
                        'assets/valu_logo.png',
                        height: 16,
                        alignment: alignment,
                      );
                    }
                    if (data.type == PaymentType.custom) {
                      return TableColumn.stringBuilder(
                          data.customPaymentMethod!.name.capitalizeFirst!);
                    }
                    return TableColumn.stringBuilder(
                        data.type.name.capitalize!);
                  },
                ),
                TableColumn(
                  flex: 2,
                  title: t.payment.branch,
                  minWidth: 150,
                  builder: (data) {
                    return TableColumn.stringBuilder(data.branch!.name);
                  },
                ),
                TableColumn(
                  flex: 2,
                  title: t.payment.createdBy,
                  minWidth: 150,
                  builder: (data) {
                    return TableColumn.stringBuilder(data.createdBy!.name);
                  },
                ),
                TableColumn(
                  flex: 2,
                  title: t.payment.patient,
                  minWidth: 150,
                  builder: (data) {
                    return TableColumn.stringBuilder(data.patient!.name);
                  },
                ),
                TableColumn(
                  flex: 2,
                  title: t.payment.createdAt,
                  minWidth: 150,
                  builder: (data) {
                    return TableColumn.stringBuilder(
                        Jiffy.parseFromDateTime(data.createdAt).yMMMEdjm);
                  },
                ),
                TableColumn(
                  title: '',
                  minWidth: 100,
                  builder: (data) {
                    return Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        IconButton(
                          icon: Icon(
                            Icons.cancel_outlined,
                            color: data.cancelledAt != null
                                ? ThemeColors.text.withValues(alpha: 0.05)
                                : ThemeColors.text,
                          ),
                          tooltip: t.paymentsView.cancel,
                          onPressed: data.cancelledAt != null
                              ? null
                              : () {
                                  Get.dialog(
                                      CancelPaymentDialog(paymentId: data.id));
                                },
                        ),
                      ],
                    );
                  },
                ),
              ],
            )),
        const SizedBox(height: 20),
        Obx(() {
          if (!c.next.value && c.currentPage.value <= 1) {
            return const SizedBox.shrink();
          }
          return Align(
            alignment: Alignment.centerRight,
            child: Container(
              width: 200,
              height: 50,
              padding: const EdgeInsets.symmetric(horizontal: 11, vertical: 5),
              decoration: BoxDecoration(
                color: ThemeColors.bg,
                borderRadius: BorderRadius.circular(13),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  if (c.currentPage.value > 1)
                    IconButton(
                      icon: const Icon(Icons.keyboard_arrow_left_rounded),
                      onPressed: c.loadPreviousPage,
                    ),
                  Text(
                    'Page ${c.currentPage.value}',
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF364152),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (c.next.value)
                    IconButton(
                      icon: const Icon(Icons.keyboard_arrow_right_rounded),
                      onPressed: c.loadPage,
                    ),
                ],
              ),
            ),
          );
        }),
      ],
    );
  }
}
