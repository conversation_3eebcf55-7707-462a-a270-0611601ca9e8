import 'package:core_app/core/api/custom_payments.dart';
import 'package:core_app/core/api/insurances.dart';
import 'package:core_app/core/api/payments.dart';
import 'package:core_app/core/helpers/api_handler.dart';
import 'package:core_app/core/models/insurance.dart';
import 'package:core_app/core/models/payment.dart';

import 'package:core_app/services/notify/messenger.dart';
import 'package:get/get.dart';

class _Treatment {
  String treatment;
  double price;
  double percentageCoverage;

  _Treatment({
    required this.treatment,
    required this.price,
    required this.percentageCoverage,
  });
}

class CreatePaymentController extends GetxController {
  final amount = ''.obs;
  final type = 'cash'.obs;

  final payingLink = ''.obs;

  final String patientId;
  final String branchId;
  final String visitId;

  final treatments = <_Treatment>[].obs;
  CreatePaymentController({
    required this.patientId,
    required this.branchId,
    required this.visitId,
    required List<String> treatmentsList,
  }) {
    treatments.value = treatmentsList
        .map<_Treatment>((e) => _Treatment(
              treatment: e,
              price: 0,
              percentageCoverage: 0,
            ))
        .toList();
  }
  final selectedCompany = ''.obs;
  final insurancePercentage = 0.0.obs;
  final insuranceCompanies = <InsuranceCompanyModel>[].obs;

  final selectedCustomPaymentId = ''.obs;
  final customPaymentMethods = <CustomPaymentModel>[].obs;

  void save() async {
    tryAPI(() async {
      final i = double.tryParse(amount.value);
      if (i == null) {
        NotifyService.notice(title: 'Invalid Amount');
        return;
      }
      PaymentModel result = await PaymentsAPI.create(
        amount: i,
        type: type.value,
        visitId: visitId,
        branchId: branchId,
        patientId: patientId,
        customPaymentMethodId: selectedCustomPaymentId.value,
      );
      if (result.paymentLink != null && result.paymentLink!.isNotEmpty) {
        payingLink.value = result.paymentLink!;
      }
      if (selectedCompany.isNotEmpty && treatments.isNotEmpty) {
        await InsurancesAPI.createClaim(
          visitId: visitId,
          companyId: selectedCompany.value,
          items: treatments
              .map<ClaimItemCreate>((e) => (
                    treatment: e.treatment,
                    price: e.price,
                    status: 'pending',
                    percentageCoverage: e.percentageCoverage,
                  ))
              .toList(),
        );
      }
      Get.back();
      NotifyService.notice(
          title: 'Payment Created', msgColor: MsgColor.success);
    });
  }

  @override
  void onInit() {
    super.onInit();
    InsurancesAPI.listCompanies().then((value) {
      insuranceCompanies.value = value;
    });
    CustomPaymentsAPI.list().then((value) {
      customPaymentMethods.value.clear();
      customPaymentMethods.value = value;
    });
  }
}
