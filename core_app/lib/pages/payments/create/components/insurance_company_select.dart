import 'package:core_app/pages/payments/create/controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

class InsuranceCompanySelectComponent extends StatelessWidget {
  const InsuranceCompanySelectComponent({super.key});

  @override
  Widget build(BuildContext context) {
    final c = Get.find<CreatePaymentController>();
    return Obx(() => Column(
          children: [
            DropdownButtonFormField<String>(
              value: c.selectedCompany.value,
              items: [
                const DropdownMenuItem(
                  value: '',
                  child: Text('Select Insurance Company'),
                ),
                for (final company in c.insuranceCompanies)
                  DropdownMenuItem(
                    value: company.id,
                    child: Text(company.name),
                  )
              ],
              onChanged: (value) {
                if (value == null || value.isEmpty) {
                  c.selectedCompany.value = '';
                  return;
                }
                c.selectedCompany.value = value;
                c.insurancePercentage.value = c.insuranceCompanies
                    .firstWhere((element) => element.id == value)
                    .defaultPercentageCoverage;
                for (final treatment in c.treatments) {
                  treatment.price = 0;
                  treatment.percentageCoverage = c.insurancePercentage.value;
                }
              },
            ),
            SizedBox(height: c.selectedCompany.isNotEmpty ? 11 : 0),
            if (c.selectedCompany.isNotEmpty)
              Obx(() => ListView.separated(
                    shrinkWrap: true,
                    itemCount: c.treatments.length,
                    physics: const NeverScrollableScrollPhysics(),
                    separatorBuilder: (context, index) => const Divider(
                      height: 26,
                      thickness: .3,
                      endIndent: 16,
                      indent: 16,
                    ),
                    itemBuilder: (context, index) {
                      final treatment = c.treatments[index];
                      return Column(
                        children: [
                          TextFormField(
                            readOnly: true,
                            mouseCursor: SystemMouseCursors.basic,
                            initialValue: treatment.treatment,
                            decoration: const InputDecoration(
                              labelText: 'Treatment',
                            ),
                          ),
                          const SizedBox(height: 11),
                          Row(
                            children: [
                              Flexible(
                                child: TextFormField(
                                  key: GlobalKey(),
                                  initialValue: treatment.price.toString(),
                                  onChanged: (val) => treatment.price = double.tryParse(val) ?? 0,
                                  inputFormatters: [
                                    FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                                  ],
                                  decoration: const InputDecoration(
                                    labelText: 'Price',
                                  ),
                                ),
                              ),
                              const SizedBox(width: 7),
                              Flexible(
                                child: TextFormField(
                                  key: Key(treatment.percentageCoverage.toString()),
                                  initialValue: treatment.percentageCoverage.toString(),
                                  onChanged: (val) =>
                                      treatment.percentageCoverage = double.tryParse(val) ?? 0,
                                  inputFormatters: [
                                    FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                                  ],
                                  decoration: const InputDecoration(
                                    labelText: 'Percentage Coverage',
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      );
                    },
                  )),
          ],
        ));
  }
}
