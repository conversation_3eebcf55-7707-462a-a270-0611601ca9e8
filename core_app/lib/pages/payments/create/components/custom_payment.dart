import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/pages/payments/create/controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class CustomPaymentSelectCompnent extends StatelessWidget {
  const CustomPaymentSelectCompnent({super.key});

  @override
  Widget build(BuildContext context) {
    final c = Get.find<CreatePaymentController>();
    return Obx(() {
      double flatFee = 0;
      double percentage = 0;
      double net = 0;
      for (final payment in c.customPaymentMethods) {
        if (payment.id == c.selectedCustomPaymentId.value) {
          flatFee = payment.flatFee;
          percentage = payment.percentageFee;
          net = double.tryParse(c.amount.value) ?? 0;
          if (net == 0) break;
          net -= flatFee;
          net -= (net * percentage) / 100;
          break;
        }
      }
      return Row(
        children: [
          Flexible(
            child: DropdownButtonFormField<String>(
              isExpanded: true,
              value: c.selectedCustomPaymentId.value,
              onChanged: (value) {
                if (value == null || value.isEmpty) {
                  c.selectedCustomPaymentId.value = '';
                  return;
                }
                c.selectedCustomPaymentId.value = value;
              },
              items: [
                const DropdownMenuItem(
                  value: '',
                  child: Text('Select Custom Payment'),
                ),
                for (final payment in c.customPaymentMethods)
                  DropdownMenuItem(
                    value: payment.id,
                    child: Text(payment.name),
                  )
              ],
            ),
          ),
          Flexible(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Flat Fee: $flatFee\$',
                      style: const TextStyle(
                        color: Colors.grey,
                        fontSize: 12,
                      ),
                    ),
                    Text(
                      'Percentage: $percentage%',
                      style: const TextStyle(
                        color: Colors.grey,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Text(
                      'Net',
                      style: TextStyle(
                        color: Colors.grey,
                        fontSize: 12,
                      ),
                    ),
                    Text(
                      net.toString() + r'$',
                      style: const TextStyle(
                        color: ThemeColors.text,
                        fontSize: 15,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          )
        ],
      );
    });
  }
}
