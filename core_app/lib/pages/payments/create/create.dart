import 'package:core_app/components/buttons/text.dart';
import 'package:core_app/components/dialogs/basic.dart';
import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/core/utils/i18n/translations.g.dart';
import 'package:core_app/pages/payments/create/controller.dart';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'components/custom_payment.dart';
import 'components/insurance_company_select.dart';

class CreatePaymentDialog extends StatelessWidget {
  const CreatePaymentDialog({
    super.key,
    this.hint,
    required this.patientId,
    required this.branchId,
    required this.visitId,
    required this.treatments,
  });

  final String? hint;
  final String patientId;
  final String branchId;
  final String visitId;
  final List<String> treatments;

  @override
  Widget build(BuildContext context) {
    final c = Get.put(CreatePaymentController(
      patientId: patientId,
      branchId: branchId,
      visitId: visitId,
      treatmentsList: treatments,
    ));
    return BasicDialog(
      title: t.paymentsView.newPayment,
      children: [
        TextFormField(
          initialValue: c.amount.value,
          onChanged: c.amount,
          decoration: InputDecoration(labelText: t.paymentsView.paidAmount),
        ),
        if (hint != null) const SizedBox(height: 11),
        if (hint != null)
          Text(
            hint!,
            style: const TextStyle(
              color: ThemeColors.text,
            ),
          ),
        const SizedBox(height: 11),
        DropdownButtonFormField(
          value: c.type.value,
          onChanged: (val) {
            c.type.value = val ?? 'cash';
          },
          items: [
            DropdownMenuItem(
              value: 'cash',
              child: Text(t.payment.types.cash),
            ),
            DropdownMenuItem(
              value: 'valu',
              child: Text(t.payment.types.valu),
            ),
            DropdownMenuItem(
              value: 'card',
              child: Text(t.payment.types.card),
            ),
            DropdownMenuItem(
              value: 'custom',
              child: Text(t.payment.types.custom),
            ),
          ],
        ),

        Obx(() => c.type.value != 'custom'
            ? const SizedBox()
            : const SizedBox(height: 11)),
        Obx(() => c.type.value != 'custom'
            ? const SizedBox()
            : const CustomPaymentSelectCompnent()),
        const SizedBox(height: 11),
        const InsuranceCompanySelectComponent(),
        const SizedBox(height: 11),
        // Obx(() => Column(
        //       children: c.payingLink.isEmpty
        //           ? []
        //           : [
        //               Container(
        //                 padding: const EdgeInsets.symmetric(
        //                   horizontal: 7,
        //                 ),
        //                 decoration: BoxDecoration(
        //                   borderRadius: BorderRadius.circular(11),
        //                   border: Border.all(
        //                     color: ThemeColors.notion,
        //                   ),
        //                 ),
        //                 child: Row(
        //                   mainAxisSize: MainAxisSize.min,
        //                   children: [
        //                     Expanded(
        //                       child: Text(
        //                         c.payingLink.value,
        //                         textAlign: TextAlign.center,
        //                         style: TextStyle(
        //                           color: ThemeColors.text.withOpacity(.81),
        //                         ),
        //                       ),
        //                     ),
        //                     const SizedBox(
        //                       height: 27,
        //                       child: VerticalDivider(
        //                         color: ThemeColors.notion,
        //                         thickness: 1,
        //                       ),
        //                     ),
        //                     if (UniversalPlatform.isDesktop)
        //                       IconButton(
        //                         onPressed: () {
        //                           c.payingLink.value = '';
        //                         },
        //                         icon: const Icon(
        //                           Icons.copy_outlined,
        //                           color: ThemeColors.notion,
        //                         ),
        //                       )
        //                     else
        //                       GestureDetector(
        //                         onTap: () {
        //                           c.payingLink.value = '';
        //                         },
        //                         child: const Icon(
        //                           Icons.share_outlined,
        //                           color: ThemeColors.notion,
        //                         ),
        //                       ),
        //                   ],
        //                 ),
        //               ),
        //               const SizedBox(height: 11),
        //               PrettyQr(
        //                 data: c.payingLink.value,
        //                 size: 200,
        //                 roundEdges: true,
        //               ),
        //               const SizedBox(height: 11),
        //             ],
        //     )),
        Align(
          alignment: Alignment.centerRight,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              ElevatedButton(
                child: Text(t.skip),
                onPressed: () {
                  Get.back();
                },
              ),
              XTextButton(
                title: t.buttonTxt.save,
                onPressed: c.save,
              ),
            ],
          ),
        )
      ],
    );
  }
}
