import 'package:core_app/components/dialogs/basic.dart';
import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/core/models/branch.dart';
import 'package:core_app/core/utils/i18n/translations.g.dart';
import 'package:flex_color_picker/flex_color_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:get/get.dart';
import 'package:latlong2/latlong.dart';

import 'controller.dart';

class BranchFormDialog extends StatelessWidget {
  const BranchFormDialog({
    this.branch,
    super.key,
  });

  final BranchModel? branch;

  @override
  Widget build(BuildContext context) {
    final c = Get.put(BranchConroller(branch));
    return BasicDialog(
      title: branch == null ? t.branches.add : t.branches.edit,
      children: [
        TextFormField(
          controller: c.name,
          decoration: InputDecoration(
            labelText: t.branches.name,
          ),
        ),
        const SizedBox(height: 13),
        TextFormField(
          controller: c.rooms,
          keyboardType: TextInputType.number,
          decoration: InputDecoration(
            labelText: t.branches.roomsCount,
          ),
        ),
        const SizedBox(height: 13),
        Wrap(
          spacing: 3,
          runSpacing: 3,
          children: [
            TextButton(
              child: Text(t.branches.mapView),
              onPressed: () {
                c.mapView.value = 0;
              },
            ),
            TextButton(
              child: Text(t.branches.googleLink),
              onPressed: () {
                c.mapView.value = 1;
              },
            ),
            TextButton(
              child: Text(t.branches.clear),
              onPressed: () {
                c.branchLocation.value = null;
              },
            ),
          ],
        ),
        const SizedBox(height: 13),
        Obx(() => AnimatedSwitcher(
            duration: kThemeAnimationDuration,
            child: [
              ClipRRect(
                borderRadius: BorderRadius.circular(23),
                child: SizedBox(
                  height: 250,
                  child: FlutterMap(
                    mapController: c.mapController,
                    options: MapOptions(
                      initialZoom: 10,
                      initialCenter:
                          c.branchLocation.value ?? const LatLng(31.5, 34.5),
                      onTap: (tapPosition, point) {
                        c.branchLocation.value = point;
                      },
                    ),
                    children: [
                      TileLayer(
                        urlTemplate:
                            'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                        userAgentPackageName: 'com.payrows.naab',
                        maxZoom: 21,
                      ),
                      Obx(() => MarkerLayer(
                            markers: [
                              if (c.branchLocation.value != null)
                                Marker(
                                  point: c.branchLocation.value!,
                                  width: 80,
                                  height: 80,
                                  child: const Icon(
                                    Icons.circle_outlined,
                                    color: ThemeColors.primary,
                                    size: 21,
                                  ),
                                ),
                            ],
                          )),
                    ],
                  ),
                ),
              ),
              TextFormField(
                decoration: InputDecoration(
                  labelText: t.branches.googleMapURL,
                ),
                onChanged: (val) {
                  if (val.isEmpty) return;
                  final direct = RegExp(
                      r'google\.com\.?(?:[a-z]+)?\/maps\/@([^a-zA-Z,]+),([^a-zA-Z,]+)');
                  final place = RegExp(
                      r'google\.com\.?(?:[a-z]+)?\/maps\/place\/.+\/@([^a-zA-Z,]+),([^a-zA-Z,]+)');
                  final shareLocation = RegExp(
                      r'google\.com\.?(?:[a-z]+)?\/maps\/place\/.+\/@([^a-zA-Z,]+),([^a-zA-Z,]+)');
                  final miniShareLocation = RegExp(
                      r'google\.com\.?(?:[a-z]+)?\/maps\/?\?q=([^a-zA-Z,]+),([^a-zA-Z,]+)');
                  final match = direct.firstMatch(val) ??
                      place.firstMatch(val) ??
                      shareLocation.firstMatch(val) ??
                      miniShareLocation.firstMatch(val);
                  // print(direct.firstMatch(val));
                  // print(shareLocation.firstMatch(val));
                  // print(miniShareLocation.firstMatch(val));
                  // print(place.firstMatch(val));
                  if (match == null) return;
                  final lat = double.parse(match.group(1)!);
                  final lng = double.parse(match.group(2)!);
                  c.branchLocation.value = LatLng(lat, lng);
                },
              ),
            ][c.mapView.value])),
        const SizedBox(height: 13),
        Obx(() => ColorPicker(
              width: 33,
              height: 33,
              borderRadius: 22,
              enableShadesSelection: false,
              color: c.selectedColor.value,
              crossAxisAlignment: CrossAxisAlignment.start,
              heading: Text(
                t.branches.selectColor,
                style: const TextStyle(
                  fontSize: 18,
                  color: ThemeColors.text,
                  fontWeight: FontWeight.w600,
                ),
              ),
              pickersEnabled: const {
                ColorPickerType.accent: false,
                ColorPickerType.primary: true,
              },
              onColorChanged: (Color color) {
                c.selectedColor.value = color;
                c.selectedHexColor = color.hex;
              },
            )),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            // XTextButton(
            //   title: (branch == null ? "add" : "save").tr,
            //   onPressed: () => branch == null ? create(c) : update(c),
            // ),
            ElevatedButton(
              onPressed: branch == null ? c.createNew : c.updateDate,
              child: Text(
                branch == null ? t.buttonTxt.add : t.buttonTxt.save,
              ),
            )
          ],
        ),
      ],
    );
  }
}
