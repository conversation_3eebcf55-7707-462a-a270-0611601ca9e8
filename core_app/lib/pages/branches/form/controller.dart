import 'package:core_app/core/api/branches.dart';
import 'package:core_app/core/helpers/api_handler.dart';
import 'package:core_app/core/helpers/hex_convertor.dart';
import 'package:core_app/core/models/branch.dart';

import 'package:core_app/services/notify/messenger.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:latlong2/latlong.dart';

class BranchConroller extends GetxController {
  final BranchModel? branch;
  static BranchConroller get to => Get.find();
  BranchConroller(this.branch);

  final mapView = 0.obs;
  final mapController = MapController();
  // ignore: unnecessary_cast
  final selectedColor = (Colors.blue as Color).obs;
  String? selectedHexColor;
  final branchLocation = Rx<LatLng?>(const LatLng(30.0594885, 31.2584644));
  final TextEditingController name = TextEditingController();
  final TextEditingController rooms = TextEditingController(text: '1');

  @override
  void onInit() {
    if (branch != null) {
      name.text = branch!.name;
      rooms.text = branch!.rooms.toString();
      selectedColor.value = branch!.getColor;
      selectedHexColor = HexConvertor.toHex(branch!.getColor);
      if (branch!.latitude != null && branch!.longitude != null) {
        branchLocation.value = LatLng(
          branch!.latitude!,
          branch!.longitude!,
        );
      }
    }
    _determinePosition().then((value) {
      if (kDebugMode) print("Position Found: $value");
      // check if the branch location is the default location
      if (branchLocation.value.toString() ==
          const LatLng(30.0594885, 31.2584644).toString()) {
        var latLng = LatLng(value.latitude, value.longitude);
        branchLocation.value = latLng;
        mapController.move(latLng, 16);
      }
    });
    super.onInit();
  }

  Future<Position> _determinePosition() async {
    bool serviceEnabled;
    LocationPermission permission;

    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      NotifyService.notice(
        title: 'Location services are disabled.',
        msgColor: MsgColor.danger,
      );
      return Future.error('Location services are disabled.');
    }

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        NotifyService.notice(
          title: 'Location permissions are denied.',
          msgColor: MsgColor.danger,
        );
        return Future.error('Location permissions are denied');
      }
    }
    if (permission == LocationPermission.deniedForever) {
      NotifyService.notice(
        title:
            'Location permissions are permanently denied, we cannot request permissions.',
        msgColor: MsgColor.danger,
      );
      return Future.error(
          'Location permissions are permanently denied, we cannot request permissions.');
    }
    if (!serviceEnabled) {
      NotifyService.notice(
        title: 'Location services are disabled.',
        msgColor: MsgColor.danger,
      );
      return Future.error('Location services are disabled.');
    }

    return await Geolocator.getCurrentPosition();
  }

  void createNew() {
    if (name.text.isEmpty) return;
    tryAPI(() async {
      BranchModel b = await BranchesAPI.create(
        name: name.text.capitalizeFirst!,
        hexColor: selectedHexColor,
        rooms: int.tryParse(rooms.text) ?? 1,
        latitude: branchLocation.value?.latitude,
        longitude: branchLocation.value?.longitude,
      );
      Get.back(result: b);
    });
  }

  void updateDate() {
    if (name.text.isEmpty) return;
    tryAPI(() async {
      BranchModel b = await BranchesAPI.update(
        branch!.id,
        name: name.text.capitalizeFirst!,
        hexColor: selectedHexColor,
        rooms: int.tryParse(rooms.text) ?? 1,
        latitude: branchLocation.value?.latitude,
        longitude: branchLocation.value?.longitude,
      );
      Get.back(result: b);
    });
  }
}
