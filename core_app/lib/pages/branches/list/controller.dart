import 'package:core_app/core/api/branches.dart';
import 'package:core_app/core/models/branch.dart';
import 'package:get/get.dart';

class BranchesListController extends GetxController {
  static BranchesListController get to => Get.find();

  final branches = <BranchModel>[].obs;

  @override
  void onInit() {
    fetch();
    super.onInit();
  }

  void fetch() async {
    final result = await BranchesAPI.list();
    branches.value = result;
  }
}
