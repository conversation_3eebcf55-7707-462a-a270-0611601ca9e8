import 'package:core_app/components/buttons/text_icon.dart';
import 'package:core_app/components/tables/table.dart';
import 'package:core_app/core/models/branch.dart';
import 'package:core_app/core/utils/i18n/translations.g.dart';
import 'package:core_app/layout/page_layout.dart';
import 'package:core_app/pages/branches/form/form.dart';
import 'package:core_app/pages/branches/list/controller.dart';

import 'package:core_app/services/auth.dart';
import 'package:core_app/services/notify/messenger.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class BranchesListPage extends StatelessWidget {
  const BranchesListPage({super.key});

  void _update(c, data) async {
    if (AuthService.to.isSubEnded) {
      NotifyService.notice(title: t.subEnded);
      return;
    }
    BranchModel? repo = await Get.dialog(BranchFormDialog(branch: data));
    if (repo == null) return;
    c.fetch();
    NotifyService.success(t.branches.branchUpdated);
  }

  @override
  Widget build(BuildContext context) {
    final c = Get.find<BranchesListController>();
    return PageLayout(
      children: [
        Obx(() => TableComponent<BranchModel>(
              title: t.branches.listBranches,
              data: c.branches.value,
              onRowTap: (data) => _update(c, data),
              actions: [
                XTextIconButton(
                  title: t.branches.add,
                  icon: Iconsax.add_copy,
                  onPressed: () async {
                    if (AuthService.to.isSubEnded) {
                      NotifyService.notice(title: t.subEnded);
                      return;
                    }
                    BranchModel? repo =
                        await Get.dialog(const BranchFormDialog());
                    if (repo == null) return;
                    c.fetch();
                    NotifyService.success(t.branches.branchAdded);
                  },
                ),
              ],
              columns: [
                TableColumn(
                  flex: 4,
                  title: t.branches.name,
                  minWidth: 200,
                  builder: (data) {
                    return TableColumn.stringBuilder(data.name);
                  },
                ),
                TableColumn(
                  title: '',
                  minWidth: 50,
                  builder: (data) {
                    return Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        IconButton(
                          iconSize: 18,
                          icon: const Icon(
                            Iconsax.edit_copy,
                            size: 18,
                          ),
                          onPressed: () => _update(c, data),
                        ),
                      ],
                    );
                  },
                ),
              ],
            )),
      ],
    );
  }
}
