import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:core_app/components/dialogs/feature_promo_dialog.dart';
import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/core/utils/version_manager.dart';

class TestFeaturePromoPage extends StatelessWidget {
  const TestFeaturePromoPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Feature Promo Test'),
        backgroundColor: ThemeColors.primary,
        foregroundColor: ThemeColors.textLight,
      ),
      body: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Feature Promo Dialog Test',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: ThemeColors.text,
              ),
            ),
            const SizedBox(height: 20),
            const Text(
              'This page allows you to test the feature promo dialog functionality.',
              style: TextStyle(
                fontSize: 16,
                color: ThemeColors.notion,
              ),
            ),
            const SizedBox(height: 40),

            // Test buttons
            ElevatedButton(
              onPressed: () async {
                await FeaturePromoService.checkAndShowFeaturePromo();
              },
              child: const Text('Check & Show Latest Feature Promo'),
            ),

            const SizedBox(height: 16),

            ElevatedButton(
              onPressed: () async {
                final content = await VersionManager.getLatestVersion();
                if (content != null) {
                  final version =
                      VersionManager.extractVersionFromContent(content);
                  if (version != null) {
                    await Get.dialog(
                      FeaturePromoDialog(
                        content: content,
                        version: version,
                      ),
                    );
                  }
                }
              },
              child: const Text('Force Show Latest Promo'),
            ),

            const SizedBox(height: 16),

            ElevatedButton(
              onPressed: () {
                final lastShown = VersionManager.getLastShownVersion();
                Get.snackbar(
                  'Last Shown Version',
                  lastShown ?? 'No version shown yet',
                  backgroundColor: ThemeColors.primaryLighter,
                  colorText: ThemeColors.primaryDark,
                );
              },
              child: const Text('Show Last Shown Version'),
            ),

            const SizedBox(height: 16),

            ElevatedButton(
              onPressed: () {
                // Clear the stored version to test again
                GetStorage().remove('last_shown_feature_version');
                Get.snackbar(
                  'Reset',
                  'Version history cleared. You can now test the promo again.',
                  backgroundColor: ThemeColors.successLighter,
                  colorText: ThemeColors.successDark,
                );
              },
              child: const Text('Reset Version History'),
            ),

            const SizedBox(height: 40),

            // Information section
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: ThemeColors.bgDark,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: ThemeColors.primary.withValues(alpha: 0.1),
                ),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'How it works:',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: ThemeColors.primary,
                    ),
                  ),
                  SizedBox(height: 12),
                  Text(
                    '• The system automatically checks for new feature promo files in assets/features_promo/\n'
                    '• Files should be named with version numbers (e.g., 1.0.0.md, 1.5.6.md)\n'
                    '• Only the latest version will be shown to users\n'
                    '• Once shown, a version won\'t be displayed again\n'
                    '• The dialog supports markdown formatting with headers, lists, and basic text',
                    style: TextStyle(
                      fontSize: 14,
                      color: ThemeColors.notion,
                      height: 1.5,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
