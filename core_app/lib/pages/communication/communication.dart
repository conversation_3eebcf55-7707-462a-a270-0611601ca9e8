import 'package:core_app/core/api/communication.dart';
import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/core/helpers/api_handler.dart';
import 'package:core_app/core/utils/responsive.dart';
import 'package:core_app/layout/page_layout.dart';
import 'package:core_app/pages/communication/controller.dart';
import 'package:core_app/services/config.dart';
import 'package:core_app/services/notify/messenger.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class CommunicationPage extends StatelessWidget {
  const CommunicationPage({super.key});

  @override
  Widget build(BuildContext context) {
    return PageLayout(
      title: 'Communication',
      children: [
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: ThemeColors.bg,
            borderRadius: BorderRadius.circular(10),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                'Communication',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              Text(
                ' /SMS Messaging',
                style: Theme.of(context).textTheme.labelSmall,
              ),
            ],
          ),
        ),
        const SizedBox(height: 10),
        Container(
          margin: const EdgeInsets.all(10),
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            color: ThemeColors.bg,
            borderRadius: BorderRadius.circular(10),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding:
                    const EdgeInsets.symmetric(vertical: 5, horizontal: 13),
                child: Text(
                  'Settings',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ),
              const SizedBox(height: 10),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 13),
                child: Row(
                  children: [
                    Expanded(
                      flex: 1,
                      child: Row(
                        children: [
                          const Text('Birthday Wishes'),
                          const SizedBox(width: 5),
                          Tooltip(
                            message:
                                'Automatically send birthday wishes to patients',
                            child: Icon(
                              Icons.info_outline,
                              size: 16,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      child: Obx(() => DropdownButtonFormField<String>(
                            decoration: InputDecoration(
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                              ),
                              contentPadding:
                                  const EdgeInsets.symmetric(horizontal: 10),
                            ),
                            value: CommunicationController
                                .to.birthdayWishSetting.value,
                            items: const [
                              DropdownMenuItem(
                                  value: 'disabled', child: Text('Disabled')),
                              DropdownMenuItem(
                                  value: 'en', child: Text('English')),
                              DropdownMenuItem(
                                  value: 'ar', child: Text('Arabic')),
                            ],
                            onChanged: (value) {
                              if (value != null) {
                                final c = CommunicationController.to;
                                c.updateBirthdayWishSetting(value);
                              }
                            },
                          )),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        Wrap(
          children: [
            FractionallySizedBox(
              widthFactor: context.mobileView ? 1 : .49,
              child: const _PeopleToSent(),
            ),
            FractionallySizedBox(
              widthFactor: context.mobileView ? 1 : .49,
              child: const _MessageToSent(),
            ),
          ],
        )
      ],
    );
  }
}

class _PeopleToSent extends StatelessWidget {
  const _PeopleToSent();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final c = CommunicationController.to;
    return Container(
      margin: const EdgeInsets.all(10),
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: ThemeColors.bg,
        borderRadius: BorderRadius.circular(10),
      ),
      child: ListView(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 13),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'People to sent',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                // trailing with number of people
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // clear all button
                    IconButton(
                      icon: const Icon(
                        Icons.clear_all,
                        color: ThemeColors.primary,
                      ),
                      onPressed: () {
                        c.sendToAll.value = false;
                        c.peopleToSent.clear();
                      },
                    ),
                    const SizedBox(width: 5),
                    Container(
                      padding: const EdgeInsets.all(5),
                      decoration: BoxDecoration(
                        color: ThemeColors.primary,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Obx(() => Text(
                            c.sendToAll.value
                                ? 'All Patients Selected'
                                : c.peopleToSent.length.toString(),
                            style: theme.textTheme.labelSmall?.copyWith(
                              color: theme.colorScheme.onPrimary,
                            ),
                          )),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 3),
          TextFormField(
            controller: c.searchQuery,
            decoration: InputDecoration(
              hintText: 'Search',
              helperText:
                  'Search with `\$` to find all people with overdue balance.',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          ),
          const SizedBox(height: 3),
          // select all text button
          Row(
            children: [
              TextButton(
                onPressed: () {
                  c.peopleToSent.assignAll(c.searchResult);
                },
                child: const Text('Select all'),
              ),
              const SizedBox(width: 5),
              TextButton(
                onPressed: () {
                  c.peopleToSent.clear();
                },
                child: const Text('Deselect All'),
              ),
            ],
          ),
          Obx(() {
            final results = c.searchResult.value;
            final peopleToSent = c.peopleToSent.value;
            if (results.isEmpty && c.searchQuery.text.isNotEmpty) {
              return const Center(
                child: Text('No results found'),
              );
            }
            if (peopleToSent.isEmpty && c.searchQuery.text.isEmpty) {
              return Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const SizedBox(height: 13),
                    const Text('No people selected'),
                    const SizedBox(height: 3),
                    TextButton(
                      child: const Text(
                        'Select all patients',
                        style: TextStyle(fontSize: 13),
                      ),
                      onPressed: () {
                        c.sendToAll.value = true;
                      },
                    )
                  ],
                ),
              );
            }
            return ListView.separated(
              shrinkWrap: true,
              itemCount: c.searchQuery.text.isEmpty
                  ? peopleToSent.length
                  : results.length,
              separatorBuilder: (context, index) => const Divider(
                height: 1,
                indent: 40,
                endIndent: 40,
                thickness: .3,
              ),
              physics: const NeverScrollableScrollPhysics(),
              itemBuilder: (context, index) {
                final item = (c.searchQuery.text.isEmpty
                    ? peopleToSent
                    : results)[index];
                final isSelected = peopleToSent.any((e) => e.id == item.id);
                return ListTile(
                  title: Text(item.name),
                  subtitle: Text.rich(
                    TextSpan(
                      text: item.phoneNumber,
                      style: theme.textTheme.labelSmall,
                      children: [
                        const TextSpan(text: ' | '),
                        TextSpan(
                          text: '${item.balance} ${ConfigService.to.currency}',
                          style: theme.textTheme.labelSmall?.copyWith(
                            color: theme.colorScheme.primary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  trailing: IconButton(
                    icon: isSelected
                        ? const Icon(Icons.check_box)
                        : const Icon(Icons.check_box_outline_blank),
                    onPressed: () {
                      if (isSelected) {
                        c.peopleToSent.removeWhere((e) => e.id == item.id);
                      } else {
                        c.peopleToSent.add(item);
                      }
                    },
                  ),
                );
              },
            );
          })
        ],
      ),
    );
  }
}

class _MessageToSent extends StatelessWidget {
  const _MessageToSent();

  @override
  Widget build(BuildContext context) {
    final c = CommunicationController.to;
    return Container(
      margin: const EdgeInsets.all(10),
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: ThemeColors.bg,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 13),
            child: Text(
              'Message to sent',
              style: Theme.of(context).textTheme.titleMedium,
            ),
          ),
          const SizedBox(height: 10),
          TextFormField(
            minLines: 5,
            maxLines: 8,
            onChanged: c.message,
            decoration: InputDecoration(
              hintText: 'Type your message here',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          ),
          const SizedBox(height: 10),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Expanded(
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                      minimumSize: const Size(double.infinity, 40)),
                  child: const Text('Send'),
                  onPressed: () async {
                    tryAPI(() async {
                      final r = await CommunicationAPI.sendSMS(
                        all: c.sendToAll.value,
                        recipients: c.peopleToSent.map((e) => e.id).toList(),
                        message: c.message.value,
                      );
                      NotifyService.success(r);
                    });
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
