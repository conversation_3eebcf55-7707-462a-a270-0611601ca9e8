import 'dart:async';

import 'package:core_app/core/api/communication.dart';
import 'package:core_app/core/api/patients.dart';
import 'package:core_app/core/helpers/api_handler.dart';
import 'package:core_app/core/models/patient.dart';
import 'package:core_app/services/notify/messenger.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class CommunicationController extends GetxController {
  static CommunicationController get to => Get.find();

  final sendToAll = false.obs;
  final peopleToSent = <PatientModel>[].obs;

  final searchQuery = TextEditingController();
  final searchResult = <PatientModel>[].obs;
  Timer? _debounce;

  final message = ''.obs;
  final birthdayWishSetting = 'disabled'.obs;

  @override
  void onInit() {
    super.onInit();
    searchQuery.addListener(_onSearchQueryChanged);
    fetchBirthdayWishSetting();
  }

  Future<void> fetchBirthdayWishSetting() async {
    try {
      final birthdayWish = await CommunicationAPI.getBirthdayWish();

      // Handle the response from the backend
      if (birthdayWish == null || birthdayWish.isEmpty) {
        birthdayWishSetting.value = 'disabled';
      } else if (birthdayWish == 'en' || birthdayWish.contains('English')) {
        birthdayWishSetting.value = 'en';
      } else if (birthdayWish == 'ar' || birthdayWish.contains('Arabic')) {
        birthdayWishSetting.value = 'ar';
      } else {
        // Default to disabled if we get an unexpected value
        birthdayWishSetting.value = 'disabled';
      }
    } catch (e) {
      birthdayWishSetting.value = 'disabled';
    }
  }

  @override
  void onClose() {
    searchQuery.removeListener(_onSearchQueryChanged);
    searchQuery.dispose();
    super.onClose();
  }

  void _onSearchQueryChanged() async {
    if (_debounce?.isActive ?? false) _debounce?.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () async {
      if (searchQuery.text.isEmpty) {
        searchResult.clear();
        return;
      }
      if (searchQuery.text.trim() == r'$') {
        final result = await PatientsAPI.listOverdue();
        searchResult.assignAll(result);
        return;
      }
      final result = await PatientsAPI.list(1, searchQuery.text);
      searchResult.assignAll(result);
    });
  }

  void updateBirthdayWishSetting(String value) async {
    // Set the value immediately to provide instant feedback in the UI
    birthdayWishSetting.value = value;

    final bool enabled = value != 'disabled';
    final String language = enabled ? value : '';

    await tryAPI(() async {
      final response = await CommunicationAPI.toggleBirthdayWish(
        enabled: enabled,
        language: language,
      );
      // Refresh the setting from the server
      await fetchBirthdayWishSetting();
      NotifyService.success('Birthday wish settings updated successfully');
      return response;
    });
  }
}
