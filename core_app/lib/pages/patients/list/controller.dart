import 'package:core_app/core/api/patients.dart';
import 'package:core_app/core/helpers/api_handler.dart';
import 'package:core_app/core/models/patient.dart';
import 'package:get/get.dart';

class PateintsListController extends GetxController {
  final next = true.obs;
  final currentPage = 0.obs; // limit per page = 20
  final patients = <int, List<PatientModel>>{1: []}.obs;

  void loadPage([String? q]) {
    if (q != null && q.isNotEmpty) {
      tryAPI(() async {
        patients.clear();
        currentPage.value = 1;
        patients[currentPage.value] = await PatientsAPI.list(currentPage.value, q);
        next.value = false;
      });
    } else {
      // if (searching) {
      //   patients.clear();
      //   currentPage = 0;
      //   searching = false;
      // }
      currentPage.value++;
      tryAPI(() async {
        final list = await PatientsAPI.list(currentPage.value);
        if (list.length < 20) {
          patients[currentPage.value] = list;
          next.value = false;
        } else {
          patients[currentPage.value] = list;
          next.value = true;
        }
      });
    }
  }

  loadPreviousPage() {
    if (currentPage.value == 1) return;
    currentPage.value--;
    next.value = true;
  }

  @override
  void onInit() {
    loadPage();
    super.onInit();
  }
}
