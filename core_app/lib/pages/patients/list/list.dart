import 'package:core_app/components/buttons/text_icon.dart';
import 'package:core_app/components/tables/table.dart';
import 'package:core_app/core/api/patients.dart';
import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/core/helpers/api_handler.dart';
import 'package:core_app/core/models/patient.dart';
import 'package:core_app/core/utils/i18n/translations.g.dart';
import 'package:core_app/layout/page_layout.dart';
import 'package:core_app/pages/patients/form/form.dart';
import 'package:core_app/pages/patients/list/controller.dart';
import 'package:core_app/routes/navigation.dart';

import 'package:core_app/routes/route_paths.dart';
import 'package:core_app/services/auth.dart';
import 'package:core_app/services/notify/messenger.dart';
import 'package:flutter/foundation.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:jiffy/jiffy.dart';

class PatientsListPage extends StatelessWidget {
  const PatientsListPage({super.key});

  @override
  Widget build(BuildContext context) {
    final c = Get.find<PateintsListController>();
    return Obx(() {
      final patients = c.patients.value[c.currentPage.value] ?? [];
      return PageLayout(
        children: [
          TableComponent<PatientModel>(
            title: t.patientsList.title,
            onRowTap: (data) {
              Navigation.to(Routes.PatientProfile, {
                'patientID': data.id,
              });
            },
            actions: [
              XTextIconButton(
                title: t.patientsList.add,
                icon: Iconsax.add_copy,
                onPressed: () async {
                  PatientModel? p =
                      await Get.dialog<PatientModel>(const PatientForm(null));
                  if (p != null) {
                    patients.insert(0, p);
                    c.patients.refresh();
                  }
                },
              ),
              XTextIconButton(
                title: t.overduePatients.title,
                icon: Iconsax.timer_copy,
                onPressed: () async {
                  Navigation.to(Routes.OverduePatients);
                },
              ),
              XTextIconButton(
                title: t.patientGroups.singleTitle,
                icon: Iconsax.people_copy,
                onPressed: () async {
                  Navigation.to(Routes.PatientGroups);
                },
              ),
            ],
            data: patients,
            columns: [
              TableColumn(
                flex: 3,
                title: t.patientForm.name,
                minWidth: 200,
                builder: (data) {
                  return TableColumn.stringBuilder(data.name);
                },
              ),
              TableColumn(
                flex: 2,
                minWidth: 150,
                title: t.patientForm.phone,
                builder: (data) {
                  return TableColumn.stringBuilder(data.phoneNumber);
                },
              ),
              if (kDebugMode)
                TableColumn(
                  flex: 2,
                  title: t.patientGroups.nameInList,
                  minWidth: 200,
                  builder: (data) {
                    return TableColumn.stringBuilder(
                      data.patientGroups?.isEmpty ?? true
                          ? 'N/A'
                          : data.patientGroups!.map((e) => e.name).join(', '),
                    );
                  },
                ),
              TableColumn(
                flex: 2,
                title: t.patientForm.birthdate,
                minWidth: 150,
                builder: (data) {
                  return TableColumn.stringBuilder(data.birthdate == null
                      ? 'N/A'
                      : Jiffy.parseFromDateTime(data.birthdate!).yMMMMd);
                },
              ),
              TableColumn(
                title: '',
                minWidth: 100,
                builder: (data) {
                  return Row(
                    // mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      IconButton(
                        iconSize: 18,
                        icon: const Icon(
                          Iconsax.edit_copy,
                          size: 18,
                        ),
                        onPressed: () async {
                          if (AuthService.to.isSubEnded) {
                            NotifyService.notice(title: t.subEnded);
                            return;
                          }
                          PatientModel? repo =
                              await Get.dialog<PatientModel>(PatientForm(data));
                          if (repo != null) {
                            patients.remove(data);
                            patients.add(repo);
                          }
                        },
                      ),
                      IconButton(
                        iconSize: 18,
                        icon: const Icon(
                          Iconsax.trash_copy,
                          size: 18,
                        ),
                        onPressed: () {
                          if (AuthService.to.isSubEnded) {
                            NotifyService.notice(title: t.subEnded);
                            return;
                          }
                          tryAPI(() async {
                            await PatientsAPI.delete(data.id);
                            patients.remove(data);
                            c.patients.refresh();
                          });
                        },
                      ),
                    ],
                  );
                },
              ),
            ],
          ),
          const SizedBox(height: 20),
          Obx(() {
            if (!c.next.value && c.currentPage.value <= 1) {
              return const SizedBox.shrink();
            }
            return Align(
              alignment: Alignment.centerRight,
              child: Container(
                width: 200,
                height: 50,
                padding:
                    const EdgeInsets.symmetric(horizontal: 11, vertical: 5),
                decoration: BoxDecoration(
                  color: ThemeColors.bg,
                  borderRadius: BorderRadius.circular(13),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    if (c.currentPage.value > 1)
                      IconButton(
                        icon: const Icon(Icons.keyboard_arrow_left_rounded),
                        onPressed: c.loadPreviousPage,
                      ),
                    Text(
                      'Page ${c.currentPage.value}',
                      style: const TextStyle(
                        fontSize: 14,
                        color: Color(0xFF364152),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    if (c.next.value)
                      IconButton(
                        icon: const Icon(Icons.keyboard_arrow_right_rounded),
                        onPressed: c.loadPage,
                      ),
                  ],
                ),
              ),
            );
          }),
        ],
      );
    });
  }
}
