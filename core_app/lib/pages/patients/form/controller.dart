import 'package:core_app/core/api/patients.dart';
import 'package:core_app/core/helpers/api_handler.dart';
import 'package:core_app/core/models/patient.dart';
import 'package:core_app/core/models/patient_group.dart';

import 'package:core_app/services/api.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class PatientFormController extends GetxController {
  final PatientModel? patient;
  PatientFormController(this.patient);

  DateTime birth = DateTime.now();
  final groupsSearchController = SearchController();
  final TextEditingController fileNumber = TextEditingController();
  final TextEditingController name = TextEditingController();
  final TextEditingController phone = TextEditingController();
  final TextEditingController email = TextEditingController();
  final TextEditingController job = TextEditingController();
  final TextEditingController address = TextEditingController();
  final TextEditingController balance = TextEditingController();
  final TextEditingController dentalHistory = TextEditingController();
  final TextEditingController medicalHistory = TextEditingController();
  final TextEditingController currentBirth = TextEditingController();
  final TextEditingController treatmentPlan = TextEditingController();
  final TextEditingController customReachChannel = TextEditingController();

  final choosenGroups = RxList<PatientGroup>();

  final reachChannel = ''.obs;

  setBirth(DateTime b) {
    birth = b;
    currentBirth.text = "${b.year}/${b.month}/${b.day}";
  }

  Future<void> create() async {
    tryAPI(() async {
      PatientModel p = await PatientsAPI.create(
        name: name.text,
        fileNumber: fileNumber.text,
        phoneNumber: phone.text,
        address: address.text,
        email: email.text,
        birthdate: birth,
        customReachChannel: customReachChannel.text,
        medicalHistory: medicalHistory.text,
        dentalHistory: dentalHistory.text,
        job: job.text,
        reachChannel: treatmentPlan.text,
      );
      for (var e in choosenGroups) {
        await ApiService.post('/patient-groups/${e.id}/members', data: {
          'patientId': p.id,
        });
      }
      Get.back(result: p);
    });
  }

  Future<void> updatedata() async {
    tryAPI(() async {
      PatientModel p = await PatientsAPI.update(
        patient!.id,
        name: name.text,
        customReachChannel: customReachChannel.text,
        email: email.text,
        fileNumber: fileNumber.text,
        phoneNumber: phone.text,
        address: address.text,
        treatmentPlan: treatmentPlan.text,
        birthdate: birth,
        medicalHistory: medicalHistory.text,
        dentalHistory: dentalHistory.text,
        job: job.text,
        reachChannel: reachChannel.isNotEmpty
            ? reachChannel.value
            : patient!.reachChannel,
      );
      Get.back(result: p);
    });
  }

  final groups = <PatientGroup>[].obs;
  @override
  Future<void> onInit() async {
    if (patient != null) {
      fileNumber.text = patient!.fileNumber;
      name.text = patient!.name;
      email.text = patient!.email ?? '';
      address.text = patient!.address;
      job.text = patient!.job;
      balance.text = patient!.balance.toString();
      phone.text = patient!.phoneNumber;
      dentalHistory.text = patient!.dentalHistory;
      medicalHistory.text = patient!.medicalHistory;
      treatmentPlan.text = patient!.treatmentPlan;
      reachChannel.value = patient!.reachChannel;
      choosenGroups.value = patient!.patientGroups ?? [];
      if (patient!.birthdate != null) setBirth(patient!.birthdate!);
    }
    tryAPI(() async {
      final res = await ApiService.get('/patient-groups/');
      if (res.data?['patientGroups'] != null) {
        groups.value = (res.data?['patientGroups'] as List)
            .map((e) => PatientGroup.fromJson(e))
            .toList();
      }
    });
    super.onInit();
  }
}
