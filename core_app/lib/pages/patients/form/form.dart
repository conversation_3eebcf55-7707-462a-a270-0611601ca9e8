import 'package:core_app/components/dialogs/basic.dart';
import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/core/helpers/api_handler.dart';
import 'package:core_app/core/models/patient.dart';
import 'package:core_app/core/models/patient_group.dart';
import 'package:core_app/core/utils/i18n/translations.g.dart';
import 'package:core_app/services/api.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';

import 'controller.dart';

class PatientForm extends StatelessWidget {
  const PatientForm(this.patient, {super.key});

  final PatientModel? patient;

  @override
  Widget build(BuildContext context) {
    final bool editing = patient != null;
    final c = Get.put(PatientFormController(patient));
    return BasicDialog(
      width: context.isPhone ? 500 : 800,
      title: t.patientsList.title,
      children: [
        Form(
          child: Wrap(
            spacing: 11,
            runSpacing: 11,
            children: [
              FractionallySizedBox(
                widthFactor: context.isPhone ? 1 : .49,
                child: Column(
                  children: [
                    _ContactInfo(c: c),
                    const SizedBox(height: 11),
                    _ClinicInfo(c: c),
                  ],
                ),
              ),
              FractionallySizedBox(
                widthFactor: context.isPhone ? 1 : .49,
                child: Column(
                  children: [
                    _PersonalInfo(c: c),
                    const SizedBox(height: 11),
                    _PatientCategorisation(c: c)
                  ],
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 13),
        Align(
          alignment: Alignment.centerRight,
          child: ElevatedButton(
            onPressed: editing ? c.updatedata : c.create,
            child: Text(t.buttonTxt.save),
          ),
        ),
      ],
    );
  }
}

class _PatientCategorisation extends StatelessWidget {
  const _PatientCategorisation({
    // super.key,
    required this.c,
  });

  final PatientFormController c;

  @override
  Widget build(BuildContext context) {
    return FocusTraversalGroup(
      child: Column(
        children: [
          ListTile(
            dense: true,
            title: const Text('Patient Categorisation'),
            titleTextStyle: context.textTheme.labelMedium,
          ),
          Obx(() => DropdownButtonFormField(
                value: c.reachChannel.isEmpty ? null : c.reachChannel.value,
                onChanged: c.reachChannel,
                decoration: InputDecoration(
                  labelText: t.patientForm.reachChannel,
                ),
                items: [
                  DropdownMenuItem(
                    value: 'wordOfMouth',
                    child: Text(t.patientForm.reachChannels.wordOfMouth),
                  ),
                  DropdownMenuItem(
                    value: 'google',
                    child: Text(t.patientForm.reachChannels.google),
                  ),
                  DropdownMenuItem(
                    value: 'facebook',
                    child: Text(t.patientForm.reachChannels.facebook),
                  ),
                  DropdownMenuItem(
                    value: 'twitter',
                    child: Text(t.patientForm.reachChannels.twitter),
                  ),
                  DropdownMenuItem(
                    value: 'instagram',
                    child: Text(t.patientForm.reachChannels.instagram),
                  ),
                  DropdownMenuItem(
                    value: 'other',
                    child: Text(t.patientForm.reachChannels.other),
                  ),
                ],
              )),
          Obx(() => Column(
                children: [
                  if (c.reachChannel.value == 'other')
                    const SizedBox(height: 11),
                  if (c.reachChannel.value == 'other')
                    TextFormField(
                      autofocus: true,
                      controller: c.customReachChannel,
                      decoration: InputDecoration(
                        labelText: t.patientForm.reachChannel,
                      ),
                    ),
                  // const SizedBox(height: 16),
                ],
              )),
          const SizedBox(height: 11),
          Column(
            children: [
              SizedBox(
                width: double.infinity,
                child: SearchAnchor(
                  viewTrailing: [
                    IconButton(
                      icon: const Icon(Icons.close_rounded),
                      onPressed: () {
                        c.groupsSearchController.closeView('');
                      },
                    )
                  ],
                  viewSurfaceTintColor: Colors.white,
                  dividerColor: Colors.black.withValues(alpha: 0.13),
                  isFullScreen: context.isPhone,
                  searchController: c.groupsSearchController,
                  viewOnSubmitted: (value) {
                    final gs = List<PatientGroup>.from(c.groups);
                    for (var e in c.choosenGroups) {
                      gs.remove(e);
                    }
                    final groups = gs.where((p0) =>
                        p0.name.toLowerCase().contains(value.toLowerCase()));
                    if (groups.isEmpty) {
                      c.groupsSearchController.closeView('');
                      return;
                    }
                    c.choosenGroups.add(groups.first);
                    c.groupsSearchController.closeView('');
                  },
                  textInputAction: TextInputAction.search,
                  builder: (context, controller) {
                    return ElevatedButton(
                      onPressed: controller.openView,
                      child: const Text('Group In'),
                    );
                  },
                  suggestionsBuilder: (context, controller) {
                    final gs = List<PatientGroup>.from(c.groups);
                    for (var e in c.choosenGroups) {
                      gs.remove(e);
                    }
                    final groups = gs.where((p0) => p0.name
                        .toLowerCase()
                        .contains(controller.text.toLowerCase()));
                    return groups.map((e) => ListTile(
                          title: Text(e.name),
                          onTap: () async {
                            tryAPI(() async {
                              if (c.patient != null) {
                                c.patient!.patientGroups?.add(e);
                                await ApiService.post(
                                    '/patient-groups/${e.id}/members',
                                    data: {
                                      'patientId': c.patient!.id,
                                    });
                              }
                              c.choosenGroups.add(e);
                              controller.closeView('');
                            });
                          },
                        ));
                  },
                ),
              ),
              const SizedBox(height: 11),
              Obx(() => SizedBox(
                    width: double.infinity,
                    child: Wrap(
                      spacing: 7,
                      runSpacing: 7,
                      alignment: WrapAlignment.start,
                      crossAxisAlignment: WrapCrossAlignment.start,
                      children: [
                        for (final g in c.choosenGroups)
                          Chip(
                            focusNode: FocusNode(skipTraversal: true),
                            deleteIcon: const Icon(
                              Iconsax.close_circle_copy,
                              color: ThemeColors.primaryDarker,
                              size: 14,
                            ),
                            side: const BorderSide(
                              color: ThemeColors.primaryLight,
                            ),
                            label: Text(g.name),
                            onDeleted: () {
                              tryAPI(() async {
                                if (c.patient != null) {
                                  c.patient!.patientGroups?.removeWhere(
                                      (element) => element.id == g.id);
                                  await ApiService.delete(
                                      '/patient-groups/${g.id}/members/${c.patient!.id}');
                                }
                                c.choosenGroups.remove(g);
                              });
                            },
                          ),
                      ],
                    ),
                  ))
            ],
          ),
        ],
      ),
    );
  }
}

class _ClinicInfo extends StatelessWidget {
  const _ClinicInfo({
    // super.key,
    required this.c,
  });

  final PatientFormController c;

  @override
  Widget build(BuildContext context) {
    return FocusTraversalGroup(
      child: Column(
        children: [
          ListTile(
            dense: true,
            title: const Text('Clinic Info'),
            titleTextStyle: context.textTheme.labelMedium,
          ),
          TextFormField(
            controller: c.fileNumber,
            textInputAction: TextInputAction.done,
            decoration: InputDecoration(
              labelText: t.patientForm.fileNumber,
            ),
          ),
          const SizedBox(height: 11),
          TextFormField(
            controller: c.dentalHistory,
            textInputAction: TextInputAction.done,
            decoration: InputDecoration(
              labelText: t.patientForm.dentalHistory,
            ),
          ),
          const SizedBox(height: 11),
          TextFormField(
            controller: c.medicalHistory,
            textInputAction: TextInputAction.done,
            decoration: InputDecoration(
              labelText: t.patientForm.medicalHistory,
            ),
          ),
          if (c.patient != null) const SizedBox(height: 11),
          if (c.patient != null)
            TextFormField(
              controller: c.treatmentPlan,
              textInputAction: TextInputAction.done,
              decoration: InputDecoration(
                labelText: t.patientForm.treatmentPlan,
              ),
            ),
        ],
      ),
    );
  }
}

class _PersonalInfo extends StatelessWidget {
  const _PersonalInfo({
    // super.key,
    required this.c,
  });

  final PatientFormController c;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        ListTile(
          dense: true,
          title: const Text('Personal Info'),
          titleTextStyle: context.textTheme.labelMedium,
        ),
        TextFormField(
          controller: c.address,
          textInputAction: TextInputAction.done,
          decoration: InputDecoration(
            labelText: t.patientForm.address,
          ),
        ),
        const SizedBox(height: 11),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                keyboardType: TextInputType.number,
                textInputAction: TextInputAction.done,
                decoration: const InputDecoration(
                  labelText: 'Year',
                ),
                onChanged: (value) {
                  c.birth = DateTime(
                    int.tryParse(value) ?? c.birth.year,
                    c.birth.month,
                    c.birth.day,
                  );
                },
              ),
            ),
            const SizedBox(width: 7),
            Expanded(
              child: TextFormField(
                keyboardType: TextInputType.number,
                textInputAction: TextInputAction.done,
                decoration: const InputDecoration(
                  labelText: 'Month',
                ),
                onChanged: (value) {
                  c.birth = DateTime(
                    c.birth.year,
                    int.tryParse(value) ?? c.birth.month,
                    c.birth.day,
                  );
                },
              ),
            ),
            const SizedBox(width: 7),
            Expanded(
              child: TextFormField(
                keyboardType: TextInputType.number,
                textInputAction: TextInputAction.done,
                decoration: const InputDecoration(
                  labelText: 'Day',
                ),
                onChanged: (value) {
                  c.birth = DateTime(
                    c.birth.year,
                    c.birth.month,
                    int.tryParse(value) ?? c.birth.day,
                  );
                },
              ),
            ),
          ],
        ),
        // TextFormField(
        //   readOnly: true,
        //   controller: c.currentBirth,
        //   textInputAction: TextInputAction.done,
        //   decoration: InputDecoration(
        //     labelText: t.patientForm.birthdate,
        //   ),
        //   onTap: () async {
        //     final result = await showDatePicker(
        //       context: context,
        //       initialDate: c.birth ?? DateTime.now(),
        //       firstDate: DateTime(1900),
        //       lastDate: DateTime.now(),
        //     );
        //     if (result != null) {
        //       c.setBirth(result);
        //     }
        //   },
        // ),
        const SizedBox(height: 11),
        TextFormField(
          controller: c.job,
          textInputAction: TextInputAction.done,
          decoration: InputDecoration(
            labelText: t.patientForm.job,
          ),
        ),
      ],
    );
  }
}

class _ContactInfo extends StatelessWidget {
  const _ContactInfo({
    // super.key,
    required this.c,
  });

  final PatientFormController c;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        ListTile(
          dense: true,
          title: const Text('Contact Info'),
          titleTextStyle: context.textTheme.labelMedium,
        ),
        TextFormField(
          autofocus: true,
          controller: c.name,
          textInputAction: TextInputAction.done,
          decoration: InputDecoration(
            labelText: t.patientForm.name,
          ),
        ),
        const SizedBox(height: 11),
        TextFormField(
          controller: c.phone,
          textInputAction: TextInputAction.done,
          decoration: InputDecoration(
            labelText: t.patientForm.phone,
          ),
        ),
        const SizedBox(height: 11),
        TextFormField(
          controller: c.email,
          textInputAction: TextInputAction.done,
          decoration: InputDecoration(
            labelText: t.patientForm.email,
          ),
        ),
      ],
    );
  }
}
