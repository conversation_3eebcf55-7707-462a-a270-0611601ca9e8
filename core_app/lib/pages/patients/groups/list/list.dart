import 'package:core_app/components/tables/table.dart';
import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/core/models/patient.dart';
import 'package:core_app/core/utils/i18n/translations.g.dart';
import 'package:core_app/layout/page_layout.dart';
import 'package:core_app/pages/patients/groups/list/controller.dart';
import 'package:core_app/routes/navigation.dart';

import 'package:core_app/routes/route_paths.dart';
import 'package:core_app/services/auth.dart';
import 'package:core_app/services/config.dart';
import 'package:core_app/services/notify/messenger.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

class GroupPateintsPage extends StatelessWidget {
  const GroupPateintsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final c = Get.find<GroupPateintsController>();
    return PageLayout(
      title: t.overduePatients.title,
      children: [
        Container(
          padding: const EdgeInsets.all(7),
          decoration: BoxDecoration(
            color: ThemeColors.bg,
            borderRadius: BorderRadius.circular(13),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  BackButton(
                    color: ThemeColors.text,
                    onPressed: () {
                      Navigation.back();
                    },
                  ),
                  const Text(
                    'Group Members',
                    style: TextStyle(
                      color: ThemeColors.text,
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        const SizedBox(height: 10),
        Obx(() => TableComponent<PatientModel>(
              title: t.patientsList.title,
              data: c.patients.value,
              onRowTap: (data) {
                Navigation.to(Routes.PatientProfile, {
                  'patientID': data.id,
                });
              },
              columns: [
                TableColumn(
                  flex: 3,
                  title: 'Name',
                  minWidth: 300,
                  builder: (data) {
                    return TableColumn.stringBuilder(data.name);
                  },
                ),
                TableColumn(
                  flex: 2,
                  minWidth: 150,
                  title: 'Phone Number',
                  builder: (data) {
                    return TableColumn.stringBuilder(data.phoneNumber);
                  },
                ),
                TableColumn(
                  flex: 2,
                  title: 'Balance',
                  minWidth: 100,
                  builder: (data) {
                    return TableColumn.stringBuilder(
                        '${data.balance} ${ConfigService.to.currency}');
                  },
                ),
                TableColumn(
                  title: '',
                  minWidth: 100,
                  builder: (data) {
                    return Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        IconButton(
                          iconSize: 18,
                          icon: const Icon(
                            Iconsax.edit_copy,
                            size: 18,
                          ),
                          onPressed: () async {
                            if (AuthService.to.isSubEnded) {
                              NotifyService.notice(title: t.subEnded);
                              return;
                            }
                            // PatientModel? repo = await Get.dialog(PatientForm(data));
                            // if (repo != null) {
                            //   c.patients.remove(data);
                            //   c.patients.add(repo);
                            // }
                          },
                        ),
                        IconButton(
                          iconSize: 18,
                          icon: const Icon(
                            Iconsax.trash_copy,
                            size: 18,
                          ),
                          onPressed: () {
                            if (AuthService.to.isSubEnded) {
                              NotifyService.notice(title: t.subEnded);
                              return;
                            }
                            // tryAPI(() async {
                            //   await BackendSDK.patients.delete(data.getId);
                            //   c.patients.remove(data);
                            // });
                          },
                        ),
                      ],
                    );
                  },
                ),
              ],
            )),
      ],
    );
  }
}
