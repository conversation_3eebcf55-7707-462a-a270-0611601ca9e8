import 'package:core_app/core/models/patient.dart';
import 'package:core_app/services/api.dart';
import 'package:get/get.dart';

class GroupPateintsController extends GetxController {
  final patients = <PatientModel>[].obs;

  final String groupId;
  GroupPateintsController(this.groupId);

  void fetch() async {
    final res = await ApiService.get('/patient-groups/$groupId/members');
    patients.assignAll(
      List<PatientModel>.from(
        res.data!['patientGroupMembers'].map((e) => PatientModel.fromJson(e)),
      ),
    );
  }

  @override
  void onInit() {
    fetch();
    super.onInit();
  }
}
