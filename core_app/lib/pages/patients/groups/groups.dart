import 'package:core_app/components/buttons/text_icon.dart';
import 'package:core_app/components/tables/table.dart';
import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/core/models/patient_group.dart';
import 'package:core_app/core/utils/i18n/translations.g.dart';
import 'package:core_app/layout/page_layout.dart';
import 'package:core_app/pages/patients/groups/controller.dart';
import 'package:core_app/pages/patients/groups/dialogs/form.dart';
import 'package:core_app/routes/navigation.dart';

import 'package:core_app/routes/route_paths.dart';
import 'package:core_app/services/auth.dart';
import 'package:core_app/services/notify/messenger.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

class PatientGroupsPage extends StatelessWidget {
  const PatientGroupsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final c = Get.find<PatientGroupsController>();
    return PageLayout(
      title: t.patientGroups.title,
      children: [
        Container(
          padding: const EdgeInsets.all(7),
          decoration: BoxDecoration(
            color: ThemeColors.bg,
            borderRadius: BorderRadius.circular(13),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  BackButton(
                    color: ThemeColors.text,
                    onPressed: () {
                      Navigation.back();
                    },
                  ),
                  Text(
                    t.patientGroups.title,
                    style: const TextStyle(
                      color: ThemeColors.text,
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        const SizedBox(height: 10),
        Obx(() => TableComponent<PatientGroup>(
              title: t.patientGroups.title,
              data: c.groups.value,
              onRowTap: (data) {
                Navigation.to(Routes.PatientGroupMembers, {
                  'id': data.id,
                });
              },
              actions: [
                XTextIconButton(
                  icon: Icons.add,
                  title: 'New',
                  onPressed: () async {
                    await Get.dialog(const PatientGroupForm(null));
                    c.fetch();
                  },
                ),
              ],
              columns: [
                TableColumn(
                  flex: 3,
                  title: 'Name',
                  minWidth: 300,
                  builder: (data) {
                    return TableColumn.stringBuilder(data.name);
                  },
                ),
                TableColumn(
                  title: '',
                  minWidth: 100,
                  builder: (data) {
                    return Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        IconButton(
                          iconSize: 18,
                          icon: const Icon(
                            Iconsax.edit_copy,
                            size: 18,
                          ),
                          onPressed: () async {
                            if (AuthService.to.isSubEnded) {
                              NotifyService.notice(title: t.subEnded);
                              return;
                            }
                            await Get.dialog(PatientGroupForm(data));
                            c.fetch();
                          },
                        ),
                        IconButton(
                          iconSize: 18,
                          icon: const Icon(
                            Iconsax.trash_copy,
                            size: 18,
                          ),
                          onPressed: () {
                            if (AuthService.to.isSubEnded) {
                              NotifyService.notice(title: t.subEnded);
                              return;
                            }
                          },
                        ),
                      ],
                    );
                  },
                ),
              ],
            )),
      ],
    );
  }
}
