import 'package:core_app/core/helpers/api_handler.dart';
import 'package:core_app/core/models/patient_group.dart';
import 'package:core_app/services/api.dart';
import 'package:get/get.dart';

class PatientGroupsController extends GetxController {
  final groups = <PatientGroup>[].obs;

  void fetch() async {
    tryAPI(() async {
      final res = await ApiService.get('/patient-groups/');
      groups.value = (res.data!['patientGroups'] as List)
          .map((e) => PatientGroup.fromJson(e))
          .toList();
    });
  }

  @override
  void onInit() {
    fetch();
    super.onInit();
  }
}
