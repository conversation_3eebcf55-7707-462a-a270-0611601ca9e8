import 'package:core_app/components/dialogs/basic.dart';
import 'package:core_app/core/helpers/api_handler.dart';
import 'package:core_app/core/models/patient_group.dart';
import 'package:core_app/core/utils/i18n/translations.g.dart';

import 'package:core_app/services/api.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class PatientGroupFormController {
  final PatientGroup? group;
  PatientGroupFormController(this.group) {
    if (group != null) {
      name.text = group!.name;
    }
  }
  final TextEditingController name = TextEditingController();

  void updatedata() {
    tryAPI(() async {
      await ApiService.patch('/patient-groups', data: {
        'name': name.text,
      });
      Get.back();
    });
  }

  void create() async {
    tryAPI(() async {
      await ApiService.post('/patient-groups', data: {
        'name': name.text,
      });
      Get.back();
    });
  }
}

class PatientGroupForm extends StatelessWidget {
  const PatientGroupForm(this.group, {super.key});

  final PatientGroup? group;

  @override
  Widget build(BuildContext context) {
    final bool editing = group != null;
    final c = Get.put(PatientGroupFormController(group));
    return BasicDialog(
      title: t.patientGroups.name,
      children: [
        const SizedBox(height: 13),
        TextFormField(
          autofocus: true,
          controller: c.name,
          decoration: InputDecoration(
            labelText: t.patientForm.name,
          ),
        ),
        const SizedBox(height: 13),
        Align(
          alignment: Alignment.centerRight,
          child: ElevatedButton(
            onPressed: editing ? c.updatedata : c.create,
            child: Text(t.buttonTxt.save),
          ),
        ),
      ],
    );
  }
}
