import 'package:core_app/core/api/appointments.dart';
import 'package:core_app/core/api/patients.dart';
import 'package:core_app/core/api/payments.dart';
import 'package:core_app/core/models/appointment.dart';
import 'package:core_app/core/models/insurance.dart';
import 'package:core_app/core/models/patient.dart';
import 'package:core_app/core/models/payment.dart';
import 'package:core_app/core/models/procedure.dart';
import 'package:core_app/core/models/visit.dart';
import 'package:core_app/routes/navigation.dart';

import 'package:get/get.dart';

class PatientProfileController extends GetxController {
  final String patientId;
  PatientProfileController(this.patientId);

  static PatientProfileController get to =>
      Get.find(tag: Navigation.queryAsKey('patientID'));

  // UI
  final loading = true.obs;
  final childTeethView = false.obs;
  // Patient Main Data
  final visits = <VisitModel>[].obs;
  late final Rx<PatientModel> patient;
  // Extra Data
  final payments = <PaymentModel>[].obs;
  final files = <PatientFileModel>[].obs;
  final appointments = <AppointmentModel>[].obs;
  final insuranceClaims = <InsuranceClaimModel>[].obs;
  // Manipulated Data
  final teethProcedures = <String, List<VisitProcedureModel>>{};

  void fetchOnlyPatient() async {
    final p = await PatientsAPI.get(patientId);
    patient.value = p;
  }

  void loadPatient() async {
    var p = await PatientsAPI.visits(patientId);
    insuranceClaims.value = await PatientsAPI.listClaims(patientId);
    if (loading.value) {
      patient = p.obs;
    } else {
      patient.value = p;
    }
    visits.value = p.visits ?? [];
    visits.sort((a, b) => b.createdAt.compareTo(a.createdAt));

    // Group Procedures by Tooth Number
    for (var v in visits) {
      for (var p in v.procedures) {
        final list = teethProcedures[p.toothNumber];
        if (list != null) {
          list.add(p);
        } else {
          teethProcedures[p.toothNumber] = [p];
        }
      }
    }

    // Get all Appointments from now to 3 months
    final from = DateTime.now();
    final to = from.add(const Duration(days: 91));
    // Sort by Start Time
    appointments.value =
        await AppointmentsAPI.list(from: from, to: to, patientId: patientId);
    appointments.sort((a, b) => b.startTime.compareTo(a.startTime));

    // Remove Completed Appointments
    final completedAppointments = <String>[];
    for (var v in visits) {
      completedAppointments.add(v.appointmentId);
    }
    appointments.value = appointments
        .where((e) => !completedAppointments.contains(e.id))
        .toList();

    // Get extra data
    files.value = await PatientsAPI.files(patientId);
    payments.value = await PaymentsAPI.list(
      page: 1,
      patientId: patientId,
    );
    loading.value = false;
  }

  @override
  void onInit() {
    loadPatient();
    super.onInit();
  }
}
