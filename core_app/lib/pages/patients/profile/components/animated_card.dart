import 'package:core_app/core/constants/colors.dart';
import 'package:flutter/material.dart';

class AnimatedCardComponent extends StatelessWidget {
  const AnimatedCardComponent({
    super.key,
    required this.child,
    this.margin,
  });

  final Widget child;
  final EdgeInsets? margin;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 23),
      decoration: BoxDecoration(
        color: ThemeColors.bg,
        borderRadius: BorderRadius.circular(13),
        border: Border.all(width: .5, color: const Color.fromARGB(92, 176, 185, 191)),
      ),
      child: child,
    );
  }
}
