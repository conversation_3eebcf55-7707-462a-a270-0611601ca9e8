import 'package:core_app/components/buttons/popup_menu.dart';
import 'package:core_app/components/buttons/text.dart';
import 'package:core_app/components/dialogs/basic.dart';
import 'package:core_app/core/api/patients.dart';
import 'package:core_app/core/api/payments.dart';
import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/core/helpers/api_handler.dart';
import 'package:core_app/core/models/procedure.dart';
import 'package:core_app/core/models/visit.dart';
import 'package:core_app/core/utils/i18n/translations.g.dart';
import 'package:core_app/pages/patients/profile/controller.dart';
import 'package:core_app/pages/patients/profile/dialogs/payments_list.dart';
import 'package:core_app/pages/patients/profile/invoide_widget.dart';
import 'package:core_app/pages/payments/create/create.dart';
import 'package:core_app/pages/visits/create/dialogs/create_procedure/create_procedure.dart';
import 'package:core_app/pages/visits/edit.dart';

import 'package:core_app/services/auth.dart';
import 'package:core_app/services/config.dart';
import 'package:core_app/services/notify/messenger.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:jiffy/jiffy.dart';
import 'package:path/path.dart' as path;
import 'package:core_app/pages/patients/profile/dialogs/file_viewer.dart'
    deferred as file_viewer;
import 'package:printing/printing.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;

const _cardDivider =
    Divider(thickness: .5, color: Color.fromARGB(92, 176, 185, 191));

class ProfileVisitItem extends StatefulWidget {
  const ProfileVisitItem({
    super.key,
    required this.repo,
  });

  final VisitModel repo;

  @override
  State<ProfileVisitItem> createState() => _ProfileVisitItemState();
}

class _ProfileVisitItemState extends State<ProfileVisitItem> {
  void addPayment() async {
    if (AuthService.to.isSubEnded) {
      NotifyService.notice(title: 'Your subscription ended');
      return;
    }
    Get.dialog(CreatePaymentDialog(
      patientId: widget.repo.patientId,
      branchId: widget.repo.branchId,
      visitId: widget.repo.id,
      treatments: widget.repo.treatments ?? [],
      hint: widget.repo.procedures.isEmpty
          ? null
          : 'Total Procedures price: '
              '${widget.repo.procedures.fold(
              0.0,
              (previousValue, e) => previousValue + e.price,
            )}',
    ));
  }

  void addProcedure() async {
    if (AuthService.to.isSubEnded) {
      NotifyService.notice(title: 'Your subscription ended');
      return;
    }
    final List<VisitProcedureModel>? ps = await Get.dialog(ProcedureFormDialog(
      andCreate: true,
      visitId: widget.repo.id,
    ));
    if (ps == null) return;
    widget.repo.procedures.addAll(ps);

    final to = PatientProfileController.to;
    for (var p in ps) {
      final list = to.teethProcedures[p.toothNumber];
      if (list != null) {
        list.add(p);
      } else {
        to.teethProcedures[p.toothNumber] = [p];
      }
    }
    to.fetchOnlyPatient();
    setState(() {});
  }

  Future<void> refund() async {
    if (AuthService.to.isSubEnded) {
      NotifyService.notice(title: 'Your subscription ended');
      return;
    }
    Map? f = await Get.dialog(const _Payment(0, 'refund'));
    if (f == null) return;

    tryAPI(() async {
      await PaymentsAPI.create(
        visitId: widget.repo.id,
        patientId: widget.repo.patientId,
        branchId: widget.repo.branchId,
        amount: f['amount']! * -1,
        type: f['type']!,
        customPaymentMethodId: '',
      );

      PatientProfileController.to.patient.update((val) {
        val!.balance += f['amount']!;
      });
    });
  }

  Future<void> uploadFile() async {
    // TODO: renaming dialog
    if (AuthService.to.isSubEnded) {
      NotifyService.notice(title: 'Your subscription ended');
      return;
    }
    final FilePickerResult? result = await FilePicker.platform.pickFiles(
      allowMultiple: true,
      allowedExtensions: ['jpg', 'png', 'pdf'],
      type: FileType.custom,
    );

    if (result == null || result.files.isEmpty) return;
    tryAPI(() async {
      var list = await PatientsAPI.uploadFile(
        widget.repo.patientId,
        widget.repo.id,
        result.files.fold<Map<String, Object>>({}, (previousValue, e) {
          previousValue
              .addAll({path.basename(e.name): kIsWeb ? e.bytes! : e.path!});
          return previousValue;
        }),
      );
      PatientProfileController.to.files.addAll(list);
    });
  }

  @override
  Widget build(BuildContext context) {
    final c = PatientProfileController.to;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(right: 18, left: 18, bottom: 7),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Row(
                  children: [
                    const Icon(
                      Iconsax.calendar_copy,
                      size: 26,
                      color: ThemeColors.notion,
                    ),
                    const SizedBox(width: 13),
                    Expanded(
                      child: Text(
                        Jiffy.parseFromDateTime(widget.repo.createdAt)
                            .yMMMMEEEEdjm,
                        overflow: TextOverflow.clip,
                        style: const TextStyle(
                          fontSize: 16,
                          color: ThemeColors.text,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              IconButton(
                icon: const Icon(Icons.edit_outlined),
                onPressed: () {
                  Get.dialog(EditVisitDialog(widget.repo));
                  setState(() {});
                },
              ),
              AutoPopupMenu(
                items: [
                  AutoPopupMenuItem(
                    name: 'Add Procedure',
                    onTap: addProcedure,
                  ),
                  AutoPopupMenuItem(
                    name: 'Upload File',
                    onTap: uploadFile,
                  ),
                  AutoPopupMenuItem(
                    name: "Add Payment",
                    onTap: addPayment,
                  ),
                  // AutoPopupMenuItem(
                  //   name: 'Refund',
                  //   onTap: refund,
                  // ),

                  AutoPopupMenuItem(
                    name: "Create Invoice",
                    onTap: () async {
                      final doc = pw.Document();

                      var logo2 =
                          await imageFromAssetBundle('assets/naab_logo.png');
                      doc.addPage(pw.Page(
                          pageFormat: PdfPageFormat.a4,
                          margin: const pw.EdgeInsets.all(20),
                          build: (pw.Context context) {
                            return buildInvoid(
                              logo: logo2,
                              patientName: c.patient.value.name,
                              patientPhoneNumber: c.patient.value.phoneNumber,
                              patientFileNumber: c.patient.value.fileNumber,
                              procedures: [
                                for (final p in widget.repo.procedures)
                                  InvoiceProcedure(
                                    name: p.procedure,
                                    price: p.price,
                                    speciality: p.speciality,
                                  ),
                              ],
                            );
                          })); // Page
                      await Printing.layoutPdf(
                          onLayout: (PdfPageFormat format) async => doc.save());
                    },
                  ),
                  AutoPopupMenuItem(
                    name: 'Payments List',
                    onTap: () {
                      final list = PatientProfileController.to.payments
                          .where((p0) => p0.visitId == widget.repo.id)
                          .toList();
                      Get.dialog(PaymentListDialog(list));
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
        _cardDivider,
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 26),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text.rich(TextSpan(children: [
                TextSpan(children: [
                  TextSpan(
                    text: "${t.profileVisitItem.diagnosis}:  ",
                    style: const TextStyle(
                      fontSize: 14,
                      fontStyle: FontStyle.italic,
                      fontWeight: FontWeight.w600,
                      color: Color.fromARGB(205, 18, 29, 38),
                    ),
                  ),
                  TextSpan(
                    text: widget.repo.diagnosis.isEmpty
                        ? t.profileVisitItem.noDiagnosis
                        : widget.repo.diagnosis,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Color.fromARGB(205, 18, 29, 38),
                    ),
                  ),
                ]),
                TextSpan(children: [
                  TextSpan(
                    text: "\n\n${t.profileVisitItem.comment}:  ",
                    style: const TextStyle(
                      fontSize: 14,
                      fontStyle: FontStyle.italic,
                      fontWeight: FontWeight.w600,
                      color: Color.fromARGB(205, 18, 29, 38),
                    ),
                  ),
                  TextSpan(
                    text: widget.repo.comments?.isEmpty ?? false
                        ? t.profileVisitItem.noComment
                        : widget.repo.comments,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Color.fromARGB(205, 18, 29, 38),
                    ),
                  ),
                ]),
                TextSpan(
                  style: const TextStyle(height: 2),
                  children: [
                    TextSpan(
                      text:
                          "\n\n${t.profileVisitItem.treatments}:  ${(widget.repo.treatments ?? []).isEmpty ? t.profileVisitItem.noTreatments : ''}",
                      style: const TextStyle(
                        fontSize: 14,
                        fontStyle: FontStyle.italic,
                        fontWeight: FontWeight.w600,
                        color: Color.fromARGB(205, 18, 29, 38),
                      ),
                    ),
                    for (var i = 0;
                        i < (widget.repo.treatments ?? []).length;
                        i++)
                      TextSpan(
                        text:
                            '\n\t\t\t\t\t\t${i + 1}. ${widget.repo.treatments![i]}',
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Color.fromARGB(205, 18, 29, 38),
                        ),
                      ),
                  ],
                ),
              ])),
              const SizedBox(height: 20),
              Text(
                "${t.profileVisitItem.procedures}:  ${widget.repo.procedures.isEmpty ? t.profileVisitItem.noProcedures : ''}",
                style: const TextStyle(
                  fontSize: 14,
                  fontStyle: FontStyle.italic,
                  fontWeight: FontWeight.w600,
                  color: Color.fromARGB(205, 18, 29, 38),
                ),
              ),
              if (widget.repo.procedures.isNotEmpty)
                ListView.builder(
                  shrinkWrap: true,
                  itemCount: widget.repo.procedures.length,
                  itemBuilder: (context, index) {
                    var procedure = widget.repo.procedures[index];
                    final price = procedure.price - procedure.discount;
                    const style = TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Color.fromARGB(205, 18, 29, 38),
                    );
                    return ListTile(
                      dense: true,
                      title: Text(procedure.procedure, style: style),
                      trailing: Text('$price ${ConfigService.to.currency}',
                          style: style),
                      subtitle: Text.rich(TextSpan(
                        children: [
                          TextSpan(
                            text: 'Dr. ${procedure.dentist!.name} | ',
                            style: const TextStyle(
                              fontSize: 11,
                              fontWeight: FontWeight.w500,
                              color: Color.fromARGB(205, 18, 29, 38),
                            ),
                          ),
                          TextSpan(
                            text: procedure.speciality,
                            style: const TextStyle(
                              fontSize: 11,
                              fontWeight: FontWeight.w500,
                              color: Color.fromARGB(205, 18, 29, 38),
                            ),
                          ),
                          TextSpan(
                            text: procedure.discount > 0
                                ? ' - ${t.profileVisitItem.discount}: ${procedure.discount} ${ConfigService.to.currency}'
                                : '',
                            style: const TextStyle(
                              fontSize: 11,
                              fontWeight: FontWeight.w500,
                              color: Color.fromARGB(205, 18, 29, 38),
                            ),
                          ),
                          TextSpan(
                            text:
                                ' - ${t.profileVisitItem.toothNumber}: ${procedure.toothNumber.toUpperCase()}',
                            style: const TextStyle(
                              fontSize: 11,
                              fontWeight: FontWeight.w500,
                              color: Color.fromARGB(205, 18, 29, 38),
                            ),
                          ),
                        ],
                      )),
                    );
                  },
                ),
              const SizedBox(height: 13),
              Obx(() {
                var files = PatientProfileController.to.files
                    .where((p0) => p0.visitId == widget.repo.id);
                return Wrap(
                  spacing: 7,
                  runSpacing: 7,
                  children: [
                    for (var file in files)
                      RawChip(
                        label: Text(file.title),
                        onPressed: () async {
                          await file_viewer.loadLibrary();
                          Get.dialog(file_viewer.FileViwer(file));
                        },
                      ),
                  ],
                );
              }),
            ],
          ),
        ),
      ],
    );
  }
}

class _Payment extends StatefulWidget {
  const _Payment(this.price, this.type);

  final double price;
  final String type; // `payment` or `refund`

  @override
  State<_Payment> createState() => __PaymentState();
}

class __PaymentState extends State<_Payment> {
  final amount = ''.obs;
  final type = 'cash'.obs;

  @override
  void initState() {
    amount.value = widget.price.toString();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BasicDialog(
      title: widget.type.capitalizeFirst!,
      children: [
        TextFormField(
          initialValue: amount.value,
          onChanged: amount,
          decoration: const InputDecoration(
            labelText: 'Amount',
            border: OutlineInputBorder(),
          ),
        ),
        const SizedBox(height: 11),
        Obx(() => CheckboxListTile(
              title: Text(t.payment.types.cash),
              value: type.value == "cash",
              controlAffinity: ListTileControlAffinity.leading,
              onChanged: (newValue) {
                type.value = 'cash';
              },
            )),
        Obx(() => CheckboxListTile(
              title: Text(t.payment.types.valu),
              value: type.value == "valu",
              controlAffinity: ListTileControlAffinity.leading,
              onChanged: (newValue) {
                type.value = 'valu';
              },
            )),
        Obx(() => CheckboxListTile(
              title: Text(t.payment.types.card),
              value: type.value == "card",
              controlAffinity: ListTileControlAffinity.leading,
              onChanged: (newValue) {
                type.value = 'card';
              },
            )),
        const SizedBox(height: 11),
        Align(
          alignment: Alignment.centerRight,
          child: XTextButton(
            title: t.finish,
            onPressed: () => Get.back(result: {
              "amount": double.parse(amount.value),
              "type": type.value,
            }),
          ),
        ),
      ],
    );
  }
}
