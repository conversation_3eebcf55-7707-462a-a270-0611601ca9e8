import 'package:core_app/components/buttons/popup_menu.dart';
import 'package:core_app/components/teeth_view.dart';
import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/core/models/appointment.dart';
import 'package:core_app/core/models/patient.dart';
import 'package:core_app/core/utils/i18n/translations.g.dart';
import 'package:core_app/core/utils/responsive.dart';
import 'package:core_app/layout/page_layout.dart';
import 'package:core_app/pages/appointments/create/create.dart';
import 'package:core_app/pages/insurance/claim/edit.dart';
import 'package:core_app/pages/patients/form/form.dart';
import 'package:core_app/pages/visits/create/form.dart';
import 'package:core_app/routes/navigation.dart';

import 'package:core_app/services/auth.dart';
import 'package:core_app/services/config.dart';
import 'package:core_app/services/notify/messenger.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:jiffy/jiffy.dart';

import 'components/animated_card.dart';
import 'components/patient_info.dart';
import 'components/visit_item.dart';
import 'controller.dart';
import 'dialogs/payments_list.dart';
import 'dialogs/tooth_procedures.dart';

const _cardDivider =
    Divider(thickness: .5, color: Color.fromARGB(92, 176, 185, 191));

class PateintProfilePage extends StatelessWidget {
  const PateintProfilePage(this.patientId, {super.key});

  final String patientId;

  @override
  Widget build(BuildContext context) {
    final isDesktopView = context.desktopView;
    final c = Get.find<PatientProfileController>(
        tag: Navigation.queryAsKey('patientID'));
    final sideOne = Column(
      // left side
      children: [
        const _PateintInfoMini(),
        const SizedBox(height: 26),
        AnimatedCardComponent(
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 18),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      t.patientProfile.appointments,
                      style: const TextStyle(
                        fontSize: 15,
                        color: ThemeColors.text,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      '${c.appointments.length} ${t.patientProfile.appointments}',
                      style: const TextStyle(
                        fontSize: 14,
                        color: ThemeColors.notion,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 6),
              _cardDivider,
              // const SizedBox(height: 11),
              Obx(() => ListView.separated(
                    shrinkWrap: true,
                    itemCount: c.appointments.length,
                    physics: const NeverScrollableScrollPhysics(),
                    separatorBuilder: (context, index) => _cardDivider,
                    itemBuilder: (context, index) {
                      final repo = c.appointments[index];
                      return Material(
                        color: Colors.transparent,
                        child: ListTile(
                          onTap: () async {
                            if (AuthService.to.isSubEnded) {
                              NotifyService.notice(title: t.subEnded);
                              return;
                            }
                            await Get.dialog(CreateVisitDialog(repo, false));
                            c.loadPatient();
                          },
                          title: Text(
                            "${t.appointment.dentist}: ${repo.dentist!.name.capitalize}",
                            style: const TextStyle(
                              fontSize: 15,
                              color: ThemeColors.text,
                            ),
                          ),
                          subtitle: SizedBox(
                            width: double.infinity,
                            child: Text.rich(
                              TextSpan(
                                children: [
                                  TextSpan(
                                    text:
                                        "${t.appointment.branch}: ${repo.branch!.name}",
                                  ),
                                  const TextSpan(text: " | "),
                                  TextSpan(
                                    text: t.appointment.from(
                                        time: Jiffy.parseFromDateTime(
                                                repo.startTime)
                                            .yMMMEdjm),
                                  ),
                                  const TextSpan(text: " | "),
                                  TextSpan(
                                    text: t.appointment.to(
                                        time: Jiffy.parseFromDateTime(
                                                repo.endTime)
                                            .jm),
                                  ),
                                ],
                              ),
                              style: const TextStyle(
                                fontSize: 13.5,
                                color: ThemeColors.text,
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  )),
            ],
          ),
        ),
        const SizedBox(height: 21),
        AnimatedCardComponent(
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 18),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      t.patientProfile.insuranceClaims,
                      style: const TextStyle(
                        fontSize: 15,
                        color: ThemeColors.text,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const Text(
                      'Last 10',
                      style: TextStyle(
                        fontSize: 14,
                        color: ThemeColors.notion,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 6),
              _cardDivider,
              Obx(() => ListView.separated(
                    shrinkWrap: true,
                    itemCount: c.insuranceClaims.take(10).length,
                    physics: const NeverScrollableScrollPhysics(),
                    separatorBuilder: (context, index) => _cardDivider,
                    itemBuilder: (context, index) {
                      final repo = c.insuranceClaims[index];
                      return Material(
                        color: Colors.transparent,
                        child: ListTile(
                          dense: true,
                          onTap: () async {
                            Get.dialog(ClaimEditDialog(repo));
                          },
                          title: Text(
                            "${t.insuranceClaim.date}: ${Jiffy.parseFromDateTime(repo.createdAt).yMMMEdjm}",
                            style: const TextStyle(
                              fontSize: 15,
                              color: ThemeColors.text,
                            ),
                          ),
                          subtitle: SizedBox(
                            width: double.infinity,
                            child: Text.rich(
                              TextSpan(
                                children: [
                                  TextSpan(
                                    text: "${t.insuranceClaim.company}: "
                                        "${repo.insuranceCompany?.name}",
                                  ),
                                ],
                              ),
                              style: const TextStyle(
                                fontSize: 13.5,
                                color: ThemeColors.text,
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  )),
            ],
          ),
        ),
      ],
    );
    final sideTwo = Obx(() => ListView(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          children: [
            const PatientInfo(),
            const SizedBox(height: 26),
            AnimatedCardComponent(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 26),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      t.patientProfile.visitsHistory,
                      style: const TextStyle(
                        fontSize: 16,
                        color: ThemeColors.text,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      '${c.visits.length} ${t.patientProfile.visits}',
                      style: const TextStyle(
                        fontSize: 14,
                        color: ThemeColors.notion,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 11),
            for (final repo in c.visits)
              AnimatedCardComponent(
                margin: const EdgeInsets.symmetric(vertical: 5),
                child: ProfileVisitItem(repo: repo),
              ),
          ],
        ));
    return PageLayout(
      title: t.patientProfile.patientProfile,
      children: [
        const _TopMenu(),
        const SizedBox(height: 16),
        Obx(() {
          if (c.loading.value) {
            return const Center(child: CircularProgressIndicator());
          }
          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 21),
            decoration: BoxDecoration(
              color: ThemeColors.bg,
              borderRadius: BorderRadius.circular(13),
            ),
            child: Column(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(18),
                  child: Obx(() => TeethViewComponent(
                        childTeeth: c.childTeethView.value,
                        teethStatus: c.patient.value.teethStatus,
                        onTap: (position, status) {
                          Get.dialog(ViewToothProcedures(
                            key: ValueKey(position),
                            status: status,
                            toothPosition: position,
                            procedures: c.teethProcedures[position] ?? [],
                          ));
                        },
                      )),
                ),
                const SizedBox(height: 21),
                Flex(
                  direction: isDesktopView ? Axis.horizontal : Axis.vertical,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (isDesktopView)
                      Flexible(
                        flex: 2,
                        child: sideOne,
                      )
                    else
                      sideOne,
                    SizedBox(
                        width: isDesktopView ? 16 : 0,
                        height: isDesktopView ? 0 : 16),
                    if (isDesktopView)
                      Flexible(
                        flex: 3,
                        child: sideTwo,
                      )
                    else
                      sideTwo,
                  ],
                ),
              ],
            ),
          );
        })
      ],
    );
  }
}

class _TopMenu extends StatelessWidget {
  const _TopMenu();

  @override
  Widget build(BuildContext context) {
    final c = Get.find<PatientProfileController>(
        tag: Navigation.queryAsKey('patientID'));
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 13, vertical: 11),
      decoration: BoxDecoration(
        color: ThemeColors.bg,
        borderRadius: BorderRadius.circular(13),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              BackButton(
                color: ThemeColors.text,
                onPressed: () {
                  // final box = context.findRenderObject() as RenderBox;
                  // if (box.size.width >= (Get.width * .81)) {
                  //   Get.back();
                  // } else {
                  //   LayoutController.back();
                  // }
                  Navigation.back();
                },
              ),
              Text(
                t.patientProfile.patientProfile,
                style: const TextStyle(
                  color: ThemeColors.text,
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
              ),
            ],
          ),
          AutoPopupMenu(
            items: [
              AutoPopupMenuItem(
                name: t.patientProfile.edit,
                onTap: () async {
                  if (AuthService.to.isSubEnded) {
                    NotifyService.notice(title: t.subEnded);
                    return;
                  }
                  PatientModel? repo =
                      await Get.dialog(PatientForm(c.patient.value));
                  if (repo != null) {
                    c.patient.value = repo;
                  }
                },
              ),
              AutoPopupMenuItem(
                name: t.appointment.addNewAppointment,
                onTap: () async {
                  if (AuthService.to.isSubEnded) {
                    NotifyService.notice(title: t.subEnded);
                    return;
                  }
                  AppointmentModel? a = await Get.dialog(
                      CreateAppointmentDialog(patientId: c.patientId));
                  if (a != null) {
                    c.appointments.insert(0, a);
                  }
                },
              ),
              AutoPopupMenuItem(
                name: t.patientProfile.switchTeethView,
                onTap: () async {
                  c.childTeethView.value = !c.childTeethView.value;
                },
              ),
              AutoPopupMenuItem(
                name: t.patientProfile.paymentsList,
                onTap: () => Get.dialog(
                    PaymentListDialog(PatientProfileController.to.payments)),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class _PateintInfoMini extends StatelessWidget {
  const _PateintInfoMini();

  @override
  Widget build(BuildContext context) {
    final data = PatientProfileController.to.patient.value;
    return AnimatedCardComponent(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 18),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    const Icon(
                      Iconsax.user_octagon_copy,
                      size: 36,
                      color: ThemeColors.notion,
                    ),
                    const SizedBox(width: 8),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          data.name,
                          style: const TextStyle(
                            fontSize: 16,
                            color: ThemeColors.text,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          data.job.isEmpty ? 'N/A' : data.job,
                          style: const TextStyle(
                            fontSize: 14,
                            color: ThemeColors.notion,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 14),
          _cardDivider,
          const SizedBox(height: 6),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 0),
            child: Column(
              children: [
                dataTile(
                  icon: Iconsax.folder_copy,
                  title: t.patientForm.fileNumber,
                  data: data.fileNumber.isEmpty ? 'N/A' : data.fileNumber,
                ),
                dataTile(
                  icon: Iconsax.info_circle_copy,
                  title: t.patientForm.age,
                  data: data.birthdate == null
                      ? 'N/A'
                      // ignore: prefer_interpolation_to_compose_strings
                      : (DateTimeRange(
                                          start: data.birthdate!,
                                          end: DateTime.now())
                                      .duration
                                      .inDays /
                                  365)
                              .floor()
                              .toString() +
                          ' Years',
                ),
                Obx(() => dataTile(
                      icon: Iconsax.money_copy,
                      title: t.patientForm.balance,
                      data:
                          '${PatientProfileController.to.patient.value.balance} ${ConfigService.to.currency}',
                    )),
                dataTile(
                  icon: Iconsax.mobile_copy,
                  title: t.patientForm.phone,
                  data: data.phoneNumber,
                ),
                dataTile(
                  icon: Iconsax.people_copy,
                  title: t.patientGroups.nameInList,
                  data: data.patientGroups?.map((e) => e.name).join(', ') ??
                      'N/A',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  SizedBox dataTile({
    required IconData icon,
    required String title,
    required String data,
  }) {
    return SizedBox(
      height: 45,
      width: double.infinity,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 21),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(
                  height: 24,
                  width: 24,
                  child: Icon(
                    icon,
                    size: 24,
                    color: ThemeColors.text,
                  ),
                ),
                const SizedBox(width: 6),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 15,
                    color: ThemeColors.text,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(width: 6),
            Expanded(
              child: Text(
                data,
                textAlign: TextAlign.end,
                style: const TextStyle(
                  fontSize: 15,
                  color: ThemeColors.text,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
