import 'package:core_app/components/dialogs/basic.dart';
import 'package:core_app/components/tables/table.dart';
import 'package:core_app/core/models/payment.dart';
import 'package:core_app/core/utils/i18n/translations.g.dart';
import 'package:core_app/services/config.dart';
import 'package:flutter/material.dart';
import 'package:jiffy/jiffy.dart';

class PaymentListDialog extends StatelessWidget {
  const PaymentListDialog(this.payments, {super.key});

  final List<PaymentModel> payments;

  @override
  Widget build(BuildContext context) {
    return BasicDialog(
      title: t.patientProfile.payments,
      width: 1100,
      children: [
        TableComponent<PaymentModel>(
          title: t.patientProfile.payments,
          data: payments,
          columns: [
            TableColumn(
              flex: 1,
              title: t.payment.amount,
              minWidth: 150,
              builder: (data) {
                return TableColumn.stringBuilder(
                    '${data.amount} ${ConfigService.to.currency}');
              },
            ),
            TableColumn(
              flex: 1,
              title: t.payment.type,
              minWidth: 100,
              builder: (data) {
                return TableColumn.stringBuilder(data.type.name);
              },
            ),
            TableColumn(
              flex: 2,
              title: t.payment.branch,
              minWidth: 150,
              builder: (data) {
                return TableColumn.stringBuilder(data.branch?.name ?? 'N/A');
              },
            ),
            TableColumn(
              flex: 2,
              title: t.payment.createdBy,
              minWidth: 150,
              builder: (data) {
                return TableColumn.stringBuilder(data.createdBy?.name ?? 'N/A');
              },
            ),
            TableColumn(
              flex: 2,
              title: t.payment.createdAt,
              minWidth: 210,
              builder: (data) {
                return TableColumn.stringBuilder(
                    Jiffy.parseFromDateTime(data.createdAt).yMMMEdjm);
              },
            ),
            TableColumn(
              flex: 2,
              title: t.payment.cancelled,
              minWidth: 210,
              builder: (data) {
                return TableColumn.stringBuilder(data.cancelledAt == null
                    ? 'N/A'
                    : Jiffy.parseFromDateTime(data.cancelledAt!).yMMMEdjm);
              },
            ),
            TableColumn(
              flex: 2,
              title: t.payment.cancellationReason,
              minWidth: 250,
              builder: (data) {
                return TableColumn.stringBuilder(
                    data.cancellationReason ?? 'N/A',
                    clip: false);
              },
            ),
          ],
        ),
      ],
    );
  }
}
