import 'package:core_app/components/dialogs/basic.dart';
import 'package:core_app/core/utils/i18n/translations.g.dart';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

class EditDialog extends StatelessWidget {
  const EditDialog(this.title, this.initValue, {super.key});
  final String title;
  final String initValue;

  @override
  Widget build(BuildContext context) {
    final value = ''.obs;
    return BasicDialog(
      title: "${t.edit} $title",
      children: [
        TextFormField(
          initialValue: initValue,
          onChanged: value,
          maxLines: null,
          decoration: InputDecoration(
            labelText: title,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            TextButton(
              onPressed: () => Get.back(),
              child: Text(t.buttonTxt.cancel),
            ),
            TextButton(
              onPressed: () => Get.back(result: value.value),
              child: Text(t.buttonTxt.save),
            ),
          ],
        ),
      ],
    );
  }
}
