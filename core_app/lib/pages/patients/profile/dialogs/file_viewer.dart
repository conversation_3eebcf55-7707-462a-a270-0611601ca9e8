import 'dart:typed_data';

import 'package:core_app/components/dialogs/basic.dart';
import 'package:core_app/core/api/patients.dart';
import 'package:core_app/core/models/patient.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';

class FileViwer extends StatefulWidget {
  const FileViwer(this.file, {super.key});

  final PatientFileModel file;

  @override
  State<FileViwer> createState() => _FileViwerState();
}

class _FileViwerState extends State<FileViwer> {
  bool loading = true;
  late final Uint8List bytes;
  int imageWidth = 0;

  Future<void> fetchAPI() async {
    try {
      bytes = await PatientsAPI.getFile(widget.file.patientId, widget.file.id);
      imageWidth = (await decodeImageFromList(bytes)).width;
      setState(() {
        loading = false;
      });
    } catch (e) {
      // Handle error appropriately
      setState(() {
        loading = false;
      });
      // You might want to show an error message here
    }
  }

  @override
  void initState() {
    super.initState();
    fetchAPI();
  }

  @override
  Widget build(BuildContext context) {
    var name = widget.file.title;
    return BasicDialog(
      title: name,
      width: loading
          ? 500
          : name.isImageFileName && (imageWidth < 1100)
              ? imageWidth.toDouble()
              : 1100,
      children: [
        if (loading)
          const Center(child: CircularProgressIndicator())
        else if (name.isPDFFileName && !loading)
          SizedBox(
            height: context.height < 700 ? 250 : 500,
            child: SfPdfViewer.memory(
              bytes,
              enableDoubleTapZooming: true,
              canShowPaginationDialog: true,
            ),
          )
        else if (name.isImageFileName && !loading)
          ClipRRect(
            borderRadius: BorderRadius.circular(7),
            child: Image.memory(
              bytes,
              width: double.infinity,
            ),
          ),
      ],
    );
  }
}
