import 'package:core_app/components/buttons/icon.dart';
import 'package:core_app/components/buttons/text.dart';
import 'package:core_app/components/dialogs/basic.dart';
import 'package:core_app/components/tables/table.dart';
import 'package:core_app/core/api/patients.dart';
import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/core/constants/procedures_colors.dart';
import 'package:core_app/core/helpers/api_handler.dart';
import 'package:core_app/core/models/patient.dart';
import 'package:core_app/core/models/procedure.dart';
import 'package:core_app/pages/patients/profile/controller.dart';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ViewToothProcedures extends StatefulWidget {
  const ViewToothProcedures({
    super.key,
    required this.status,
    required this.toothPosition,
    required this.procedures,
  });

  final String toothPosition;
  final TeethStatus? status;
  final List<VisitProcedureModel> procedures;

  @override
  State<ViewToothProcedures> createState() => ViewToothProceduresState();
}

class ViewToothProceduresState extends State<ViewToothProcedures> {
  bool hideTemp = false;
  late TeethStatus? status;

  @override
  void initState() {
    status = widget.status;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedOpacity(
      opacity: hideTemp ? 0 : 1,
      duration: kThemeAnimationDuration,
      child: BasicDialog(
        width: 1000,
        dismissible: true,
        title: "Tooth Procedures",
        actions: [
          XIconButton(
            icon: Icons.edit_outlined,
            onPressed: () async {
              setState(() => hideTemp = true);
              TeethStatus? newStatus =
                  await Get.dialog(_EditToothNotes(status ?? TeethStatus()));
              if (newStatus == null) {
                setState(() => hideTemp = false);
                return;
              }
              var to = PatientProfileController.to;
              to.patient.update((val) {
                // if (val!.teethStatus[widget.toothPosition] == null) {
                //   var teethStatus = TeethStatus();
                //   val.teethStatus.addAll({widget.toothPosition: teethStatus});
                //   status = teethStatus;
                // } else {
                val!.teethStatus[widget.toothPosition] = newStatus;
                status = newStatus;
                // }
              });
              await tryAPI(() async {
                await PatientsAPI.update(
                  to.patientId,
                  teethStatus: to.patient.value.teethStatus,
                );
              });
              setState(() => hideTemp = false);
            },
          ),
        ],
        children: [
          Text.rich(TextSpan(children: [
            const TextSpan(
              text: 'Notes: ',
              style: TextStyle(
                fontSize: 16,
                color: ThemeColors.text,
                fontWeight: FontWeight.w500,
                fontStyle: FontStyle.italic,
              ),
            ),
            TextSpan(
              text: status?.notes.isEmpty ?? true ? 'None' : status?.notes,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: ThemeColors.notion,
              ),
            ),
          ])),
          const SizedBox(height: 16),
          TableComponent<VisitProcedureModel>(
            title: 'Procedures',
            data: widget.procedures,
            columns: [
              TableColumn(
                flex: 2,
                title: "Speciality",
                builder: (data) {
                  return TableColumn.stringBuilder(data.speciality.capitalize!);
                },
              ),
              TableColumn(
                flex: 2,
                title: "Procedure",
                builder: (data) {
                  return TableColumn.stringBuilder(data.procedure.capitalize!);
                },
              ),
              TableColumn(
                title: "Notes",
                builder: (data) {
                  return TableColumn.stringBuilder(data.notes.capitalize!);
                },
              ),
              TableColumn(
                title: "Next Visit",
                builder: (data) {
                  return TableColumn.stringBuilder(data.nextVisit.capitalize!);
                },
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class _EditToothNotes extends StatelessWidget {
  const _EditToothNotes(
    this.status, {
    Key? key,
  }) : super(key: key);

  final TeethStatus status;

  @override
  Widget build(BuildContext context) {
    final operative = status.operative.obs;
    final isRemoved = status.removed.obs;
    final isEndo = status.endo.obs;
    final isCrowned = status.crown.obs;
    final isImplant = status.implant.obs;
    return BasicDialog(
      title: "Edit tooth notes",
      children: [
        TextFormField(
          maxLines: null,
          initialValue: status.notes,
          onChanged: (val) {
            status.notes = val;
          },
          decoration: const InputDecoration(
            labelText: 'Notes',
          ),
        ),
        Obx(() => SwitchListTile.adaptive(
              value: isRemoved.value,
              activeColor: kRemovedToothColor,
              title: const Text('Tooth Removed'),
              onChanged: (value) {
                isRemoved.value = value;
                status.removed = value;
              },
            )),
        Obx(() => SwitchListTile.adaptive(
              value: isCrowned.value,
              activeColor: kCrownToothColor,
              title: const Text('Crown'),
              onChanged: (value) {
                isCrowned.value = value;
                status.crown = value;
              },
            )),
        Obx(() => SwitchListTile.adaptive(
              value: isEndo.value,
              activeColor: kEndoToothColor,
              title: const Text('Endo'),
              onChanged: (value) {
                isEndo.value = value;
                status.endo = value;
              },
            )),
        Obx(() => SwitchListTile.adaptive(
              value: isImplant.value,
              activeColor: kImplantToothColor,
              title: const Text('Implant'),
              onChanged: (value) {
                isImplant.value = value;
                status.implant = value;
              },
            )),
        const SizedBox(height: 13),
        Obx(() => DropdownButtonFormField<String>(
              decoration: const InputDecoration(
                labelText: 'Operative',
              ),
              value: operative.value,
              onChanged: (val) {
                if (val == null) {
                  operative.value = '';
                  status.operative = '';
                  return;
                }

                operative.value = val;
                status.operative = val;
              },
              items: [
                const DropdownMenuItem(
                  value: '',
                  child: Text('None'),
                ),
                for (var i = 1; i < 7; i++)
                  DropdownMenuItem(
                    value: '$i',
                    child: Text('Class $i'),
                  ),
                const DropdownMenuItem(
                  value: 'MOD',
                  child: Text('MOD'),
                ),
              ],
            )),
        const SizedBox(height: 13),
        Align(
          alignment: Alignment.centerRight,
          child: XTextButton(
            title: "Save",
            onPressed: () => Get.back(result: status),
          ),
        ),
      ],
    );
  }
}
