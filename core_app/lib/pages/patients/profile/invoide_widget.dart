// ignore_for_file: depend_on_referenced_packages, implementation_imports

import 'package:core_app/services/auth.dart';
import 'package:core_app/services/config.dart';
import 'package:get/get.dart';
import 'package:jiffy/jiffy.dart';
import 'package:pdf/widgets.dart';
import 'package:pdf/src/pdf/color.dart';

class InvoiceProcedure {
  final String name;
  final String speciality;
  final double price;

  const InvoiceProcedure({
    required this.name,
    required this.price,
    required this.speciality,
  });
}

Widget buildInvoid({
  required List<InvoiceProcedure> procedures,
  required ImageProvider logo,
  required String patientName,
  String? patientPhoneNumber,
  String? patientFileNumber,
}) {
  final double totalPrice =
      procedures.fold(0, (previousValue, e) => previousValue + e.price);
  var clinicModel = AuthService.to.clinic.value!;

  return Container(
    padding: const EdgeInsets.all(32), // Increased padding
    decoration: BoxDecoration(
      borderRadius: BorderRadius.circular(8),
      border: Border.all(color: const PdfColor(0.8, 0.8, 0.8), width: 1),
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Header Section
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Clinic Information
            Expanded(
              flex: 2,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'INVOICE',
                    style: TextStyle(
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                      color: PdfColor.fromHex('#1a1a1a'),
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    clinicModel.name.capitalize!,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: PdfColor.fromHex('#333333'),
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    'Dental Clinic',
                    style: TextStyle(
                      fontSize: 14,
                      color: PdfColor.fromHex('#666666'),
                    ),
                  ),
                ],
              ),
            ),

            // Invoice Details
            Expanded(
              flex: 1,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: PdfColor.fromHex('#f8f9fa'),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Invoice Date:',
                          style: TextStyle(
                            fontSize: 12,
                            color: PdfColor.fromHex('#666666'),
                          ),
                        ),
                        Text(
                          Jiffy.parseFromDateTime(DateTime.now()).yMMMEdjm,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: PdfColor.fromHex('#333333'),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),

        SizedBox(height: 30),

        // Patient Information Section
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: PdfColor.fromHex('#f8f9fa'),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Patient Information',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: PdfColor.fromHex('#333333'),
                ),
              ),
              SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Name:',
                          style: TextStyle(
                            fontSize: 12,
                            color: PdfColor.fromHex('#666666'),
                          ),
                        ),
                        Text(
                          patientName,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.normal,
                            color: PdfColor.fromHex('#1a1a1a'),
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (patientPhoneNumber != null &&
                      patientPhoneNumber.isNotEmpty)
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Phone:',
                            style: TextStyle(
                              fontSize: 12,
                              color: PdfColor.fromHex('#666666'),
                            ),
                          ),
                          Text(
                            patientPhoneNumber,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.normal,
                              color: PdfColor.fromHex('#1a1a1a'),
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
              if (patientFileNumber != null &&
                  patientFileNumber.isNotEmpty) ...[
                SizedBox(height: 8),
                Row(
                  children: [
                    Text(
                      'File Number: ',
                      style: TextStyle(
                        fontSize: 10,
                        color: PdfColor.fromHex('#666666'),
                      ),
                    ),
                    Text(
                      patientFileNumber,
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.normal,
                        color: PdfColor.fromHex('#1a1a1a'),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),

        SizedBox(height: 25),

        // Procedures Table Header
        Container(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          decoration: BoxDecoration(
            color: PdfColor.fromHex('#156DBF'), // Use primary color
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(6),
              topRight: Radius.circular(6),
            ),
          ),
          child: Row(
            children: [
              Expanded(
                flex: 3,
                child: Text(
                  'PROCEDURE',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: PdfColor.fromHex('#ffffff'),
                  ),
                ),
              ),
              Expanded(
                flex: 2,
                child: Text(
                  'SPECIALITY',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: PdfColor.fromHex('#ffffff'),
                  ),
                ),
              ),
              Expanded(
                flex: 1,
                child: Text(
                  'PRICE',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: PdfColor.fromHex('#ffffff'),
                  ),
                ),
              ),
            ],
          ),
        ),

        // Procedures Table Body
        ...procedures.asMap().entries.map((entry) {
          int index = entry.key;
          InvoiceProcedure procedure = entry.value;
          bool isEven = index % 2 == 0;

          return Container(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            decoration: BoxDecoration(
              color: isEven
                  ? PdfColor.fromHex('#ffffff')
                  : PdfColor.fromHex('#f8f9fa'),
              border: Border(
                bottom: BorderSide(
                  color: PdfColor.fromHex('#e9ecef'),
                  width: 0.5,
                ),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  flex: 3,
                  child: Text(
                    procedure.name,
                    style: TextStyle(
                      fontSize: 14,
                      color: PdfColor.fromHex('#333333'),
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    procedure.speciality,
                    style: TextStyle(
                      fontSize: 14,
                      color: PdfColor.fromHex('#666666'),
                    ),
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Text(
                    '${procedure.price} ${ConfigService.to.currency}',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.normal,
                      color: PdfColor.fromHex('#333333'),
                    ),
                  ),
                ),
              ],
            ),
          );
        }).toList(),

        // Total Section
        Row(
          children: [
            Spacer(),
            Container(
              width: 200, // About 1/3 of typical invoice width
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: PdfColor.fromHex('#156DBF'), // Use primary color
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(6),
                  bottomRight: Radius.circular(6),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    'TOTAL AMOUNT',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: PdfColor.fromHex('#ffffff'),
                    ),
                  ),
                  Text(
                    '$totalPrice ${ConfigService.to.currency}',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: PdfColor.fromHex('#ffffff'),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),

        SizedBox(height: 30),

        // Footer Section
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: PdfColor.fromHex('#f8f9fa'),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                'Thank you for choosing ${clinicModel.name.capitalize!}!',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.normal,
                  color: PdfColor.fromHex('#2c3e50'),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ],
    ),
  );
}
