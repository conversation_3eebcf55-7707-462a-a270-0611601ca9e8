import 'package:core_app/components/dialogs/basic.dart';
import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/core/models/user.dart';
import 'package:core_app/core/utils/i18n/translations.g.dart';
import 'package:core_app/layout/page_layout.dart';
import 'package:core_app/routes/navigation.dart';

import 'package:core_app/routes/route_paths.dart';
import 'package:core_app/services/auth.dart';
import 'package:core_app/services/storage.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:jiffy/jiffy.dart';

import '../change_password.dart';
import '../edit_clinic_dialog.dart';
import 'components/account_info.dart';
import 'components/billing_info.dart';
import 'controller.dart';

class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  @override
  Widget build(BuildContext context) {
    Get.put(SettingsController());
    return PageLayout(
      title: 'Settings',
      children: [
        Container(
          padding: const EdgeInsets.all(13),
          decoration: BoxDecoration(
            color: ThemeColors.bg,
            borderRadius: BorderRadius.circular(13),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const BackButton(
                color: ThemeColors.text,
                onPressed: Navigation.back,
              ),
              Text(
                t.settings.settings,
                style: const TextStyle(
                  color: ThemeColors.text,
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 21),
        const SettingsAcoountInfoComponent(),
        const SizedBox(height: 21),
        Container(
          padding: const EdgeInsets.all(13),
          decoration: BoxDecoration(
            color: ThemeColors.bg,
            borderRadius: BorderRadius.circular(13),
          ),
          child: Column(
            children: [
              if (kDebugMode)
                ListTile(
                  leading: const Icon(
                    Icons.edit_outlined,
                    color: ThemeColors.notion,
                  ),
                  title: Text(
                    t.settings.editClinicSettings,
                    style: const TextStyle(
                      color: ThemeColors.text,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  onTap: () async {
                    await Get.dialog(const EditClinicDialog());
                  },
                ),
              ListTile(
                leading: const Icon(
                  Icons.language,
                  color: ThemeColors.notion,
                ),
                title: Text(
                  t.settings.changeLanguage,
                  style: const TextStyle(
                    color: ThemeColors.text,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                onTap: () async {
                  await Get.dialog(const _ChangeLangDialog());
                  StorageService.language = Get.locale?.languageCode;
                },
              ),
              ListTile(
                leading: const Icon(
                  Icons.password_outlined,
                  color: ThemeColors.notion,
                ),
                title: Text(
                  t.settings.changePassword,
                  style: const TextStyle(
                    color: ThemeColors.text,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                onTap: () {
                  Get.to(() => const ChangePasswordForm(), id: 1);
                },
              ),
              ListTile(
                leading: const Icon(
                  Icons.logout,
                  color: Colors.red,
                ),
                title: Text(
                  t.settings.logout,
                  style: const TextStyle(
                    color: ThemeColors.text,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                onTap: () async {
                  await AuthService.to.logout();
                  Navigation.to(Routes.Login);
                },
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        if (AuthService.getUser.role.index == UserRole.master.index) ...[
          const SettingsBillingInfoComponent(),
          const SizedBox(height: 16),
        ]
      ],
    );
  }
}

class _ChangeLangDialog extends StatelessWidget {
  const _ChangeLangDialog();

  @override
  Widget build(BuildContext context) {
    // final lang = Get.locale?.languageCode;
    return BasicDialog(
      title: t.settings.changeLanguage,
      children: [
        ...AppLocale.values.map((locale) {
          // active locale
          AppLocale activeLocale = LocaleSettings.currentLocale;
          bool active = activeLocale == locale;
          return Material(
            color: Colors.transparent,
            child: ListTile(
              tileColor: active ? ThemeColors.primaryLighter : Colors.white,
              title: Text(t.locales[locale.languageTag]!),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(13),
              ),
              onTap: () {
                Jiffy.setLocale(locale.languageTag);
                LocaleSettings.setLocale(locale);
                Navigation.back();
              },
            ),
          );
        }).toList()
      ],
    );
  }
}
