import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/core/utils/i18n/translations.g.dart';
import 'package:core_app/core/utils/responsive.dart';
import 'package:core_app/pages/auth/settings/controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SettingsBillingInfoComponent extends StatelessWidget {
  const SettingsBillingInfoComponent({super.key});

  @override
  Widget build(BuildContext context) {
    final c = Get.find<SettingsController>();
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(13),
      decoration: BoxDecoration(
        color: ThemeColors.bg,
        borderRadius: BorderRadius.circular(13),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            t.settings.billing,
            style: TextStyle(
              color: ThemeColors.text.withValues(alpha: 0.81),
              fontWeight: FontWeight.w600,
              fontSize: 26,
            ),
          ),
          const SizedBox(height: 16),
          Obx(() => Wrap(
                runSpacing: 16,
                children: [
                  FractionallySizedBox(
                    widthFactor: context.mobileView ? 1 : 0.3,
                    child: Container(
                      padding: const EdgeInsets.all(13),
                      decoration: BoxDecoration(
                        color: ThemeColors.bg,
                        borderRadius: BorderRadius.circular(13),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            t.settings.totalVisits,
                            style: const TextStyle(
                              color: ThemeColors.notion,
                              fontWeight: FontWeight.w400,
                              fontSize: 13,
                            ),
                          ),
                          const SizedBox(height: 3),
                          Text(
                            c.billingStateRepo.value.visitsCount.toString(),
                            style: TextStyle(
                              color: ThemeColors.text.withValues(alpha: 0.81),
                              fontWeight: FontWeight.w500,
                              fontSize: 24,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  FractionallySizedBox(
                    widthFactor: context.mobileView ? 1 : 0.3,
                    child: Container(
                      padding: const EdgeInsets.all(13),
                      decoration: BoxDecoration(
                        color: ThemeColors.bg,
                        borderRadius: BorderRadius.circular(13),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            t.settings.pricePerVisit,
                            style: const TextStyle(
                              color: ThemeColors.notion,
                              fontWeight: FontWeight.w400,
                              fontSize: 13,
                            ),
                          ),
                          const SizedBox(height: 3),
                          Text(
                            '${c.pricePerVisit.toStringAsFixed(1)} ${c.billingStateRepo.value.currency.toUpperCase()}',
                            style: TextStyle(
                              color: ThemeColors.text.withValues(alpha: 0.81),
                              fontWeight: FontWeight.w500,
                              fontSize: 24,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  FractionallySizedBox(
                    widthFactor: context.mobileView ? 1 : 0.3,
                    child: Container(
                      padding: const EdgeInsets.all(13),
                      decoration: BoxDecoration(
                        color: ThemeColors.bg,
                        borderRadius: BorderRadius.circular(13),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            t.settings.totalPrice,
                            style: const TextStyle(
                              color: ThemeColors.notion,
                              fontWeight: FontWeight.w400,
                              fontSize: 13,
                            ),
                          ),
                          const SizedBox(height: 3),
                          Text(
                            '${c.billingStateRepo.value.bill} ${c.billingStateRepo.value.currency.toUpperCase()}',
                            style: TextStyle(
                              color: ThemeColors.text.withValues(alpha: 0.81),
                              fontWeight: FontWeight.w500,
                              fontSize: 24,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              )),
        ],
      ),
    );
  }
}
