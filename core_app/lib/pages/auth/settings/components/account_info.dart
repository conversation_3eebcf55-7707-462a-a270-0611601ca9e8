import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/services/auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SettingsAcoountInfoComponent extends StatelessWidget {
  const SettingsAcoountInfoComponent({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(13),
      decoration: BoxDecoration(
        color: ThemeColors.bg,
        borderRadius: BorderRadius.circular(13),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                "Account Info".tr,
                style: const TextStyle(
                  color: ThemeColors.text,
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
              ),
              Text(
                " (${AuthService.to.clinic.value!.displayName})",
                style: const TextStyle(
                  color: ThemeColors.text,
                  fontWeight: FontWeight.w400,
                  fontSize: 14,
                ),
              ),
            ],
          ),
          const SizedBox(height: 11),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: ThemeColors.notion.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(13),
                ),
                child: const Icon(
                  Icons.person,
                  color: ThemeColors.notion,
                  size: 30,
                ),
              ),
              const SizedBox(width: 16),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        AuthService.getUser.name,
                        style: const TextStyle(
                          color: ThemeColors.text,
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                      ),
                      Text(
                        " @${AuthService.getUser.username}",
                        style: TextStyle(
                          color: ThemeColors.text.withValues(alpha: 0.8),
                          fontWeight: FontWeight.w500,
                          fontSize: 13,
                        ),
                      )
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    AuthService.getUser.phoneNumber.isEmpty
                        ? "noPhoneNumber".tr
                        : AuthService.getUser.phoneNumber,
                    style: const TextStyle(
                      color: ThemeColors.text,
                      fontWeight: FontWeight.w400,
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Role: ${AuthService.getUser.role.name.tr}',
                    style: const TextStyle(
                      color: ThemeColors.text,
                      fontWeight: FontWeight.w400,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }
}
