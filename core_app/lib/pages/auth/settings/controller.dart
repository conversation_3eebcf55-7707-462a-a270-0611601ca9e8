import 'package:core_app/core/api/billing.dart';
import 'package:core_app/core/models/user.dart';
import 'package:core_app/services/auth.dart';
import 'package:core_app/services/config.dart';
import 'package:get/get.dart';

class SettingsController extends GetxController {
  final billingStateRepo = ((
    bill: 0.0,
    currency: ConfigService.to.currency,
    visitsCount: 0,
  )).obs;

  @override
  void onInit() {
    super.onInit();
    if (AuthService.getUser.role.index == UserRole.master.index) {
      BillingAPI.current().then((value) {
        billingStateRepo.value = value;
      });
    }
  }

  double get pricePerVisit {
    final pricePer =
        (billingStateRepo.value.bill / billingStateRepo.value.visitsCount);
    if (pricePer.isNaN) {
      return 0;
    }
    return pricePer;
  }
}
