import 'package:core_app/components/buttons/text.dart';
import 'package:core_app/components/dialogs/basic.dart';
import 'package:core_app/core/api/clinic.dart';
import 'package:core_app/core/utils/i18n/translations.g.dart';
import 'package:core_app/services/auth.dart';
import 'package:core_app/services/notify/messenger.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class EditClinicController extends GetxController {
  static EditClinicController get to => Get.find();

  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  final TextEditingController nameController = TextEditingController();
  final TextEditingController languageController = TextEditingController();
  final TextEditingController daysPriorController = TextEditingController();

  final RxBool includeLocation = false.obs;

  @override
  void onInit() {
    super.onInit();
    nameController.text = AuthService.to.clinic.value!.name;
    final clinicPatientNotification = AuthService.to.notification.value!;
    languageController.text = clinicPatientNotification.language.name;
    daysPriorController.text = clinicPatientNotification.daysPrior.toString();
    includeLocation.value = clinicPatientNotification.includeLocation;
  }

  Future<void> submit() async {
    if (formKey.currentState!.validate() == false) return;
    await clinicUpdate(
      displayName: nameController.text,
      language: languageController.text,
      daysPrior: int.parse(daysPriorController.text),
      includeLocation: includeLocation.value,
    );
    NotifyService.success(t.finish);
  }
}

class EditClinicDialog extends StatelessWidget {
  const EditClinicDialog({super.key});

  @override
  Widget build(BuildContext context) {
    final c = Get.put(EditClinicController());
    return Form(
      key: c.formKey,
      child: BasicDialog(
        title: 'editClinic'.tr,
        children: [
          ListTile(
            dense: true,
            title: Text(
              'clinicInfo'.tr,
              style: context.textTheme.labelLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          ListTile(
            title: TextFormField(
              controller: c.nameController,
              decoration: InputDecoration(
                labelText: t.clinic.name,
              ),
            ),
          ),
          ListTile(
            dense: true,
            title: Text(
              'notificationConfig'.tr,
              style: context.textTheme.labelLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 25),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('language'.tr),
                SizedBox(
                  width: 150,
                  child: DropdownButtonFormField(
                    onChanged: (val) {
                      c.languageController.text = val.toString();
                    },
                    value: c.languageController.text,
                    decoration: InputDecoration(
                      labelText: t.clinic.language,
                    ),
                    items: [
                      DropdownMenuItem(
                        value: 'en',
                        child: Text(t.english),
                      ),
                      DropdownMenuItem(
                        value: 'ar',
                        child: Text(t.arabic),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          ListTile(
            title: TextFormField(
              controller: c.daysPriorController,
              decoration: InputDecoration(
                labelText: t.clinic.daysPrior,
              ),
            ),
          ),
          // check box to include location
          Obx(() => CheckboxListTile(
                value: c.includeLocation.value,
                onChanged: c.includeLocation,
                title: Text('Include Location'.tr),
              )),
          const SizedBox(height: 20),
          Align(
            alignment: Alignment.centerRight,
            child: XTextButton(
              title: t.buttonTxt.save,
              onPressed: c.submit,
            ),
          )
        ],
      ),
    );
  }
}
