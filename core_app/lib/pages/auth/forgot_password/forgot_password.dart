import 'package:core_app/core/utils/responsive.dart';
import 'package:core_app/pages/auth/forgot_password/controller.dart';
import 'package:core_app/routes/navigation.dart';
import 'package:core_app/routes/route_paths.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ForgotPasswordPage extends StatelessWidget {
  final ForgotPasswordController controller =
      Get.find<ForgotPasswordController>();

  ForgotPasswordPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: const SizedBox(),
        centerTitle: true,
        title: const Text('Forgot Password'),
      ),
      body: Center(
        child: SizedBox(
          width: context.mobileView ? null : 375,
          child: Obx(() {
            if (controller.otpSent.value &&
                controller.otpResponseCode.value != "request_sent" &&
                !controller.otpVerified.value) {
              return _buildOtpForm();
            } else if (controller.otpVerified.value) {
              return _buildResetPasswordForm();
            } else {
              return _buildRequestResetForm();
            }
          }),
        ),
      ),
    );
  }

  Widget _buildRequestResetForm() {
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          const Text(
            'Enter your clinic name and username to reset your password',
            style: TextStyle(fontSize: 16),
          ),
          const SizedBox(height: 20),
          TextField(
            key: const Key('clinicNameField'),
            focusNode: controller.clinicNameFocus,
            decoration: const InputDecoration(
              labelText: 'Clinic Name',
              border: OutlineInputBorder(),
            ),
            onChanged: (value) => controller.clinicName.value = value,
            textInputAction: TextInputAction.done,
            onSubmitted: (_) {
              controller.usernameFocus.requestFocus();
            },
          ),
          const SizedBox(height: 15),
          TextField(
            key: const Key('usernameField'),
            focusNode: controller.usernameFocus,
            decoration: const InputDecoration(
              labelText: 'Username',
              border: OutlineInputBorder(),
            ),
            onChanged: (value) => controller.username.value = value,
            textInputAction: TextInputAction.done,
          ),
          const SizedBox(height: 25),
          ElevatedButton(
            onPressed: controller.isLoading.value
                ? null
                : controller.requestPasswordReset,
            child: controller.isLoading.value
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Request Password Reset'),
          ),
          const SizedBox(height: 15),
          TextButton(
            onPressed: () => Navigation.to(Routes.Login),
            child: const Text('Back to Login'),
          ),
        ],
      ),
    );
  }

  Widget _buildOtpForm() {
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          const Text(
            'Enter the OTP sent to your registered contact information',
            style: TextStyle(fontSize: 16),
          ),
          const SizedBox(height: 20),
          TextField(
            key: const Key('otpField'),
            focusNode: controller.otpFocus,
            decoration: const InputDecoration(
              labelText: 'OTP',
              border: OutlineInputBorder(),
            ),
            onChanged: (value) => controller.otp.value = value,
            keyboardType: TextInputType.number,
            textInputAction: TextInputAction.done,
          ),
          const SizedBox(height: 25),
          ElevatedButton(
            onPressed: controller.isLoading.value ? null : controller.verifyOTP,
            child: controller.isLoading.value
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Verify OTP'),
          ),
          const SizedBox(height: 15),
          TextButton(
            onPressed: () => Navigation.to(Routes.Login),
            child: const Text('Back to Login'),
          ),
        ],
      ),
    );
  }

  Widget _buildResetPasswordForm() {
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          const Text(
            'Enter your new password',
            style: TextStyle(fontSize: 16),
          ),
          const SizedBox(height: 20),
          TextField(
            key: const Key('newPasswordField'),
            focusNode: controller.newPasswordFocus,
            decoration: const InputDecoration(
              labelText: 'New Password',
              border: OutlineInputBorder(),
            ),
            obscureText: true,
            onChanged: (value) => controller.newPassword.value = value,
            textInputAction: TextInputAction.done,
            onSubmitted: (_) {
              controller.confirmPasswordFocus.requestFocus();
            },
          ),
          const SizedBox(height: 15),
          TextField(
            key: const Key('confirmPasswordField'),
            focusNode: controller.confirmPasswordFocus,
            decoration: const InputDecoration(
              labelText: 'Confirm Password',
              border: OutlineInputBorder(),
            ),
            obscureText: true,
            onChanged: (value) => controller.confirmPassword.value = value,
            textInputAction: TextInputAction.done,
          ),
          const SizedBox(height: 25),
          ElevatedButton(
            onPressed:
                controller.isLoading.value ? null : controller.resetPassword,
            child: controller.isLoading.value
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Reset Password'),
          ),
        ],
      ),
    );
  }
}
