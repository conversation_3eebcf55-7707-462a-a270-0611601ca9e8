import 'package:core_app/core/api/forgot_password.dart';
import 'package:core_app/routes/route_paths.dart';
import 'package:core_app/routes/navigation.dart';
import 'package:core_app/services/notify/messenger.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ForgotPasswordController extends GetxController {
  final clinicName = ''.obs;
  final username = ''.obs;
  final otp = ''.obs;
  final newPassword = ''.obs;
  final confirmPassword = ''.obs;
  final resetToken = ''.obs;

  final clinicNameFocus = FocusNode();
  final usernameFocus = FocusNode();
  final otpFocus = FocusNode();
  final newPasswordFocus = FocusNode();
  final confirmPasswordFocus = FocusNode();

  final isLoading = false.obs;
  final otpSent = false.obs;
  final otpVerified = false.obs;
  final otpResponseCode = ''.obs;

  Future<void> requestPasswordReset() async {
    if (clinicName.value.isEmpty || username.value.isEmpty) {
      NotifyService.notice(
        title: "Error",
        body: "Please enter clinic name and username",
      );
      return;
    }

    isLoading.value = true;
    try {
      final responseCode = await ForgotPasswordAPI.requestPasswordReset(
        clinicName: clinicName.value,
        username: username.value,
      );

      otpResponseCode.value = responseCode;
      otpSent.value = true;

      if (responseCode == "request_sent") {
        NotifyService.notice(
          title: "Request Sent",
          body: "Password reset request has been sent to clinic administrators",
        );
        Navigation.to(Routes.Login);
      } else if (responseCode == "otp_sent_sms") {
        NotifyService.notice(
          title: "OTP Sent",
          body: "An OTP has been sent to your registered mobile number",
        );
      } else if (responseCode == "otp_sent_email") {
        NotifyService.notice(
          title: "OTP Sent",
          body: "An OTP has been sent to your registered email address",
        );
      } else {
        NotifyService.notice(
          title: "OTP Sent",
          body: "An OTP has been sent to your registered contact information",
        );
      }
    } on DioException catch (e) {
      try {
        NotifyService.notice(
          title: e.response?.statusMessage ?? "Error",
          body: e.response?.data?['error'],
        );
      } catch (_) {
        NotifyService.notice(title: e.response?.statusMessage ?? "Error");
      }
    } catch (e) {
      NotifyService.notice(title: e.toString());
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> verifyOTP() async {
    if (otp.value.isEmpty) {
      NotifyService.notice(
        title: "Error",
        body: "Please enter the OTP",
      );
      return;
    }

    isLoading.value = true;
    try {
      final token = await ForgotPasswordAPI.verifyOTP(
        clinicName: clinicName.value,
        username: username.value,
        otp: otp.value,
      );

      resetToken.value = token;
      otpVerified.value = true;
      NotifyService.notice(
        title: "OTP Verified",
        body: "You can now reset your password",
      );
    } on DioException catch (e) {
      try {
        NotifyService.notice(
          title: e.response?.statusMessage ?? "Error",
          body: e.response?.data?['error'],
        );
      } catch (_) {
        NotifyService.notice(title: e.response?.statusMessage ?? "Error");
      }
    } catch (e) {
      NotifyService.notice(title: e.toString());
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> resetPassword() async {
    if (newPassword.value.isEmpty) {
      NotifyService.notice(
        title: "Error",
        body: "Please enter a new password",
      );
      return;
    }

    if (newPassword.value.length < 8) {
      NotifyService.notice(
        title: "Error",
        body: "Password must be at least 8 characters long",
      );
      return;
    }

    if (newPassword.value != confirmPassword.value) {
      NotifyService.notice(
        title: "Error",
        body: "Passwords do not match",
      );
      return;
    }

    isLoading.value = true;
    try {
      await ForgotPasswordAPI.resetPassword(
        token: resetToken.value,
        newPassword: newPassword.value,
      );

      NotifyService.notice(
        title: "Password Reset",
        body: "Your password has been reset successfully",
      );
      Navigation.off(Routes.Login);
    } on DioException catch (e) {
      try {
        NotifyService.notice(
          title: e.response?.statusMessage ?? "Error",
          body: e.response?.data?['error'],
        );
      } catch (_) {
        NotifyService.notice(title: e.response?.statusMessage ?? "Error");
      }
    } catch (e) {
      NotifyService.notice(title: e.toString());
    } finally {
      isLoading.value = false;
    }
  }
}
