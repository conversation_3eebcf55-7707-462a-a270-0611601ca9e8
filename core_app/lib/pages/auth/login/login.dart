import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/core/utils/i18n/translations.g.dart';
import 'package:core_app/core/utils/responsive.dart';
import 'package:core_app/pages/auth/login/controller.dart';
import 'package:core_app/routes/navigation.dart';
import 'package:core_app/routes/route_paths.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  bool _obscureText = true;

  void _togglePasswordVisibility() {
    setState(() {
      _obscureText = !_obscureText;
    });
  }

  @override
  Widget build(BuildContext context) {
    final c = Get.put(LoginController());
    final loginBox = Container(
      width: 375,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 32),
      decoration: BoxDecoration(
        color: context.theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.027),
            blurRadius: 7,
            spreadRadius: 3,
            offset: const Offset(2, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset('assets/naab_logo.png', height: 150),
          const SizedBox(height: 21),
          TextFormField(
            key: const Key('clinicNameField'),
            onChanged: c.clinicName,
            focusNode: c.clinicNameFocus,
            keyboardType: TextInputType.text,
            textInputAction: TextInputAction.next,
            onFieldSubmitted: (_) => c.usernameFocus.requestFocus(),
            decoration: InputDecoration(
              labelText: t.login.clinicName,
              prefixIcon: const Icon(Iconsax.location_copy),
            ),
          ),
          const SizedBox(height: 18),
          TextFormField(
            key: const Key('usernameField'),
            onChanged: c.username,
            focusNode: c.usernameFocus,
            keyboardType: TextInputType.text,
            textInputAction: TextInputAction.next,
            onFieldSubmitted: (_) => c.pwdFocus.requestFocus(),
            decoration: InputDecoration(
              labelText: t.login.username,
              prefixIcon: const Icon(Iconsax.user_copy),
            ),
          ),
          const SizedBox(height: 18),
          TextFormField(
            key: const Key('passwordField'),
            obscureText: _obscureText,
            onChanged: c.password,
            focusNode: c.pwdFocus,
            keyboardType: TextInputType.text,
            textInputAction: TextInputAction.done,
            decoration: InputDecoration(
              labelText: t.login.password,
              prefixIcon: const Icon(Iconsax.lock_copy),
              suffixIcon: IconButton(
                icon: Icon(
                  _obscureText ? Iconsax.eye_copy : Iconsax.eye_slash_copy,
                  size: 16,
                ),
                onPressed: _togglePasswordVisibility,
              ),
            ),
            onFieldSubmitted: (_) => c.login(
              c.clinicName.value,
              c.username.value,
              c.password.value,
            ),
          ),
          const SizedBox(height: 18),
          Align(
            alignment: Get.locale?.languageCode == 'ar'
                ? Alignment.centerLeft
                : Alignment.centerRight,
            child: ElevatedButton.icon(
              onPressed: () async => await c.login(
                  c.clinicName.value, c.username.value, c.password.value),
              icon: const Icon(Iconsax.login_copy, color: ThemeColors.primary),
              label: Text(
                t.login.login,
                style: const TextStyle(
                  color: ThemeColors.primary,
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
          const SizedBox(height: 18),
          TextButton(
            onPressed: () => Navigation.to(Routes.ForgotPassword),
            child: const Text('Forgot Password?'),
          ),
        ],
      ),
    );
    return Scaffold(
      backgroundColor: context.theme.colorScheme.surfaceContainerHighest,
      body: SingleChildScrollView(
        child: context.desktopView
            ? SizedBox(
                height: context.height,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Center(child: loginBox),
                  ],
                ),
              )
            : Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 21, vertical: 51),
                child: loginBox,
              ),
      ),
    );
  }
}
