import 'package:core_app/core/api/auth.dart';
import 'package:core_app/core/constants/configuration.dart';
import 'package:core_app/routes/navigation.dart';

import 'package:core_app/routes/route_paths.dart';
import 'package:core_app/services/auth.dart';
import 'package:core_app/services/notify/messenger.dart';
import 'package:dio/dio.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class LoginController extends GetxController {
  final clinicName = ''.obs;
  final username = ''.obs;
  final password = ''.obs;
  final clinicNameFocus = FocusNode(),
      usernameFocus = FocusNode(),
      pwdFocus = FocusNode();

  Future<void> login(String clinicName, String username, String pass) async {
    try {
      String token = await AuthAPI.login(
        password: pass,
        username: username,
        clinicName: clinicName,
      );
      final authService = AuthService.to;
      await authService.login(token);
      Navigation.to(Routes.Home);
      if (supportsFirebase) {
        await FirebaseAnalytics.instance.logLogin();
        await FirebaseAnalytics.instance
            .setUserId(id: authService.user.value!.id);
      }
    } on DioException catch (e) {
      try {
        NotifyService.notice(
          title: e.response?.statusMessage ?? "Connection Error",
          body: e.response?.data?['error'] ??
              "Unable to connect to server. Please check your internet connection.",
        );
      } catch (_) {
        NotifyService.notice(
          title: e.response?.statusMessage ?? "Connection Error",
          body:
              "Unable to connect to server. Please check your internet connection.",
        );
      }
    } catch (e) {
      NotifyService.notice(title: e.toString());
    }
  }
}
