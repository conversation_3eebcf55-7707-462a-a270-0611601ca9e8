import 'package:core_app/core/api/users.dart';
import 'package:core_app/core/helpers/api_handler.dart';
import 'package:core_app/core/models/user.dart';
import 'package:core_app/services/auth.dart';
import 'package:get/get.dart';

class UsersListController extends GetxController {
  final bool master = AuthService.getUser.role.index >= UserRole.master.index;
  final users = <UserModel>[].obs;

  Future<void> fetch() async {
    await tryAPI(() async {
      users.value = await UsersAPI.list();
    });
  }

  @override
  void onInit() {
    fetch();
    super.onInit();
  }
}
