import 'package:core_app/components/dialogs/basic.dart';
import 'package:core_app/components/fields/text.dart';
import 'package:core_app/core/api/users.dart';
import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/core/helpers/api_handler.dart';
import 'package:core_app/core/models/user.dart';
import 'package:core_app/core/utils/i18n/translations.g.dart';

import 'package:core_app/services/auth.dart';
import 'package:core_app/services/notify/messenger.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

part './controller.dart';

/// dialog
class UserFormDialog extends StatelessWidget {
  const UserFormDialog({this.user, super.key});

  final UserModel? user;

  @override
  Widget build(BuildContext context) {
    _UserFormController controller = Get.put(_UserFormController(user));
    return BasicDialog(
      title: t.userForm.title,
      children: [
        TextFormField(
          decoration: InputDecoration(
            labelText: t.userForm.username,
            helperText: 'Must be in English.',
          ),
          onChanged: controller.username,
          initialValue: user?.username,
        ),
        const SizedBox(height: 11),
        TextFormField(
          decoration: InputDecoration(labelText: t.userForm.name),
          onChanged: controller.name,
          initialValue: user?.name,
        ),
        if (user == null) const SizedBox(height: 11),
        if (user == null)
          TextFormField(
            decoration: InputDecoration(labelText: t.userForm.password),
            onChanged: controller.password,
          ),
        const SizedBox(height: 11),
        Obx(() => DropdownButtonFormField<UserRole>(
              decoration: InputDecoration(labelText: t.userForm.role),
              onChanged: controller.role,
              value: controller.role.value,
              items: [
                DropdownMenuItem(
                  value: UserRole.master,
                  child: Text(t.users.roles.master),
                ),
                DropdownMenuItem(
                  value: UserRole.admin,
                  child: Text(t.users.roles.admin),
                ),
                DropdownMenuItem(
                  value: UserRole.secretary,
                  child: Text(t.users.roles.secretary),
                ),
                DropdownMenuItem(
                  value: UserRole.basic,
                  child: Text(t.users.roles.basic),
                ),
                DropdownMenuItem(
                  value: UserRole.external,
                  child: Text(t.users.roles.external),
                ),
              ],
            )),
        const SizedBox(height: 11),
        Obx(() => CheckboxListTile(
              value: controller.isDentist.value,
              title: Text(
                t.userForm.isDentist,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: ThemeColors.text.withValues(alpha: 0.81),
                ),
              ),
              onChanged: controller.isDentist,
            )),
        const SizedBox(height: 11),
        if (AuthService.getUser.role == UserRole.master)
          Obx(() {
            if (controller.isDentist.value == false) return const SizedBox();
            return TextFormField(
              decoration: InputDecoration(labelText: t.userForm.percentage),
              initialValue: (user?.percentage ?? '').toString(),
              inputFormatters: [DecimalTextInputFormatter(2)],
              onChanged: (val) {
                double? value = double.tryParse(val);
                if (value == null) {
                  NotifyService.notice(
                    title: t.formErrors.invalid,
                    body: t.formErrors.shouldBeNumber,
                  );
                  return;
                }
                controller.percentage.value = value;
              },
            );
          }),
        const SizedBox(height: 11),
        if (AuthService.getUser.role == UserRole.master)
          TextFormField(
            decoration: const InputDecoration(labelText: "Hourly Rate"),
            initialValue:
                user != null ? (user?.hourlyRate ?? '0.0').toString() : '0.0',
            inputFormatters: [DecimalTextInputFormatter(2)],
            onChanged: (val) {
              double? value = double.tryParse(val);
              if (value == null) {
                NotifyService.notice(
                  title: t.formErrors.invalid,
                  body: t.formErrors.shouldBeNumber,
                );
                return;
              }
              controller.hourlyRate.value = value;
            },
          ),
        const SizedBox(height: 11),
        Align(
          alignment: Alignment.centerRight,
          child: ElevatedButton(
            onPressed:
                user == null ? controller.createUser : controller.updateUser,
            child: Text((t.buttonTxt.save)),
          ),
        ),
      ],
    );
  }
}
