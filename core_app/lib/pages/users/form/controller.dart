part of './form.dart';

class _UserFormController extends GetxController {
  _UserFormController(this.editedUser);

  final UserModel? editedUser;

  final username = "".obs;
  final name = "".obs;
  final password = "".obs;
  final phoneNumber = "".obs;
  final role = UserRole.admin.obs;
  final isDentist = false.obs;
  final percentage = 0.0.obs;
  final hourlyRate = 0.0.obs;

  void createUser() {
    if (username.isEmpty || name.isEmpty) {
      NotifyService.notice(
          title: "Invalid Data", body: "You must type username & name");
      return;
    }
    tryAPI(() async {
      UserModel user = await UsersAPI.create(
        name: name.value,
        username: username.value,
        role: role.value.name,
        isDentist: isDentist.value,
        password: password.value,
        phoneNumber: phoneNumber.value,
        percentage: percentage.value,
        hourlyRate: hourlyRate.value,
      );
      Get.back(result: user);
    });
  }

  void updateUser() {
    String? username_ = username.value;
    if (username.isEmpty) username_ = null;

    String? name_ = name.value;
    if (name.isEmpty) name_ = null;

    tryAPI(() async {
      UserModel user = await UsersAPI.update(
        editedUser!.id,
        username: username_,
        name: name_,
        role: role.value.name,
        isDentist: isDentist.value,
        percentage: percentage.value,
        hourlyRate: role.value == UserRole.master ? hourlyRate.value : null,
      );
      Get.back(result: user);
    });
  }

  @override
  void onInit() {
    if (editedUser != null) {
      username.value = editedUser!.username;
      name.value = editedUser!.name;
      role.value = editedUser!.role;
      isDentist.value = editedUser!.isDentist;
      percentage.value = editedUser!.percentage ?? 0.0;
      hourlyRate.value = editedUser!.hourlyRate ?? 0.0;
    }
    super.onInit();
  }
}
