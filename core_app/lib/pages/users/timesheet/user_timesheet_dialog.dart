import 'package:core_app/components/dialogs/basic.dart';
import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/core/models/timesheet.dart';
import 'package:core_app/core/models/user.dart';
import 'package:core_app/pages/timesheet/form/form.dart';
import 'package:core_app/pages/timesheet/list/confirm_delete_dialog.dart';
import 'package:core_app/pages/users/timesheet/user_timesheet_controller.dart';
import 'package:core_app/services/auth.dart';
import 'package:core_app/services/notify/messenger.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:jiffy/jiffy.dart';

class UserTimesheetDialog extends StatelessWidget {
  final UserModel user;

  const UserTimesheetDialog({
    super.key,
    required this.user,
  });

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(UserTimesheetController(user));

    return BasicDialog(
      title: '${user.name} - Timesheet',
      width: 840,
      children: [
        Obx(() {
          if (controller.isLoading.value) {
            return const Center(
              child: Padding(
                padding: EdgeInsets.all(20.0),
                child: CircularProgressIndicator(),
              ),
            );
          }

          if (controller.error.value.isNotEmpty) {
            return Center(
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Text(
                  'Error: ${controller.error.value}',
                  style: const TextStyle(color: ThemeColors.error),
                ),
              ),
            );
          }

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(controller),
              const SizedBox(height: 16),
              if (controller.entries.isEmpty)
                _buildEmptyState()
              else
                _buildTimesheetList(context, controller),
            ],
          );
        }),
      ],
    );
  }

  Widget _buildHeader(UserTimesheetController controller) {
    final now = DateTime.now();
    final monthYear = Jiffy.parseFromDateTime(now).format(pattern: 'MMMM yyyy');

    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                monthYear,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'Total: ${controller.totalHours.value} hours',
                style: const TextStyle(
                  color: ThemeColors.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (controller.totalSalary.value > 0) ...[
                const SizedBox(height: 4),
                Text(
                  'Total Salary: \$${controller.totalSalary.value.toStringAsFixed(2)}',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ],
          ),
          ElevatedButton.icon(
            icon: const Icon(Icons.add),
            label: const Text('Add Entry'),
            onPressed: () async {
              final result = await Get.dialog(const TimesheetFormDialog());
              if (result == true) {
                controller.fetchData();
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(40.0),
        child: Text(
          'No timesheet entries found for this month.',
          style: TextStyle(color: ThemeColors.notion),
        ),
      ),
    );
  }

  Widget _buildTimesheetList(
      BuildContext context, UserTimesheetController controller) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: SizedBox(
        width: 800,
        child: Table(
          columnWidths: const {
            0: FlexColumnWidth(2), // Date
            1: FlexColumnWidth(2), // Time
            2: FlexColumnWidth(1.5), // Duration
            3: FlexColumnWidth(2), // Branch
            4: FlexColumnWidth(2), // Actions
          },
          border: TableBorder(
            horizontalInside: BorderSide(
              width: 0.5,
              color: Colors.grey.shade300,
            ),
          ),
          children: [
            // Header row
            TableRow(
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
              ),
              children: const [
                Padding(
                  padding: EdgeInsets.symmetric(vertical: 8, horizontal: 4),
                  child: Text(
                    'Date',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.symmetric(vertical: 8, horizontal: 4),
                  child: Text(
                    'Time',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.symmetric(vertical: 8, horizontal: 4),
                  child: Text(
                    'Duration',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.symmetric(vertical: 8, horizontal: 4),
                  child: Text(
                    'Branch',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.symmetric(vertical: 8, horizontal: 4),
                  child: Text(
                    'Actions',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
            // Data rows
            ...controller.entries
                .map((entry) => _buildTimesheetRow(context, entry, controller)),
          ],
        ),
      ),
    );
  }

  TableRow _buildTimesheetRow(
    BuildContext context,
    TimesheetEntryModel entry,
    UserTimesheetController controller,
  ) {
    final startTime = Jiffy.parseFromDateTime(entry.startTime);
    final endTime =
        entry.endTime != null ? Jiffy.parseFromDateTime(entry.endTime!) : null;

    String duration = 'In progress';
    if (endTime != null) {
      final durationInMinutes =
          entry.endTime!.difference(entry.startTime).inMinutes;
      final hours = durationInMinutes ~/ 60;
      final minutes = durationInMinutes % 60;
      duration = '${hours}h ${minutes}m';
    }

    // Get branch color or use default
    Color branchColor = ThemeColors.primary;
    if (entry.branch != null && entry.branch!.color.isNotEmpty) {
      try {
        branchColor =
            Color(int.parse('0xFF${entry.branch!.color.replaceAll('#', '')}'));
      } catch (e) {
        // Fallback to default color if parsing fails
      }
    }

    return TableRow(
      children: [
        // Date
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 4),
          child: Text(
            startTime.yMMMd,
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
        ),
        // Time
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 4),
          child: Text(
              '${startTime.format(pattern: 'h:mm a')} - ${endTime?.format(pattern: 'h:mm a') ?? 'In progress'}'),
        ),
        // Duration
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 4),
          child: Text(duration),
        ),
        // Branch
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 4),
          child: Row(
            children: [
              Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: branchColor,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 6),
              Expanded(
                child: Text(
                  entry.branch?.name ?? '-',
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
        // Actions
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                iconSize: 18,
                tooltip: 'Edit',
                icon: const Icon(
                  Icons.edit_outlined,
                  size: 18,
                ),
                onPressed: () async {
                  final result = await Get.dialog(
                    TimesheetFormDialog(entry: entry),
                  );
                  if (result == true) {
                    controller.fetchData();
                  }
                },
              ),
              if (AuthService.to.user.value!.role != UserRole.master)
                IconButton(
                  iconSize: 18,
                  tooltip: 'Delete',
                  icon: const Icon(
                    Icons.delete_outline,
                    size: 18,
                    color: ThemeColors.error,
                  ),
                  onPressed: () async {
                    if (AuthService.to.user.value!.role != UserRole.master) {
                      return;
                    }

                    final confirmed =
                        await Get.dialog(const ConfirmTimesheetDelete());
                    if (confirmed == true) {
                      await controller.deleteEntry(entry.id);
                      NotifyService.success(
                          'Timesheet entry deleted successfully');
                    }
                  },
                ),
            ],
          ),
        ),
      ],
    );
  }
}
