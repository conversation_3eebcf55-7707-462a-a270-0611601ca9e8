import 'package:core_app/core/api/timesheet.dart';
import 'package:core_app/core/helpers/api_handler.dart';
import 'package:core_app/core/models/timesheet.dart';
import 'package:core_app/core/models/user.dart';
import 'package:get/get.dart';

class UserTimesheetController extends GetxController {
  final UserModel user;
  final entries = <TimesheetEntryModel>[].obs;
  final isLoading = true.obs;
  final error = ''.obs;
  final totalHours = 0.0.obs;
  final totalSalary = 0.0.obs;

  UserTimesheetController(this.user);

  @override
  void onInit() {
    super.onInit();
    fetchData();
  }

  Future<void> fetchData() async {
    isLoading.value = true;
    error.value = '';

    try {
      // Get the current month's start and end dates
      final now = DateTime.now();
      final startDate = DateTime(now.year, now.month, 1);
      final endDate = DateTime(now.year, now.month + 1, 0, 23, 59, 59);

      // Fetch timesheet entries for the user
      final summary = await TimesheetAPI.list(
        dentistId: user.id,
        startDate: startDate,
        endDate: endDate,
      );

      entries.value = summary.entries;
      totalHours.value = summary.totalHours;

      // Set total salary from API or calculate it if user has hourlyRate
      if (summary.totalSalary > 0) {
        totalSalary.value = summary.totalSalary;
      } else if (user.hourlyRate != null && user.hourlyRate! > 0) {
        totalSalary.value = totalHours.value * user.hourlyRate!;
      } else {
        totalSalary.value = 0;
      }
    } catch (e) {
      error.value = e.toString();
    } finally {
      isLoading.value = false;
    }
  }

  void calculateTotalHours() {
    double total = 0;

    for (var entry in entries) {
      if (entry.endTime != null) {
        final duration = entry.endTime!.difference(entry.startTime);
        total += duration.inMinutes / 60;
      }
    }

    totalHours.value = double.parse(total.toStringAsFixed(2));
  }

  Future<void> deleteEntry(String id) async {
    await tryAPI(() async {
      await TimesheetAPI.delete(id);
      entries.removeWhere((entry) => entry.id == id);
      calculateTotalHours();

      // Recalculate total salary if user has hourlyRate
      if (user.hourlyRate != null && user.hourlyRate! > 0) {
        totalSalary.value = totalHours.value * user.hourlyRate!;
      }
    });
  }
}
