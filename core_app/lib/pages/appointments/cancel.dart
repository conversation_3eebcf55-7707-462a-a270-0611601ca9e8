import 'package:core_app/components/buttons/text.dart';
import 'package:core_app/components/dialogs/basic.dart';
import 'package:core_app/core/api/appointments.dart';
import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/core/helpers/api_handler.dart';
import 'package:core_app/core/models/appointment.dart';
import 'package:core_app/core/utils/i18n/translations.g.dart';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

class CancelAppointment extends StatelessWidget {
  const CancelAppointment(this.repo, {super.key});

  final AppointmentModel repo;

  @override
  Widget build(BuildContext context) {
    return BasicDialog(
      title: t.appointment.cancel.title,
      children: [
        Text(
          t.appointment.cancel
              .message(patientName: repo.patient!.name.capitalize!),
          textAlign: TextAlign.start,
          style: const TextStyle(
            fontSize: 16,
            color: ThemeColors.text,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            OutlinedButton(
              child: Text(t.yes),
              onPressed: () {
                tryAPI(() async {
                  await AppointmentsAPI.cancel(repo.id);
                  Get.back(result: true);
                });
              },
            ),
            XTextButton(
              title: t.no,
              onPressed: Get.back,
            ),
          ],
        ),
      ],
    );
  }
}
