import 'package:core_app/core/api/appointments.dart';
import 'package:core_app/core/api/branches.dart';
import 'package:core_app/core/api/patients.dart';
import 'package:core_app/core/api/users.dart';
import 'package:core_app/core/helpers/api_handler.dart';
import 'package:core_app/core/models/appointment.dart';
import 'package:core_app/core/models/branch.dart';
import 'package:core_app/core/models/user.dart';
import 'package:core_app/core/utils/i18n/translations.g.dart';
import 'package:core_app/pages/patients/form/form.dart';

import 'package:core_app/services/notify/messenger.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:syncfusion_flutter_calendar/calendar.dart';

class AppointmentCreateController extends GetxController {
  static AppointmentCreateController get to => Get.find();

  final DateTime? choosenDay;
  final AppointmentModel? oldAppointment;
  AppointmentCreateController(
      {this.choosenDay, this.oldAppointment, String? selectedPatientId}) {
    patientId.value = selectedPatientId ?? '';
    if (oldAppointment != null) {
      patientId.value = oldAppointment!.patient!.id;
    }
  }

  final patientId = ''.obs;
  final patientName =
      ''.obs; // used for search - to get if patient is not exist
  //
  final selectedRoom = 1.obs;
  final selectedUser = Rx<UserModel?>(null);
  final selectedBranch = Rx<BranchModel?>(null);
  //
  final duration = '30'.obs;
  Rx<DateTime?> startTime = Rx<DateTime?>(null);
  //
  final calendarController = CalendarController();
  final startTimeController = TextEditingController();
  //
  final allBranches = <BranchModel>[].obs;
  final allDentists = <UserModel>[].obs;

  void loadData() async {
    allBranches.value = await BranchesAPI.list();
    final users = await UsersAPI.list();
    allDentists.value = users.where((e) => e.isDentist).toList();
    if (allBranches.length == 1) {
      selectedBranch.value = allBranches.first;
    }
    if (allDentists.length == 1) {
      selectedUser.value = allDentists.first;
    }

    // set old data if exist (edit mode)
    if (oldAppointment != null) {
      startTime.value = oldAppointment!.startTime;
      selectedBranch.value =
          allBranches.firstWhere((e) => e.id == oldAppointment!.branchId);
      selectedUser.value =
          allDentists.firstWhere((e) => e.id == oldAppointment!.dentistId);
      selectedRoom.value = oldAppointment!.room;
      duration.value = oldAppointment!.endTime
          .difference(oldAppointment!.startTime)
          .inMinutes
          .toString();
    } else {
      if (choosenDay != null) {
        startTime.value = DateTime(
          choosenDay!.year,
          choosenDay!.month,
          choosenDay!.day,
          DateTime.now().hour,
        );
      }
    }
  }

  @override
  void onInit() {
    if (choosenDay != null) {
      var now = DateTime.now();
      startTime.value = DateTime(
        choosenDay!.year,
        choosenDay!.month,
        choosenDay!.day,
        now.hour,
      );
    }
    loadData();
    super.onInit();
  }

  Future<void> _sendSaveRequest() async {
    tryAPI(() async {
      final endTime =
          startTime.value!.add(Duration(minutes: int.parse(duration.value)));
      AppointmentModel a = await AppointmentsAPI.create(
        patientId: patientId.value,
        startTime: startTime.value!,
        room: selectedRoom.value,
        endTime: endTime,
        branchId: selectedBranch.value!.id,
        dentistId: selectedUser.value!.id,
      );
      Get.back(result: a);
    });
  }

  Future<void> save() async {
    if (selectedBranch.value == null) {
      NotifyService.notice(
          title: t.blankFields,
          body: t.pleaseFillRequiredFields(field: 'branch'));
      return;
    }
    if (selectedUser.value == null) {
      NotifyService.notice(
          title: t.blankFields,
          body: t.pleaseFillRequiredFields(field: 'dentist'));
      return;
    }

    if (patientId.value.isEmpty && patientName.isNotEmpty) {
      final patientName2 = patientName.trim().capitalize!;
      // confirm dialog to create a new patient
      final confirm = await Get.defaultDialog<bool>(
        title: t.patient.addNewPatient,
        content: Text(
          t.patient.askToCreatePatient(name: patientName2),
          textAlign: TextAlign.center,
        ),
        textConfirm: t.yes,
        textCancel: t.no,
        buttonColor: Colors.blue,
        onConfirm: () => Get.back(result: true),
      );
      if ((confirm ?? false) == false) {
        NotifyService.notice(
          title: t.blankFields,
          body: t.pleaseFillRequiredFields(field: 'patient'),
        );
        return;
      }

      final newPatientData = await PatientsAPI.create(name: patientName2);
      patientId.value = newPatientData.id;
      await _sendSaveRequest();
      await Future.delayed(const Duration(milliseconds: 1000));
      await Get.dialog(PatientForm(newPatientData));
      return;
    }

    await _sendSaveRequest();
  }
}
