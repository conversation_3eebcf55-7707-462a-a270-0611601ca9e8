import 'package:core_app/components/buttons/text.dart';
import 'package:core_app/components/dialogs/basic.dart';
import 'package:core_app/components/search_patient.dart';
import 'package:core_app/core/models/appointment.dart';
import 'package:core_app/core/models/branch.dart';
import 'package:core_app/core/models/user.dart';
import 'package:core_app/core/utils/i18n/translations.g.dart';
import 'package:core_app/pages/appointments/create/controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:jiffy/jiffy.dart';

import 'components/choose_day.dart';

class CreateAppointmentDialog extends StatelessWidget {
  const CreateAppointmentDialog({
    super.key,
    this.patientId,
    this.choosenDay,
    this.oldAppointment,
  });

  final String? patientId;
  final DateTime? choosenDay;
  final AppointmentModel? oldAppointment;

  @override
  Widget build(BuildContext context) {
    final c = Get.put(AppointmentCreateController(
      choosenDay: choosenDay,
      selectedPatientId: patientId,
      oldAppointment: oldAppointment,
    ));
    return BasicDialog(
      width: 950,
      title: t.appointment.addNewAppointment,
      children: [
        Wrap(
          spacing: 20,
          runSpacing: 20,
          runAlignment: WrapAlignment.spaceBetween,
          alignment: WrapAlignment.spaceBetween,
          children: [
            SizedBox(
              width: context.width >= 975 ? 300 : double.infinity,
              child: Column(
                children: [
                  if (patientId == null)
                    SearchPatients(
                      (id) => c.patientId.value = id,
                      onSearchInputChange: c.patientName,
                    ),
                  const SizedBox(height: 16),
                  Obx(() => DropdownButtonFormField<UserModel>(
                        onChanged: c.selectedUser,
                        value: c.selectedUser.value,
                        decoration:
                            InputDecoration(labelText: t.appointment.dentist),
                        items: [
                          for (var u in c.allDentists)
                            DropdownMenuItem(
                              value: u,
                              child: Text(u.name.capitalize!),
                            ),
                        ],
                      )),
                  const SizedBox(height: 16),
                  Obx(() => DropdownButtonFormField<BranchModel>(
                        value: c.selectedBranch.value,
                        decoration:
                            InputDecoration(labelText: t.appointment.branch),
                        onChanged: (val) {
                          c.selectedBranch.value = val;
                          c.selectedRoom.value = 1;
                        },
                        items: [
                          for (var branch in c.allBranches)
                            DropdownMenuItem(
                              value: branch,
                              child: Text(branch.name.capitalize!),
                            ),
                        ],
                      )),
                  const SizedBox(height: 16),
                  Obx(() => DropdownButtonFormField<int>(
                        value: c.selectedRoom.value,
                        decoration:
                            InputDecoration(labelText: t.appointment.room),
                        onChanged: c.selectedRoom,
                        items: [
                          for (var i = 1;
                              i <= (c.selectedBranch.value?.rooms ?? 1);
                              i++)
                            DropdownMenuItem(
                              value: i,
                              child: Text(t.appointment.roomNumber(number: i)),
                            ),
                        ],
                      )),
                ],
              ),
            ),
            SizedBox(
              width: context.width >= 975 ? 550 : double.infinity,
              child: Column(
                children: [
                  const DayPickerComponent(),
                  const SizedBox(height: 16),
                  LayoutBuilder(builder: (context, cons) {
                    return Obx(() => AnimatedOpacity(
                          duration: kThemeAnimationDuration,
                          opacity: c.startTime.value == null ? .51 : 1,
                          child: IgnorePointer(
                            ignoring: c.startTime.value == null,
                            child: Wrap(
                              spacing: 16,
                              runSpacing: 16,
                              alignment: WrapAlignment.spaceBetween,
                              runAlignment: WrapAlignment.spaceBetween,
                              crossAxisAlignment: WrapCrossAlignment.start,
                              children: [
                                SizedBox(
                                  width: context.width >= 975
                                      ? cons.maxWidth / 2 - 11
                                      : cons.maxWidth,
                                  child: TextFormField(
                                    readOnly: true,
                                    controller: c.startTimeController,
                                    decoration: InputDecoration(
                                      labelText: t.appointment.startTime,
                                    ),
                                    onTap: () async {
                                      TimeOfDay? time = await showTimePicker(
                                        context: context,
                                        initialTime: TimeOfDay.fromDateTime(
                                            c.startTime.value!),
                                      );
                                      if (time == null) return;
                                      c.startTime.value = DateTime(
                                        c.startTime.value!.year,
                                        c.startTime.value!.month,
                                        c.startTime.value!.day,
                                        time.hour,
                                        time.minute,
                                      );
                                      c.startTimeController.text =
                                          Jiffy.parseFromDateTime(
                                                  c.startTime.value!)
                                              .yMMMdjm;
                                    },
                                  ),
                                ),
                                SizedBox(
                                  width: context.width >= 975
                                      ? cons.maxWidth / 2 - 11
                                      : cons.maxWidth,
                                  child: DropdownButtonFormField<String>(
                                    onChanged: c.duration,
                                    value: c.duration.value,
                                    decoration: InputDecoration(
                                      labelText: t.appointment.duration,
                                    ),
                                    items: {
                                      15: "15 Minutes",
                                      30: "30 Minutes",
                                      45: "45 Minutes",
                                      60: "1 Hour",
                                      75: "1 Hour 15 Minutes",
                                      90: "1 Hour 30 Minutes",
                                      105: "1 Hour 45 Minutes",
                                      120: "2 Hours",
                                      135: "2 Hours 15 Minutes",
                                      150: "2 Hours 30 Minutes",
                                      165: "2 Hours 45 Minutes",
                                      180: "3 Hours",
                                      195: "3 Hours 15 Minutes",
                                      210: "3 Hours 30 Minutes",
                                      225: "3 Hours 45 Minutes",
                                      240: "4 hours",
                                    }
                                        .entries
                                        .map((e) => DropdownMenuItem<String>(
                                              value: e.key.toString(),
                                              child: Text(
                                                e.value,
                                                style: const TextStyle(
                                                  fontSize: 13,
                                                ),
                                              ),
                                            ))
                                        .toList(),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ));
                  }),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        const SizedBox(height: 11),
        Align(
          alignment: Alignment.centerRight,
          child: XTextButton(
            title: t.buttonTxt.save,
            onPressed: c.save,
          ),
        ),
      ],
    );
  }
}
