import 'package:core_app/core/api/insurances.dart';
import 'package:core_app/core/models/insurance.dart';
import 'package:get/get.dart';

class InsuranceListController extends GetxController {
  final selectedCompanyId = ''.obs;
  final insuranceCompanies = <InsuranceCompanyModel>[].obs;
  final claims = <InsuranceClaimModel>[].obs;

  @override
  Future<void> onInit() async {
    super.onInit();
    await fetchCompanies();
    if (insuranceCompanies.isNotEmpty) {
      selectedCompanyId.value = insuranceCompanies.first.id;
      fetchClaims();
    }
  }

  Future<void> fetchCompanies() async {
    insuranceCompanies.value = await InsurancesAPI.listCompanies();
  }

  void fetchClaims() {
    InsurancesAPI.listClaims(selectedCompanyId.value).then((value) {
      claims.value = value;
    });
  }
}
