import 'package:core_app/components/buttons/popup_menu.dart';
import 'package:core_app/components/tables/table.dart';
import 'package:core_app/core/api/insurances.dart';
import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/core/helpers/api_handler.dart';
import 'package:core_app/core/models/insurance.dart';
import 'package:core_app/core/utils/i18n/translations.g.dart';
import 'package:core_app/layout/page_layout.dart';
import 'package:core_app/pages/insurance/claim/edit.dart';
import 'package:core_app/pages/insurance/company/form.dart';

import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:jiffy/jiffy.dart';

import 'controller.dart';

class InsuranceList extends StatelessWidget {
  const InsuranceList({super.key});

  @override
  Widget build(BuildContext context) {
    final c = Get.find<InsuranceListController>();
    return PageLayout(
      children: [
        Obx(() => Wrap(
              spacing: 11,
              runSpacing: 11,
              children: [
                for (var company in c.insuranceCompanies)
                  GestureDetector(
                    onTap: () {
                      if (c.selectedCompanyId.value != company.id) {
                        c.selectedCompanyId.value = company.id;
                        c.fetchClaims();
                      }
                    },
                    child: Container(
                      constraints: const BoxConstraints(minWidth: 210),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(13),
                        border: Border.all(
                          width:
                              c.selectedCompanyId.value == company.id ? 1 : .5,
                          color: c.selectedCompanyId.value == company.id
                              ? ThemeColors.primaryLight
                              : Colors.grey.shade400,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  company.name,
                                  style: const TextStyle(
                                    color: ThemeColors.text,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 26,
                                  ),
                                ),
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Text(
                                      '${t.insuranceCompany.defaultCoverage}: ',
                                      style: const TextStyle(
                                        color: ThemeColors.notion,
                                        fontWeight: FontWeight.w400,
                                      ),
                                    ),
                                    Text(
                                      "${company.defaultPercentageCoverage}%",
                                      style: const TextStyle(
                                        color: Colors.grey,
                                        // fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ],
                                )
                              ],
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: AutoPopupMenu(
                              items: [
                                AutoPopupMenuItem(
                                  name: t.insuranceView.edit,
                                  onTap: () async {
                                    final InsuranceCompanyModel? model =
                                        await Get.dialog(
                                            InsuranceCompanyFormDialog(
                                                company));
                                    if (model != null) {
                                      c.fetchCompanies();
                                    }
                                  },
                                ),
                                AutoPopupMenuItem(
                                  name: t.insuranceView.delete,
                                  onTap: () {
                                    tryAPI(() async {
                                      await InsurancesAPI.deleteCompany(
                                          id: company.id);
                                      c.fetchCompanies();
                                    });
                                  },
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                Material(
                  clipBehavior: Clip.antiAlias,
                  color: Colors.transparent,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(13),
                  ),
                  child: InkWell(
                    onTap: () async {
                      final InsuranceCompanyModel? model = await Get.dialog(
                          const InsuranceCompanyFormDialog(null));
                      if (model != null) {
                        c.insuranceCompanies.add(model);
                      }
                    },
                    child: SizedBox(
                      height: 90,
                      width: 100,
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            const Icon(Icons.add,
                                size: 24, color: ThemeColors.text),
                            const SizedBox(height: 5),
                            Text(
                              t.insuranceView.add,
                              style: const TextStyle(
                                color: ThemeColors.text,
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            )),
        const SizedBox(height: 20),
        Obx(() => TableComponent<InsuranceClaimModel>(
              title: t.insuranceView.claims,
              data: c.claims.value,
              key: Key('claims-table${c.selectedCompanyId.value}'),
              onRowTap: (data) {
                Get.dialog(ClaimEditDialog(data));
              },
              columns: [
                TableColumn(
                  minWidth: 200,
                  title: t.insuranceClaim.date,
                  builder: (claim) => Text(
                    Jiffy.parseFromDateTime(claim.createdAt).yMMMMEEEEdjm,
                    style: const TextStyle(
                      color: ThemeColors.text,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                TableColumn(
                  minWidth: 100,
                  title: t.insuranceClaim.patient,
                  builder: (claim) => Text(
                    (claim.patient?.name).toString(),
                    style: const TextStyle(
                      color: ThemeColors.text,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                TableColumn(
                  flex: 1,
                  minWidth: 100,
                  title: '',
                  builder: (data) {
                    return Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        IconButton(
                          iconSize: 18,
                          icon: const Icon(
                            Iconsax.edit_copy,
                            size: 18,
                          ),
                          onPressed: () async {
                            Get.dialog(ClaimEditDialog(data));
                          },
                        ),
                        IconButton(
                          iconSize: 18,
                          icon: const Icon(
                            Iconsax.trash_copy,
                            size: 18,
                          ),
                          onPressed: () async {
                            tryAPI(() async {
                              await InsurancesAPI.deleteClaim(
                                id: data.id,
                                companyId: data.insuranceCompanyId,
                              );
                              c.claims.remove(data);
                            });
                          },
                        ),
                      ],
                    );
                  },
                ),
              ],
            ))
      ],
    );
  }
}
