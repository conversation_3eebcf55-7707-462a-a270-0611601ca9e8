import 'package:core_app/components/dialogs/basic.dart';
import 'package:core_app/core/api/insurances.dart';
import 'package:core_app/core/models/insurance.dart';
import 'package:core_app/core/utils/i18n/translations.g.dart';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

class _FormController extends GetxController {
  final formKey = GlobalKey<FormState>();
  final name = TextEditingController();
  final defaultPercentage = TextEditingController();

  final InsuranceCompanyModel? insuranceCompany;
  _FormController(this.insuranceCompany) {
    if (insuranceCompany != null) {
      name.text = insuranceCompany!.name;
      defaultPercentage.text =
          insuranceCompany!.defaultPercentageCoverage.toString();
    }
  }

  Future<void> save() async {
    if (formKey.currentState!.validate() == false) return;
    if (insuranceCompany != null) {
      final result = await InsurancesAPI.updateCompany(
        id: insuranceCompany!.id,
        name: name.text,
        defaultPercentageCoverage: double.parse(defaultPercentage.text),
      );

      Get.back(result: result);
      return;
    }

    // create
    final result = await InsurancesAPI.createCompany(
      name: name.text,
      defaultPercentageCoverage: double.parse(defaultPercentage.text),
    );
    Get.back(result: result);
  }
}

class InsuranceCompanyFormDialog extends StatelessWidget {
  const InsuranceCompanyFormDialog(this.insuranceCompany, {super.key});

  final InsuranceCompanyModel? insuranceCompany;

  @override
  Widget build(BuildContext context) {
    final c = Get.put(_FormController(insuranceCompany));
    return Form(
      key: c.formKey,
      child: BasicDialog(
        title: insuranceCompany == null
            ? t.insuranceCompanyView.add
            : t.insuranceCompanyView.edit,
        children: [
          TextFormField(
            controller: c.name,
            decoration: InputDecoration(
              hintText: t.insuranceCompany.name,
            ),
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: c.defaultPercentage,
            decoration: InputDecoration(
              hintText: t.insuranceCompany.defaultCoverage,
            ),
          ),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              ElevatedButton(onPressed: c.save, child: Text(t.buttonTxt.save)),
            ],
          ),
        ],
      ),
    );
  }
}
