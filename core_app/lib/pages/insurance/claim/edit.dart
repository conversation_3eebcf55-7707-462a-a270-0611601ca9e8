import 'package:core_app/components/dialogs/basic.dart';
import 'package:core_app/core/api/insurances.dart';
import 'package:core_app/core/helpers/api_handler.dart';
import 'package:core_app/core/models/insurance.dart';
import 'package:core_app/core/utils/i18n/translations.g.dart';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

class _EditClaimController extends GetxController {
  final items = <ClaimItemModel>[].obs;

  final InsuranceClaimModel claim;
  _EditClaimController(this.claim);

  @override
  Future<void> onInit() async {
    super.onInit();

    final val =
        await InsurancesAPI.listClaimItems(claim.insuranceCompanyId, claim.id);
    items.addAll(val.$2);
  }

  Future<void> save() async {
    tryAPI(() async {
      await InsurancesAPI.updateClaim(
        companyId: claim.insuranceCompanyId,
        id: claim.id,
        items: items
            .map<ClaimItemCreate>((e) => (
                  price: e.price,
                  treatment: e.treatment,
                  percentageCoverage: e.percentageCoverage,
                  status: e.status,
                ))
            .toList(),
      );
      Get.back(result: true);
    });
  }
}

class ClaimEditDialog extends StatelessWidget {
  const ClaimEditDialog(this.claim, {super.key});

  final InsuranceClaimModel? claim;

  @override
  Widget build(BuildContext context) {
    final c = Get.put(_EditClaimController(claim!));
    return BasicDialog(
      title: t.insuranceClaimView.edit,
      children: [
        // show patient name as text
        Text(
          '${t.insuranceClaim.patient}: ${claim!.patient!.name}',
          style: Theme.of(context).textTheme.labelLarge,
        ),
        const SizedBox(height: 16),
        Obx(() => ListView.separated(
              shrinkWrap: true,
              itemCount: c.items.length,
              physics: const NeverScrollableScrollPhysics(),
              separatorBuilder: (context, index) => const Divider(
                endIndent: 16,
                indent: 16,
                height: 26,
                thickness: .3,
              ),
              itemBuilder: (context, index) {
                final treatment = c.items[index];
                return Column(
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            initialValue: treatment.treatment,
                            onChanged: (val) => treatment.treatment = val,
                            decoration: InputDecoration(
                              labelText: t.insuranceClaimForm.treatments,
                            ),
                          ),
                        ),
                        const SizedBox(width: 7),
                        SizedBox(
                          width: 150,
                          child: DropdownButtonFormField<String>(
                            value: treatment.status,
                            onChanged: (val) => treatment.status = val!,
                            items: ['pending', 'approved', 'rejected']
                                .map((e) => DropdownMenuItem(
                                      value: e,
                                      child: Text(e.capitalizeFirst!),
                                    ))
                                .toList(),
                            decoration: const InputDecoration(
                              labelText: 'Status',
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 11),
                    Row(
                      children: [
                        Flexible(
                          child: TextFormField(
                            initialValue: treatment.price.toString(),
                            onChanged: (val) =>
                                treatment.price = double.tryParse(val) ?? 0,
                            decoration: const InputDecoration(
                              labelText: 'Price',
                            ),
                          ),
                        ),
                        const SizedBox(width: 7),
                        Flexible(
                          child: TextFormField(
                            initialValue:
                                treatment.percentageCoverage.toString(),
                            onChanged: (val) => treatment.percentageCoverage =
                                double.tryParse(val) ?? 0,
                            decoration: const InputDecoration(
                              labelText: 'Percentage Coverage',
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                );
              },
            )),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            ElevatedButton(
              onPressed: c.save,
              child: const Text('Save'),
            ),
          ],
        )
      ],
    );
  }
}
