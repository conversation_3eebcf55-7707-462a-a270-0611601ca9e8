import 'package:core_app/core/api/analysis.dart';
import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/core/utils/i18n/translations.g.dart';
import 'package:core_app/core/utils/responsive.dart';
import 'package:core_app/layout/page_layout.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

import 'controller.dart';

const chartColors = <Color>[
  ThemeColors.primary,
  Colors.red,
  Colors.amber,
  Colors.brown,
  Colors.cyan,
  Colors.deepOrange,
  Colors.orange,
];

class AnalysisPage extends StatelessWidget {
  const AnalysisPage({super.key});

  @override
  Widget build(BuildContext context) {
    final c = Get.find<AnalysisController>();
    return PageLayout(
      onRefresh: () async {
        await c.fetchAPI();
      },
      children: [
        // visits diagraam title
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(13),
          decoration: BoxDecoration(
            color: ThemeColors.bg,
            borderRadius: BorderRadius.circular(13),
          ),
          child: Flex(
            direction: context.mobileView ? Axis.vertical : Axis.horizontal,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                t.analysis.title,
                style: context.textTheme.headlineSmall,
              ),
              Obx(() => Wrap(
                    crossAxisAlignment: WrapCrossAlignment.center,
                    children: [
                      // calendar icon
                      const Icon(
                        Icons.calendar_month_outlined,
                        color: ThemeColors.notion,
                        size: 21,
                      ),
                      TextButton(
                        onPressed: () async {
                          final result = await showDatePicker(
                            context: context,
                            initialDate: c.startTime.value,
                            firstDate: DateTime.now()
                                .subtract(const Duration(days: 365)),
                            lastDate: c.endTime.value,
                            builder: (context, child) {
                              return Theme(
                                data: Theme.of(context).copyWith(
                                  dialogTheme: const DialogThemeData(
                                    backgroundColor: Colors.white,
                                  ),
                                ),
                                child: child!,
                              );
                            },
                          );
                          if (result != null) {
                            c.startTime.value = result;
                            c.period.value = Period.custom;
                            c.fetchAPI();
                          }
                        },
                        child: Text.rich(TextSpan(children: [
                          TextSpan(
                            text: '${t.from}: ',
                            style: const TextStyle(
                              color: ThemeColors.notion,
                              fontSize: 14,
                            ),
                          ),
                          TextSpan(
                            text: c.startTime.value.toString().substring(0, 10),
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              color: ThemeColors.primaryDark,
                            ),
                          ),
                        ])),
                      ),
                      const SizedBox(
                        height: 11,
                        child: VerticalDivider(
                          width: 3,
                          thickness: .3,
                          color: ThemeColors.notion,
                        ),
                      ),
                      TextButton(
                        onPressed: () async {
                          final result = await showDatePicker(
                            context: context,
                            initialDate: c.endTime.value,
                            firstDate: c.startTime.value,
                            lastDate:
                                DateTime.now().add(const Duration(days: 365)),
                          );
                          if (result != null) {
                            c.endTime.value = result;
                            c.period.value = Period.custom;
                            c.fetchAPI();
                          }
                        },
                        child: Text.rich(TextSpan(children: [
                          TextSpan(
                            text: '${t.to}: ',
                            style: const TextStyle(
                              color: ThemeColors.notion,
                              fontSize: 14,
                            ),
                          ),
                          TextSpan(
                            text: c.endTime.value.toString().substring(0, 10),
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              color: ThemeColors.primaryDark,
                            ),
                          ),
                        ])),
                      ),
                    ],
                  )),
            ],
          ),
        ),
        const SizedBox(height: 13),
        Container(
          padding: const EdgeInsets.all(13),
          decoration: BoxDecoration(
            color: ThemeColors.bg,
            borderRadius: BorderRadius.circular(13),
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    t.analysis.visitsDiagram,
                    style: context.textTheme.titleMedium,
                  ),
                ],
              ),
              const SizedBox(height: 18),
              Obx(() => SfCartesianChart(
                    enableAxisAnimation: true,
                    borderColor: Colors.transparent,
                    palette: const [ThemeColors.primary, Colors.cyan],
                    tooltipBehavior:
                        TooltipBehavior(enable: true, duration: 2000),
                    primaryXAxis: const CategoryAxis(
                      isVisible: true,
                      majorGridLines: MajorGridLines(color: Colors.transparent),
                      // minorGridLines: const MinorGridLines(color: Colors.transparent),
                    ),
                    zoomPanBehavior: ZoomPanBehavior(
                      enablePanning: true,
                      enablePinching: true,
                      // enableMouseWheelZooming: true,
                    ),
                    series: [
                      SplineAreaSeries<VisitsGroup, String>(
                        borderWidth: 2,
                        name: t.analysis.visits,
                        enableTooltip: true,
                        splineType: SplineType.monotonic,
                        dataSource: c.visitsGrouped.value,
                        yValueMapper: (VisitsGroup visitsGroup, _) =>
                            visitsGroup.count,
                        xValueMapper: (VisitsGroup visitsGroup, _) =>
                            visitsGroup.date.toIso8601String().split("T")[0],
                        borderGradient: const LinearGradient(
                          colors: [
                            ThemeColors.primary,
                            Colors.cyan,
                          ],
                        ),
                        gradient: LinearGradient(
                          colors: [
                            ThemeColors.primary.withValues(alpha: 0.51),
                            Colors.cyan.withValues(alpha: 0.51),
                          ],
                        ),
                      )
                    ],
                  )),
            ],
          ),
        ),
        Obx(
          () => (c.clinicAnalytics.value == null ||
                  c.clinicAnalytics.value!.patientReachChannel == null ||
                  c.clinicAnalytics.value!.patientReachChannel!.isEmpty)
              ? const SizedBox()
              : const SizedBox(height: 13),
        ),
        const SizedBox(height: 13),
        Obx(() => c.clinicAnalytics.value == null
            ? const SizedBox()
            : Wrap(
                spacing: 13,
                runSpacing: 13,
                children: [
                  FractionallySizedBox(
                    widthFactor: context.desktopView ? .49 : 1,
                    child: Container(
                      padding: const EdgeInsets.all(13),
                      decoration: BoxDecoration(
                        color: ThemeColors.bg,
                        borderRadius: BorderRadius.circular(13),
                      ),
                      child: Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                t.analysis.patients,
                                style: context.textTheme.titleMedium,
                              ),
                            ],
                          ),
                          const SizedBox(height: 18),
                          if (c.patientsPie.value
                              .where((e) => e.count != 0)
                              .isEmpty)
                            SizedBox(
                              child: Center(
                                child: Text(
                                  t.analysis.noData,
                                ),
                              ),
                            )
                          else
                            SfCircularChart(
                              margin: EdgeInsets.zero,
                              legend: Legend(
                                isVisible: true,
                                overflowMode: LegendItemOverflowMode.wrap,
                                position: context.mobileView
                                    ? LegendPosition.bottom
                                    : LegendPosition.right,
                              ),
                              series: [
                                PieSeries(
                                  // ignore: invalid_use_of_protected_member
                                  opacity: .51,
                                  dataSource: c.patientsPie.value,
                                  pointColorMapper: (datum, index) {
                                    return chartColors[index];
                                  },
                                  xValueMapper: (data, _) => data.name,
                                  yValueMapper: (data, _) => data.count,
                                  dataLabelSettings: const DataLabelSettings(
                                    isVisible: true,
                                    labelPosition:
                                        ChartDataLabelPosition.outside,
                                    textStyle: TextStyle(
                                      fontSize: 16,
                                      color: ThemeColors.text,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                        ],
                      ),
                    ),
                  ),
                  FractionallySizedBox(
                    widthFactor: context.desktopView ? .49 : 1,
                    child: Container(
                      padding: const EdgeInsets.all(13),
                      decoration: BoxDecoration(
                        color: ThemeColors.bg,
                        borderRadius: BorderRadius.circular(13),
                      ),
                      child: Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                t.analysis.expenses,
                                style: context.textTheme.titleMedium,
                              ),
                            ],
                          ),
                          const SizedBox(height: 18),
                          if (c.clinicAnalytics.value!.expensesGrouped
                              .where((e) => e.sum != 0)
                              .isEmpty)
                            SizedBox(
                              child: Center(
                                child: Text(
                                  t.analysis.noData,
                                ),
                              ),
                            )
                          else
                            SfCircularChart(
                              legend: Legend(
                                isVisible: true,
                                overflowMode: LegendItemOverflowMode.wrap,
                                position: context.mobileView
                                    ? LegendPosition.bottom
                                    : LegendPosition.right,
                              ),
                              margin: EdgeInsets.zero,
                              series: [
                                PieSeries<ExpensesGroup, String>(
                                  opacity: .51,
                                  pointColorMapper: (datum, index) {
                                    return chartColors[index];
                                  },
                                  xValueMapper: (data, _) =>
                                      data.type.toLowerCase().tr,
                                  yValueMapper: (data, _) => data.sum.round(),
                                  dataSource:
                                      c.clinicAnalytics.value!.expensesGrouped,
                                  dataLabelSettings: const DataLabelSettings(
                                    isVisible: true,
                                    labelPosition:
                                        ChartDataLabelPosition.outside,
                                    textStyle: TextStyle(
                                      fontSize: 16,
                                      color: ThemeColors.text,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                        ],
                      ),
                    ),
                  ),
                ],
              )),
        const SizedBox(height: 13),
        Obx(() => (c.clinicAnalytics.value?.patientReachChannel
                    ?.any((e) => e.count == 0) ??
                true)
            ? const SizedBox()
            : Container(
                padding: const EdgeInsets.all(13),
                decoration: BoxDecoration(
                  color: ThemeColors.bg,
                  borderRadius: BorderRadius.circular(13),
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          t.analysis.patientsReachChannel,
                          style: context.textTheme.titleMedium,
                        ),
                      ],
                    ),
                    const SizedBox(height: 18),
                    SfCircularChart(
                      margin: EdgeInsets.zero,
                      legend: Legend(
                        isVisible: true,
                        overflowMode: LegendItemOverflowMode.wrap,
                        position: context.mobileView
                            ? LegendPosition.bottom
                            : LegendPosition.right,
                      ),
                      series: [
                        PieSeries(
                          opacity: .51,
                          dataSource:
                              c.clinicAnalytics.value!.patientReachChannel,
                          pointColorMapper: (datum, index) {
                            return chartColors[index];
                          },
                          yValueMapper: (data, _) => data.count,
                          xValueMapper: (data, _) {
                            final String name = data.reachChannel;
                            if (name.isEmpty) {
                              return t.analysis.unknown;
                            }
                            return name.capitalizeFirst!;
                          },
                          dataLabelSettings: const DataLabelSettings(
                            isVisible: true,
                            labelPosition: ChartDataLabelPosition.outside,
                            textStyle: TextStyle(
                              fontSize: 16,
                              color: ThemeColors.text,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              )),
      ],
    );
  }
}
