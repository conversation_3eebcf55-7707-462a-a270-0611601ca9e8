import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/core/utils/responsive.dart';
import 'package:core_app/layout/page_layout.dart';
import 'package:flutter/material.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:jiffy/jiffy.dart';
import 'package:get/get.dart';

class TimesheetPage extends StatelessWidget {
  const TimesheetPage({super.key});

  // Mock data for timesheet
  List<Map<String, dynamic>> getMockTimesheetData() {
    final now = DateTime.now();
    final List<Map<String, dynamic>> days = [];

    // Generate data for the last 7 days
    for (int i = 0; i < 7; i++) {
      final day = now.subtract(Duration(days: i));
      final users = [
        {'name': 'Dr. <PERSON>', 'timeRange': '8:00 to 12:00'},
        {'name': 'Dr. <PERSON>', 'timeRange': '9:00 to 14:00'},
        {'name': 'Dr. <PERSON>', 'timeRange': '13:00 to 18:00'},
        {'name': '<PERSON>', 'timeRange': '8:00 to 16:00'},
        {'name': 'Receptionist <PERSON>', 'timeRange': '8:30 to 17:30'},
      ];

      // Add fewer users on weekends
      if (day.weekday == DateTime.saturday || day.weekday == DateTime.sunday) {
        users.removeRange(2, 5);
      }

      days.add({
        'date': day,
        'users': users,
      });
    }

    return days;
  }

  @override
  Widget build(BuildContext context) {
    final timesheetData = getMockTimesheetData();
    // final isTablet = context.width >= 768 && context.width < 1024;
    // final isDesktop = context.width >= 1024;

    return PageLayout(
      title: 'Timesheet',
      children: [
        Container(
          width: double.infinity,
          clipBehavior: Clip.antiAlias,
          decoration: BoxDecoration(
            color: ThemeColors.bg,
            borderRadius: BorderRadius.circular(13),
          ),
          child: Column(
            children: [
              Container(
                width: double.infinity,
                padding:
                    const EdgeInsets.symmetric(horizontal: 28, vertical: 28),
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      width: .2,
                      color: context.theme.dividerTheme.color ?? Colors.grey,
                    ),
                  ),
                ),
                child: Wrap(
                  alignment: WrapAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Timesheet',
                      style: TextStyle(
                        fontSize: 24,
                        color: ThemeColors.text,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    if (context.mobileView) const SizedBox(height: 16),
                    Wrap(
                      spacing: 11,
                      runSpacing: 11,
                      children: [
                        // add new timesheet button
                        TextButton.icon(
                          icon: const Icon(Iconsax.add_copy),
                          label: const Text('New Timestamp'),
                          onPressed: () {},
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 24),
                child: Wrap(
                  spacing: 16,
                  runSpacing: 16,
                  // make the cards start from the left
                  alignment: WrapAlignment.start,
                  runAlignment: WrapAlignment.start,
                  crossAxisAlignment: WrapCrossAlignment.start,
                  children: timesheetData.map((dayData) {
                    final DateTime date = dayData['date'];
                    final List<Map<String, dynamic>> users = dayData['users'];

                    return TimesheetDayCard(
                      date: date,
                      users: users,
                      width: 0,
                    );
                  }).toList(),
                ),
              ),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ],
    );
  }
}

class TimesheetDayCard extends StatelessWidget {
  final DateTime date;
  final List<Map<String, dynamic>> users;
  final double width;

  const TimesheetDayCard({
    super.key,
    required this.date,
    required this.users,
    required this.width,
  });

  @override
  Widget build(BuildContext context) {
    final isToday = date.day == DateTime.now().day &&
        date.month == DateTime.now().month &&
        date.year == DateTime.now().year;

    return Container(
      width: 300,
      decoration: BoxDecoration(
        color: isToday
            ? ThemeColors.primaryLighter.withValues(alpha: 0.1)
            : Colors.white,
        borderRadius: BorderRadius.circular(13),
        border: Border.all(
            width: 0.5, color: const Color.fromARGB(92, 176, 185, 191)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Day header
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: isToday ? ThemeColors.primary : ThemeColors.bg,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  Jiffy.parseFromDateTime(date).EEEE,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: isToday ? Colors.white : ThemeColors.text,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  Jiffy.parseFromDateTime(date).yMMMd,
                  style: TextStyle(
                    fontSize: 12,
                    color: isToday
                        ? Colors.white.withValues(alpha: 0.9)
                        : ThemeColors.notion,
                  ),
                ),
              ],
            ),
          ),
          // User list
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: users.length,
            separatorBuilder: (context, index) => const Divider(height: 1),
            itemBuilder: (context, index) {
              final user = users[index];
              return ListTile(
                dense: true,
                title: Text(
                  user['name'],
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: ThemeColors.text,
                  ),
                ),
                subtitle: Text(
                  user['timeRange'],
                  style: const TextStyle(
                    fontSize: 12,
                    color: ThemeColors.notion,
                  ),
                ),
                // row of two icons delete and edit
                // trailing: const Icon(
                //   Icons.edit_outlined,
                //   size: 16,
                //   color: ThemeColors.notion,
                // ),
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      iconSize: 16,
                      visualDensity: VisualDensity.compact,
                      icon: const Icon(
                        Iconsax.trash_copy,
                        size: 16,
                        color: ThemeColors.notion,
                      ),
                      onPressed: () {},
                    ),
                    IconButton(
                      iconSize: 16,
                      visualDensity: VisualDensity.compact,
                      icon: const Icon(
                        Iconsax.edit_copy,
                        size: 16,
                        color: ThemeColors.notion,
                      ),
                      onPressed: () {},
                    ),
                  ],
                ),
              );
            },
          ),
          const SizedBox(height: 8),
        ],
      ),
    );
  }
}
