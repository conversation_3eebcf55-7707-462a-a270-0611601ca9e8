import 'package:core_app/components/dialogs/basic.dart';
import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/core/utils/i18n/translations.g.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ConfirmTimesheetDelete extends StatelessWidget {
  const ConfirmTimesheetDelete({super.key});

  @override
  Widget build(BuildContext context) {
    return BasicDialog(
      title: 'Delete Timesheet Entry',
      children: [
        const Text('Are you sure you want to delete this timesheet entry?'),
        const SizedBox(height: 20),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            TextButton(
              style: TextButton.styleFrom(
                backgroundColor: ThemeColors.error,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              onPressed: () {
                Get.back(result: false);
              },
              child: Text(
                t.cancel,
                style: const TextStyle(
                  color: Colors.white,
                ),
              ),
            ),
            const SizedBox(width: 16),
            TextButton(
              onPressed: () {
                Get.back(result: true);
              },
              child: Text(
                t.confirm,
                style: const TextStyle(
                  color: ThemeColors.text,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
