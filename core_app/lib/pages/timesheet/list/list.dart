import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/core/helpers/hex_convertor.dart';
import 'package:core_app/core/models/user.dart';
import 'package:core_app/core/utils/i18n/translations.g.dart';
import 'package:core_app/core/utils/responsive.dart';
import 'package:core_app/layout/page_layout.dart';
import 'package:core_app/pages/timesheet/form/form.dart';
import 'package:core_app/pages/timesheet/list/confirm_delete_dialog.dart';
import 'package:core_app/pages/timesheet/list/controller.dart';
import 'package:core_app/routes/navigation.dart';
import 'package:core_app/services/auth.dart';
import 'package:core_app/services/notify/messenger.dart';
import 'package:flutter/material.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:jiffy/jiffy.dart';
import 'package:get/get.dart';

class TimesheetPage extends StatelessWidget {
  const TimesheetPage({super.key});

  @override
  Widget build(BuildContext context) {
    // Initialize controller if not already done
    if (!Get.isRegistered<TimesheetController>()) {
      Get.put(TimesheetController());
    }
    final controller = TimesheetController.to;

    return PageLayout(
      title: 'Timesheet',
      children: [
        Container(
          width: double.infinity,
          clipBehavior: Clip.antiAlias,
          decoration: BoxDecoration(
            color: ThemeColors.bg,
            borderRadius: BorderRadius.circular(13),
          ),
          child: Column(
            children: [
              Container(
                width: double.infinity,
                padding: const EdgeInsets.only(
                  top: 28,
                  left: 28,
                  right: 28,
                  bottom: 11,
                ),
                // decoration: BoxDecoration(
                //   border: Border(
                //     bottom: BorderSide(
                //       width: .2,
                //       color: context.theme.dividerTheme.color ?? Colors.grey,
                //     ),
                //   ),
                // ),
                child: Wrap(
                  alignment: WrapAlignment.spaceBetween,
                  children: [
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        BackButton(
                          onPressed: () => Navigation.back(),
                        ),
                        const SizedBox(width: 8),
                        const Text(
                          'Timesheet',
                          style: TextStyle(
                            fontSize: 24,
                            color: ThemeColors.text,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                    if (context.mobileView) const SizedBox(height: 16),
                    Wrap(
                      spacing: 11,
                      runSpacing: 11,
                      children: [
                        TextButton.icon(
                          icon: const Icon(Iconsax.refresh_copy),
                          label: const Text('Refresh'),
                          onPressed: () => controller.fetchData(),
                        ),
                        const SizedBox(width: 8),
                        TextButton.icon(
                          icon: const Icon(Iconsax.add_copy),
                          label: const Text('New Timestamp'),
                          onPressed: () async {
                            final result =
                                await Get.dialog(const TimesheetFormDialog());
                            if (result == true) {
                              controller.fetchData();
                            }
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Obx(() {
                if (controller.isLoading.value) {
                  return const Center(
                    child: Padding(
                      padding: EdgeInsets.all(20.0),
                      child: CircularProgressIndicator(),
                    ),
                  );
                }

                if (controller.error.value != null) {
                  return Center(
                    child: Padding(
                      padding: const EdgeInsets.all(20.0),
                      child: Column(
                        children: [
                          Text(
                            'Error loading timesheet data\n${controller.error.value}',
                            style: TextStyle(color: Colors.red[700]),
                          ),
                          const SizedBox(height: 8),
                          TextButton.icon(
                            icon: const Icon(Iconsax.refresh_copy),
                            label: const Text('Retry'),
                            onPressed: () => controller.fetchData(),
                          ),
                        ],
                      ),
                    ),
                  );
                }

                if (controller.timesheetData.isEmpty) {
                  return const Center(
                    child: Padding(
                      padding: EdgeInsets.all(20.0),
                      child: Text('No timesheet entries found'),
                    ),
                  );
                }

                return ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: controller.timesheetData.length,
                    itemBuilder: (context, index) {
                      final dayData = controller.timesheetData[index];
                      final DateTime date = dayData['date'];
                      final List<Map<String, dynamic>> dentists =
                          dayData['dentists'];

                      // Skip days with no dentists or timestamps
                      if (dentists.isEmpty) {
                        return const SizedBox.shrink();
                      }

                      final bool isToday = date.day == DateTime.now().day &&
                          date.month == DateTime.now().month &&
                          date.year == DateTime.now().year;

                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Day header
                          Container(
                            width: double.infinity,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 28, vertical: 16),
                            margin: const EdgeInsets.only(top: 16),
                            decoration: BoxDecoration(
                              color: isToday
                                  ? ThemeColors.primary.withValues(alpha: 0.1)
                                  : ThemeColors.bg,
                              border: isToday
                                  ? null
                                  : Border(
                                      bottom: BorderSide(
                                        width: .5,
                                        color:
                                            context.theme.dividerTheme.color ??
                                                Colors.grey,
                                      ),
                                    ),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  Iconsax.calendar_copy,
                                  size: 20,
                                  color: isToday
                                      ? ThemeColors.primary
                                      : ThemeColors.notion,
                                ),
                                const SizedBox(width: 12),
                                Text(
                                  Jiffy.parseFromDateTime(date).EEEE,
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: isToday
                                        ? ThemeColors.primary
                                        : ThemeColors.text,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  Jiffy.parseFromDateTime(date).yMMMd,
                                  style: const TextStyle(
                                    fontSize: 14,
                                    color: ThemeColors.notion,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          // Dentist cards
                          Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 28, vertical: 16),
                            child: Wrap(
                              spacing: 16,
                              runSpacing: 16,
                              alignment: WrapAlignment.start,
                              children: dentists
                                  .map((dentist) => DentistTimesheetCard(
                                        dentistName: dentist['name'],
                                        timestamps: dentist['timestamps'],
                                        isToday: isToday,
                                      ))
                                  .toList(),
                            ),
                          ),
                        ],
                      );
                    });
              }),
            ],
          ),
        ),
      ],
    );
  }
}

class DentistTimesheetCard extends StatelessWidget {
  final String dentistName;
  final List<Map<String, dynamic>> timestamps;
  final bool isToday;

  const DentistTimesheetCard({
    super.key,
    required this.dentistName,
    required this.timestamps,
    this.isToday = false,
  });

  @override
  Widget build(BuildContext context) {
    final controller = TimesheetController.to;
    return FractionallySizedBox(
      widthFactor: context.width >= 768 ? 0.32 : 1,
      alignment: Alignment.centerLeft,
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(13),
          border: Border.all(
            width: 0.5,
            color: const Color.fromARGB(92, 176, 185, 191),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.03),
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Dentist header
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: isToday ? ThemeColors.primaryLighter : ThemeColors.bg,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Row(
                children: [
                  const Icon(
                    Iconsax.user_copy,
                    size: 18,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      dentistName,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: ThemeColors.text,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
            // Timestamps list
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: timestamps.length,
              separatorBuilder: (context, index) => const Divider(height: 1),
              itemBuilder: (context, index) {
                final timestamp = timestamps[index];
                return ListTile(
                  dense: true,
                  title: Text(
                    timestamp['time'],
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: ThemeColors.text,
                    ),
                  ),
                  subtitle: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        width: 14,
                        height: 1.5,
                        margin: const EdgeInsets.only(right: 6),
                        decoration: BoxDecoration(
                          color: HexConvertor.fromHex(timestamp['branchColor']),
                        ),
                      ),
                      Expanded(
                        child: Text(
                          'Branch: ${timestamp['branch']}',
                          style: const TextStyle(
                            fontSize: 12,
                            color: ThemeColors.notion,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                  trailing: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (AuthService.to.user.value!.role != UserRole.master)
                        IconButton(
                          iconSize: 16,
                          visualDensity: VisualDensity.compact,
                          icon: const Icon(
                            Iconsax.trash_copy,
                            size: 16,
                            color: ThemeColors.notion,
                          ),
                          onPressed: () async {
                            if (AuthService.to.isSubEnded) {
                              NotifyService.notice(title: t.subEnded);
                              return;
                            }
                            if (AuthService.to.user.value!.role !=
                                UserRole.master) {
                              return;
                            }
                            // Get the entry ID from the timestamp
                            final entryId = timestamp['entryId'];
                            if (entryId != null) {
                              // Show confirmation dialog
                              final confirmed = await Get.dialog(
                                  const ConfirmTimesheetDelete());
                              if (confirmed == true) {
                                // Delete the entry
                                await controller.deleteEntry(entryId);
                                NotifyService.success(
                                    'Timesheet entry deleted successfully');
                              }
                            } else {
                              NotifyService.notice(title: 'Entry ID not found');
                            }
                          },
                        ),
                      IconButton(
                        iconSize: 16,
                        visualDensity: VisualDensity.compact,
                        icon: const Icon(
                          Iconsax.edit_copy,
                          size: 16,
                          color: ThemeColors.notion,
                        ),
                        onPressed: () async {
                          // Get the entry ID from the timestamp
                          final entryId = timestamp['entryId'];
                          if (entryId != null) {
                            // Find the entry in the controller
                            final entry = controller.findEntryById(entryId);
                            if (entry != null) {
                              // Open the form dialog with the entry
                              final result = await Get.dialog(
                                TimesheetFormDialog(entry: entry),
                              );
                              if (result == true) {
                                // Refresh the data if the entry was updated
                                controller.fetchData();
                              }
                            } else {
                              NotifyService.notice(title: 'Entry not found');
                            }
                          } else {
                            NotifyService.notice(title: 'Entry ID not found');
                          }
                        },
                      ),
                    ],
                  ),
                );
              },
            ),
            const SizedBox(height: 8),
          ],
        ),
      ),
    );
  }
}
