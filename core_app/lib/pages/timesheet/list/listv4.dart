import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/core/utils/responsive.dart';
import 'package:core_app/layout/page_layout.dart';
import 'package:flutter/material.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:jiffy/jiffy.dart';
import 'package:get/get.dart';

class TimesheetPage extends StatelessWidget {
  const TimesheetPage({super.key});

  // Mock data for timesheet
  List<Map<String, dynamic>> getMockTimesheetData() {
    final now = DateTime.now();
    final List<Map<String, dynamic>> days = [];

    // Generate data for the last 7 days
    for (int i = 0; i < 7; i++) {
      final day = now.subtract(Duration(days: i));

      // Create dentist schedules with start and end times
      final dentistSchedules = [
        {
          'name': 'Dr. <PERSON>',
          'color': Colors.blue,
          'schedules': [
            {
              'startHour': 8,
              'startMinute': 0,
              'endHour': 10,
              'endMinute': 0,
              'patient': '<PERSON>'
            },
            {
              'startHour': 10,
              'startMinute': 30,
              'endHour': 11,
              'endMinute': 45,
              'patient': '<PERSON>'
            },
            {
              'startHour': 13,
              'startMinute': 0,
              'endHour': 14,
              'endMinute': 30,
              'patient': '<PERSON> <PERSON>'
            },
          ]
        },
        {
          'name': 'Dr. Sarah <PERSON>',
          'color': Colors.green,
          'schedules': [
            {
              'startHour': 9,
              'startMinute': 0,
              'endHour': 10,
              'endMinute': 15,
              'patient': 'David Miller'
            },
            {
              'startHour': 11,
              'startMinute': 0,
              'endHour': 12,
              'endMinute': 30,
              'patient': 'Emma Wilson'
            },
            {
              'startHour': 14,
              'startMinute': 0,
              'endHour': 15,
              'endMinute': 30,
              'patient': 'Frank Thomas'
            },
          ]
        },
        {
          'name': 'Dr. Michael Brown',
          'color': Colors.orange,
          'schedules': [
            {
              'startHour': 8,
              'startMinute': 30,
              'endHour': 9,
              'endMinute': 45,
              'patient': 'Grace Martinez'
            },
            {
              'startHour': 10,
              'startMinute': 15,
              'endHour': 11,
              'endMinute': 30,
              'patient': 'Henry Anderson'
            },
            {
              'startHour': 13,
              'startMinute': 30,
              'endHour': 15,
              'endMinute': 0,
              'patient': 'Isabella Taylor'
            },
          ]
        },
        {
          'name': 'Dr. Emily Wilson',
          'color': Colors.purple,
          'schedules': [
            {
              'startHour': 9,
              'startMinute': 30,
              'endHour': 11,
              'endMinute': 0,
              'patient': 'James Taylor'
            },
            {
              'startHour': 12,
              'startMinute': 0,
              'endHour': 13,
              'endMinute': 15,
              'patient': 'Karen White'
            },
            {
              'startHour': 15,
              'startMinute': 30,
              'endHour': 17,
              'endMinute': 0,
              'patient': 'Lucas Martin'
            },
          ]
        },
      ];

      // Add fewer dentists on weekends
      if (day.weekday == DateTime.saturday || day.weekday == DateTime.sunday) {
        dentistSchedules.removeRange(2, 4);
      }

      days.add({
        'date': day,
        'dentistSchedules': dentistSchedules,
      });
    }

    return days;
  }

  @override
  Widget build(BuildContext context) {
    final timesheetData = getMockTimesheetData();

    return PageLayout(
      title: 'Timesheet',
      children: [
        Container(
          width: double.infinity,
          clipBehavior: Clip.antiAlias,
          decoration: BoxDecoration(
            color: ThemeColors.bg,
            borderRadius: BorderRadius.circular(13),
          ),
          child: Column(
            children: [
              Container(
                width: double.infinity,
                padding:
                    const EdgeInsets.symmetric(horizontal: 28, vertical: 28),
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      width: .2,
                      color: context.theme.dividerTheme.color ?? Colors.grey,
                    ),
                  ),
                ),
                child: Wrap(
                  alignment: WrapAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Timesheet',
                      style: TextStyle(
                        fontSize: 24,
                        color: ThemeColors.text,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    if (context.mobileView) const SizedBox(height: 16),
                    Wrap(
                      spacing: 11,
                      runSpacing: 11,
                      children: [
                        TextButton.icon(
                          icon: const Icon(Iconsax.add_copy),
                          label: const Text('New Timestamp'),
                          onPressed: () {},
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: timesheetData.length,
                itemBuilder: (context, index) {
                  final dayData = timesheetData[index];
                  final DateTime date = dayData['date'];
                  final List<Map<String, dynamic>> dentistSchedules =
                      dayData['dentistSchedules'];
                  final bool isToday = date.day == DateTime.now().day &&
                      date.month == DateTime.now().month &&
                      date.year == DateTime.now().year;

                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Day header
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 28, vertical: 16),
                        margin: const EdgeInsets.only(top: 16),
                        decoration: BoxDecoration(
                          color: isToday
                              ? ThemeColors.primary.withValues(alpha: 0.1)
                              : ThemeColors.bg,
                          border: Border(
                            bottom: BorderSide(
                              width: .5,
                              color: context.theme.dividerTheme.color ??
                                  Colors.grey,
                            ),
                          ),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Iconsax.calendar_copy,
                              size: 20,
                              color: isToday
                                  ? ThemeColors.primary
                                  : ThemeColors.notion,
                            ),
                            const SizedBox(width: 12),
                            Text(
                              Jiffy.parseFromDateTime(date).EEEE,
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: isToday
                                    ? ThemeColors.primary
                                    : ThemeColors.text,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              Jiffy.parseFromDateTime(date).yMMMd,
                              style: const TextStyle(
                                fontSize: 14,
                                color: ThemeColors.notion,
                              ),
                            ),
                          ],
                        ),
                      ),
                      // Timeline view
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 16),
                        child: TimesheetTimeline(
                          dentistSchedules: dentistSchedules,
                          isToday: isToday,
                        ),
                      ),
                    ],
                  );
                },
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class TimesheetTimeline extends StatelessWidget {
  final List<Map<String, dynamic>> dentistSchedules;
  final bool isToday;

  const TimesheetTimeline({
    super.key,
    required this.dentistSchedules,
    this.isToday = false,
  });

  @override
  Widget build(BuildContext context) {
    // Define the working hours (8 AM to 6 PM)
    const startHour = 8;
    const endHour = 18;
    const totalHours = endHour - startHour;
    final isMobile = context.width < 768;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Time indicators
        Container(
          height: 30,
          margin: const EdgeInsets.only(left: 60),
          child: Row(
            children: List.generate(totalHours + 1, (index) {
              final hour = startHour + index;
              final isFullHour = index % 1 == 0;
              return Expanded(
                child: Container(
                  alignment: Alignment.center,
                  child: Text(
                    hour > 12
                        ? '${hour - 12}:00 PM'
                        : '$hour:00 ${hour == 12 ? "PM" : "AM"}',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight:
                          isFullHour ? FontWeight.bold : FontWeight.normal,
                      color: ThemeColors.notion,
                    ),
                  ),
                ),
              );
            }),
          ),
        ),
        // Timeline grid with hour markers
        Container(
          height: 30,
          margin: const EdgeInsets.only(left: 60),
          decoration: BoxDecoration(
            border: Border(
              top: BorderSide(
                width: 1,
                color: Colors.grey.shade300,
              ),
              bottom: BorderSide(
                width: 1,
                color: Colors.grey.shade300,
              ),
            ),
          ),
          child: Row(
            children: List.generate(totalHours, (index) {
              return Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    border: Border(
                      right: BorderSide(
                        width: 1,
                        color: Colors.grey.shade300,
                      ),
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(child: Container()),
                      Container(
                        width: 1,
                        color: Colors.grey.shade300,
                      ),
                      Expanded(child: Container()),
                    ],
                  ),
                ),
              );
            }),
          ),
        ),
        // Dentist schedules
        ...dentistSchedules.map((dentist) {
          final String name = dentist['name'];
          final Color color = dentist['color'];
          final List<Map<String, dynamic>> schedules = dentist['schedules'];

          return Container(
            margin: const EdgeInsets.only(bottom: 12),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Dentist name
                Container(
                  width: 60,
                  padding: const EdgeInsets.only(right: 8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Container(
                            width: 12,
                            height: 12,
                            margin: const EdgeInsets.only(right: 4),
                            decoration: BoxDecoration(
                              color: color,
                              shape: BoxShape.circle,
                            ),
                          ),
                          Expanded(
                            child: Text(
                              name
                                  .split(' ')
                                  .last, // Show only last name on mobile
                              style: const TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                color: ThemeColors.text,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                      if (!isMobile)
                        Padding(
                          padding: const EdgeInsets.only(left: 16),
                          child: Text(
                            name.split(' ').first,
                            style: const TextStyle(
                              fontSize: 10,
                              color: ThemeColors.notion,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                    ],
                  ),
                ),
                // Timeline for this dentist
                Expanded(
                  child: Stack(
                    children: [
                      // Background grid
                      Container(
                        height: 50,
                        decoration: BoxDecoration(
                          border: Border(
                            bottom: BorderSide(
                              width: 1,
                              color: Colors.grey.shade300,
                            ),
                          ),
                        ),
                        child: Row(
                          children: List.generate(totalHours, (index) {
                            return Expanded(
                              child: Container(
                                decoration: BoxDecoration(
                                  border: Border(
                                    right: BorderSide(
                                      width: 1,
                                      color: Colors.grey.shade300,
                                    ),
                                  ),
                                ),
                              ),
                            );
                          }),
                        ),
                      ),
                      // Schedule blocks
                      ...schedules.map((schedule) {
                        final startHourVal = schedule['startHour'];
                        final startMinuteVal = schedule['startMinute'];
                        final endHourVal = schedule['endHour'];
                        final endMinuteVal = schedule['endMinute'];
                        final patient = schedule['patient'];

                        // Calculate position and width
                        final startPosition = (startHourVal - startHour) +
                            (startMinuteVal / 60.0);
                        final endPosition =
                            (endHourVal - startHour) + (endMinuteVal / 60.0);
                        final blockWidth =
                            (endPosition - startPosition) / totalHours;
                        final leftPosition = startPosition / totalHours;

                        return Positioned(
                          left: leftPosition * 100,
                          width: blockWidth * 100,
                          top: 5,
                          height: 40,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: color.withValues(alpha: 0.7),
                              borderRadius: BorderRadius.circular(4),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.1),
                                  blurRadius: 2,
                                  offset: const Offset(0, 1),
                                ),
                              ],
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  '${startHourVal.toString().padLeft(2, '0')}:${startMinuteVal.toString().padLeft(2, '0')} - ${endHourVal.toString().padLeft(2, '0')}:${endMinuteVal.toString().padLeft(2, '0')}',
                                  style: TextStyle(
                                    fontSize: isMobile ? 9 : 10,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                                if (blockWidth >
                                    0.1) // Only show patient name if block is wide enough
                                  Text(
                                    patient,
                                    style: TextStyle(
                                      fontSize: isMobile ? 8 : 9,
                                      color: Colors.white,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                              ],
                            ),
                          ),
                        );
                      }).toList(),
                    ],
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      ],
    );
  }
}
