import 'package:core_app/core/api/branches.dart';
import 'package:core_app/core/api/timesheet.dart';
import 'package:core_app/core/api/users.dart';
import 'package:core_app/core/models/branch.dart';
import 'package:core_app/core/models/timesheet.dart';
import 'package:core_app/core/models/timesheet_summary.dart';
import 'package:core_app/core/models/user.dart';
import 'package:get/get.dart';
import 'package:jiffy/jiffy.dart';

class TimesheetController extends GetxController {
  static TimesheetController get to => Get.find();

  final entries = <TimesheetEntryModel>[].obs;
  final users = <UserModel>[].obs;
  final branches = <BranchModel>[].obs;
  final isLoading = false.obs;
  final error = Rx<String?>(null);

  // Processed data for UI
  final timesheetData = <Map<String, dynamic>>[].obs;

  @override
  void onInit() {
    fetchData();
    super.onInit();
  }

  Future<void> fetchData() async {
    isLoading.value = true;
    error.value = null;

    try {
      // Get the last 7 days
      final now = DateTime.now();
      final startDate = now.subtract(const Duration(days: 6));
      final endDate = now;

      // Fetch all required data
      final entriesFuture = TimesheetAPI.list(
        startDate: startDate,
        endDate: endDate,
      );

      final dentistsFuture = UsersAPI.list();
      final branchesFuture = BranchesAPI.list();

      final results = await Future.wait([
        entriesFuture,
        dentistsFuture,
        branchesFuture,
      ]);

      entries.value = (results[0] as TimesheetSummaryModel).entries;
      users.value = (results[1] as List<UserModel>).toList();
      branches.value = results[2] as List<BranchModel>;

      // Process data for UI
      processTimesheetData();
    } catch (e) {
      error.value = e.toString();
    } finally {
      isLoading.value = false;
    }
  }

  void processTimesheetData() {
    final now = DateTime.now();
    final List<Map<String, dynamic>> days = [];

    // Generate data for the last 7 days
    for (int i = 0; i < 7; i++) {
      final day = now.subtract(Duration(days: i));
      final dayStart = DateTime(day.year, day.month, day.day);
      final dayEnd = DateTime(day.year, day.month, day.day, 23, 59, 59);

      // Filter entries for this day
      final dayEntries = entries
          .where((entry) =>
              entry.startTime.isAfter(dayStart) &&
              entry.startTime.isBefore(dayEnd))
          .toList();

      // Group by dentist
      final Map<String, List<TimesheetEntryModel>> dentistEntries = {};

      for (final entry in dayEntries) {
        if (!dentistEntries.containsKey(entry.userId)) {
          dentistEntries[entry.userId] = [];
        }
        dentistEntries[entry.userId]!.add(entry);
      }

      // Create dentist data with timestamps
      final List<Map<String, dynamic>> dentistsData = [];

      for (final dentistId in dentistEntries.keys) {
        final dentist = users.firstWhereOrNull((d) => d.id == dentistId);
        if (dentist == null) continue;

        final timestamps = <Map<String, dynamic>>[];

        for (final entry in dentistEntries[dentistId]!) {
          final branch =
              branches.firstWhereOrNull((b) => b.id == entry.branchId);
          if (branch == null) continue;

          final startTime = Jiffy.parseFromDateTime(entry.startTime.toLocal())
              .format(pattern: 'h:mm a');
          final endTime = entry.endTime != null
              ? Jiffy.parseFromDateTime(entry.endTime!.toLocal())
                  .format(pattern: 'h:mm a')
              : '';

          timestamps.add({
            'time': '$startTime - $endTime',
            'branch': branch.name,
            'branchColor': branch.color,
            'entryId': entry.id,
          });
        }

        if (timestamps.isNotEmpty) {
          dentistsData.add({
            'name': dentist.name,
            'timestamps': timestamps,
          });
        }
      }

      days.add({
        'date': day,
        'dentists': dentistsData,
      });
    }

    timesheetData.value = days;
  }

  TimesheetEntryModel? findEntryById(String id) {
    return entries.firstWhereOrNull((entry) => entry.id == id);
  }

  Future<void> deleteEntry(String id) async {
    try {
      await TimesheetAPI.delete(id);
      // Remove the entry from the list
      entries.removeWhere((entry) => entry.id == id);
      // Refresh the UI
      processTimesheetData();
    } catch (e) {
      error.value = e.toString();
    }
  }
}
