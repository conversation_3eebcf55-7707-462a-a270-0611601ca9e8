import 'package:core_app/components/dialogs/basic.dart';
import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/core/models/timesheet.dart';
import 'package:core_app/pages/timesheet/form/controller.dart';
import 'package:core_app/services/notify/messenger.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:jiffy/jiffy.dart';

class TimesheetFormDialog extends StatelessWidget {
  const TimesheetFormDialog({
    super.key,
    this.entry,
  });

  final TimesheetEntryModel? entry;

  @override
  Widget build(BuildContext context) {
    final c = Get.put(TimesheetFormController(entry));
    final bool isEditing = entry != null;

    return BasicDialog(
      title: isEditing ? 'Edit Timestamp' : 'New Timestamp',
      children: [
        Obx(() {
          if (c.isLoading.value) {
            return const Center(
              child: Padding(
                padding: EdgeInsets.all(20.0),
                child: CircularProgressIndicator(),
              ),
            );
          }

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Dentist selection
              const Text(
                'User',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: ThemeColors.text,
                ),
              ),
              const SizedBox(height: 8),
              DropdownButtonFormField<String>(
                value: c.selectedDentist.value?.id,
                decoration: const InputDecoration(
                  label: Text('Select User'),
                  border: OutlineInputBorder(),
                  // contentPadding:
                  //     EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                  // alignLabelWithHint: true,
                ),
                // isExpanded: true,
                alignment: AlignmentDirectional.centerStart,
                items: c.users.map((dentist) {
                  return DropdownMenuItem<String>(
                    value: dentist.id,
                    child: Text(dentist.name),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    c.selectedDentist.value =
                        c.users.firstWhere((d) => d.id == value);
                  }
                },
              ),
              const SizedBox(height: 16),

              // Branch selection
              const Text(
                'Branch',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: ThemeColors.text,
                ),
              ),
              const SizedBox(height: 8),
              DropdownButtonFormField<String>(
                value: c.selectedBranch.value?.id,
                decoration: const InputDecoration(
                  label: Text('Select Branch'),
                  border: OutlineInputBorder(),
                ),
                items: c.branches.map((branch) {
                  return DropdownMenuItem<String>(
                    value: branch.id,
                    child: Row(
                      children: [
                        Container(
                          width: 24,
                          height: 3,
                          margin: const EdgeInsets.only(right: 8),
                          color: branch.getColor,
                        ),
                        Text(branch.name),
                      ],
                    ),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    c.selectedBranch.value =
                        c.branches.firstWhere((b) => b.id == value);
                  }
                },
              ),
              const SizedBox(height: 16),

              // Start date and time
              const Text(
                'Start Time',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: ThemeColors.text,
                ),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: InkWell(
                      onTap: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate: c.startDate.value,
                          firstDate:
                              DateTime.now().subtract(const Duration(days: 30)),
                          lastDate:
                              DateTime.now().add(const Duration(days: 30)),
                        );
                        if (date != null) {
                          c.startDate.value = date;
                        }
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 16),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Obx(() => Text(
                              Jiffy.parseFromDateTime(c.startDate.value).yMMMd,
                              style: const TextStyle(fontSize: 16),
                            )),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: InkWell(
                      onTap: () async {
                        final time = await showTimePicker(
                          context: context,
                          initialTime: c.startTime.value,
                        );
                        if (time != null) {
                          c.startTime.value = time;
                        }
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 16),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Obx(() => Text(
                              '${c.startTime.value.hour.toString().padLeft(2, '0')}:${c.startTime.value.minute.toString().padLeft(2, '0')}',
                              style: const TextStyle(fontSize: 16),
                            )),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // End time checkbox
              Row(
                children: [
                  Obx(() => Checkbox(
                        value: c.hasEndTime.value,
                        onChanged: (value) {
                          c.hasEndTime.value = value ?? false;
                          if (value == true && c.endDate.value == null) {
                            c.endDate.value = DateTime.now();
                            c.endTime.value = TimeOfDay.now();
                          }
                        },
                      )),
                  const Text('Add End Time'),
                ],
              ),

              // End date and time (conditional)
              Obx(() {
                if (!c.hasEndTime.value) {
                  return const SizedBox.shrink();
                }

                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'End Time',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: ThemeColors.text,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Expanded(
                          child: InkWell(
                            onTap: () async {
                              final date = await showDatePicker(
                                context: context,
                                initialDate: c.endDate.value ?? DateTime.now(),
                                firstDate: DateTime.now()
                                    .subtract(const Duration(days: 30)),
                                lastDate: DateTime.now()
                                    .add(const Duration(days: 30)),
                              );
                              if (date != null) {
                                c.endDate.value = date;
                              }
                            },
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 16),
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                c.endDate.value != null
                                    ? Jiffy.parseFromDateTime(c.endDate.value!)
                                        .yMMMd
                                    : 'Select Date',
                                style: const TextStyle(fontSize: 16),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: InkWell(
                            onTap: () async {
                              final time = await showTimePicker(
                                context: context,
                                initialTime: c.endTime.value ?? TimeOfDay.now(),
                              );
                              if (time != null) {
                                c.endTime.value = time;
                              }
                            },
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 16),
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                c.endTime.value != null
                                    ? '${c.endTime.value!.hour.toString().padLeft(2, '0')}:${c.endTime.value!.minute.toString().padLeft(2, '0')}'
                                    : 'Select Time',
                                style: const TextStyle(fontSize: 16),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                );
              }),

              const SizedBox(height: 24),

              // Save button
              Align(
                alignment: Alignment.centerRight,
                child: ElevatedButton(
                  onPressed: () {
                    if (c.selectedDentist.value == null) {
                      NotifyService.notice(title: 'Please select a dentist');
                      return;
                    }

                    if (c.selectedBranch.value == null) {
                      NotifyService.notice(title: 'Please select a branch');
                      return;
                    }

                    if (c.hasEndTime.value) {
                      if (c.endDate.value == null) {
                        NotifyService.notice(
                            title: 'Please select an end date');
                        return;
                      }

                      if (c.endTime.value == null) {
                        NotifyService.notice(
                            title: 'Please select an end time');
                        return;
                      }

                      // Check if end time is after start time
                      final startDateTime = DateTime(
                        c.startDate.value.year,
                        c.startDate.value.month,
                        c.startDate.value.day,
                        c.startTime.value.hour,
                        c.startTime.value.minute,
                      );
                      final endDateTime = DateTime(
                        c.endDate.value!.year,
                        c.endDate.value!.month,
                        c.endDate.value!.day,
                        c.endTime.value!.hour,
                        c.endTime.value!.minute,
                      );

                      if (endDateTime.isBefore(startDateTime)) {
                        NotifyService.notice(
                            title: 'End time must be after start time');
                        return;
                      }
                    }

                    c.save();
                  },
                  child: const Text('Save'),
                ),
              ),
            ],
          );
        }),
      ],
    );
  }
}
