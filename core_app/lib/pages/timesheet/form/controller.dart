import 'package:core_app/core/api/branches.dart';
import 'package:core_app/core/api/timesheet.dart';
import 'package:core_app/core/api/users.dart';
import 'package:core_app/core/helpers/api_handler.dart';
import 'package:core_app/core/models/branch.dart';
import 'package:core_app/core/models/timesheet.dart';
import 'package:core_app/core/models/user.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class TimesheetFormController extends GetxController {
  static TimesheetFormController get to => Get.find();

  final TimesheetEntryModel? entry;

  TimesheetFormController(this.entry);

  final isLoading = false.obs;
  final users = <UserModel>[].obs;
  final branches = <BranchModel>[].obs;

  final selectedDentist = Rx<UserModel?>(null);
  final selectedBranch = Rx<BranchModel?>(null);

  final startDate = Rx<DateTime>(DateTime.now());
  final startTime = Rx<TimeOfDay>(TimeOfDay.now());

  final endDate = Rx<DateTime?>(null);
  final endTime = Rx<TimeOfDay?>(null);

  final hasEndTime = false.obs;

  @override
  void onInit() {
    super.onInit();
    fetchData();

    if (entry != null) {
      // If editing an existing entry, populate the form
      startDate.value = entry!.startTime;
      startTime.value = TimeOfDay(
        hour: entry!.startTime.hour,
        minute: entry!.startTime.minute,
      );

      if (entry!.endTime != null) {
        hasEndTime.value = true;
        endDate.value = entry!.endTime;
        endTime.value = TimeOfDay(
          hour: entry!.endTime!.hour,
          minute: entry!.endTime!.minute,
        );
      }
    }
  }

  Future<void> fetchData() async {
    isLoading.value = true;

    try {
      final dentistsFuture = UsersAPI.list();
      final branchesFuture = BranchesAPI.list();

      final results = await Future.wait([
        dentistsFuture,
        branchesFuture,
      ]);

      users.value = (results[0] as List<UserModel>).toList();
      branches.value = results[1] as List<BranchModel>;

      // Set initial selections if editing
      if (entry != null) {
        selectedDentist.value =
            users.firstWhereOrNull((d) => d.id == entry!.userId);
        selectedBranch.value =
            branches.firstWhereOrNull((b) => b.id == entry!.branchId);
      }
    } catch (e) {
      // Handle error
    } finally {
      isLoading.value = false;
    }
  }

  DateTime _combineDateTime(DateTime date, TimeOfDay time) {
    return DateTime(
      date.year,
      date.month,
      date.day,
      time.hour,
      time.minute,
    );
  }

  Future<void> save() async {
    if (selectedDentist.value == null || selectedBranch.value == null) {
      return;
    }

    final startDateTime = _combineDateTime(startDate.value, startTime.value);
    DateTime? endDateTime;

    if (hasEndTime.value && endDate.value != null && endTime.value != null) {
      endDateTime = _combineDateTime(endDate.value!, endTime.value!);
    }

    tryAPI(() async {
      if (entry == null) {
        // Create new entry
        await TimesheetAPI.create(
          userId: selectedDentist.value!.id,
          branchId: selectedBranch.value!.id,
          startTime: startDateTime,
          endTime: endDateTime,
        );
      } else {
        // Update existing entry
        await TimesheetAPI.update(
          entry!.id,
          startTime: startDateTime,
          endTime: endDateTime,
        );
      }

      Get.back(result: true);
    });
  }
}
