import 'package:core_app/components/tables/table.dart';
import 'package:core_app/core/api/inventories.dart';
import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/core/models/inventory.dart';
import 'package:core_app/layout/page_layout.dart';
import 'package:core_app/routes/navigation.dart';
import 'package:flutter/material.dart';
import 'package:jiffy/jiffy.dart';

class TransactionsListPage extends StatefulWidget {
  const TransactionsListPage(
    this.inventoryID, {
    super.key,
    required this.unit,
    required this.inventoryName,
  });

  final String inventoryID;
  final String unit;
  final String inventoryName;

  @override
  State<TransactionsListPage> createState() => _TransactionsListPageState();
}

class _TransactionsListPageState extends State<TransactionsListPage> {
  List<InventoryItemTransactionModel> transactions = [];

  loadData() async {
    transactions = await InventoriesAPI.listTransaction(widget.inventoryID);
    setState(() {});
  }

  @override
  void initState() {
    loadData();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return PageLayout(
      children: [
        Container(
          padding: const EdgeInsets.all(7),
          decoration: BoxDecoration(
            color: ThemeColors.bg,
            borderRadius: BorderRadius.circular(13),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const BackButton(
                    color: ThemeColors.text,
                    onPressed: Navigation.back,
                  ),
                  Text(
                    'Inventory: ${widget.inventoryName}',
                    style: const TextStyle(
                      color: ThemeColors.text,
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        const SizedBox(height: 10),
        TableComponent<InventoryItemTransactionModel>(
          title: 'Transctions List',
          data: transactions,
          columns: [
            TableColumn(
              minWidth: 100,
              title: 'Quantity Before',
              builder: (data) {
                return TableColumn.stringBuilder(
                    '${data.quantityBefore} ${widget.unit}');
              },
            ),
            TableColumn(
              minWidth: 100,
              title: 'Quantity After',
              builder: (data) {
                final quantityAfter =
                    data.quantityBefore + data.transactionQuantity;
                return TableColumn.stringBuilder(
                    '$quantityAfter ${widget.unit}');
              },
            ),
            TableColumn(
              minWidth: 150,
              title: "Action By",
              builder: (data) {
                return TableColumn.stringBuilder(
                    data.createdBy?.name ?? 'Unknown');
              },
            ),
            TableColumn(
              minWidth: 200,
              title: "Created At",
              builder: (data) {
                final string = Jiffy.parseFromDateTime(data.createdAt).yMMMEdjm;
                return TableColumn.stringBuilder(string);
              },
            ),
          ],
        ),
      ],
    );
  }
}
