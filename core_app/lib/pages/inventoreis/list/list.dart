import 'package:core_app/components/buttons/text_icon.dart';
import 'package:core_app/components/tables/table.dart';
import 'package:core_app/core/models/inventory.dart';
import 'package:core_app/core/utils/i18n/translations.g.dart';
import 'package:core_app/layout/page_layout.dart';
import 'package:core_app/pages/inventoreis/form/form.dart';
import 'package:core_app/pages/inventoreis/transaction/create.dart';
import 'package:core_app/routes/navigation.dart';

import 'package:core_app/routes/route_paths.dart';
import 'package:core_app/services/auth.dart';
import 'package:core_app/services/notify/messenger.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'controller.dart';

class InventoriesListPage extends StatelessWidget {
  const InventoriesListPage({super.key});

  @override
  Widget build(BuildContext context) {
    final c = Get.find<InventoriesListController>();
    return Obx(() => PageLayout(
          children: [
            TableComponent<InventoryItemModel>(
              title: t.inventoryView.list,
              data: c.inventories.value,
              getColor: (index) => index.quantity < index.warningQuantity
                  ? Colors.yellow.shade100
                  : Colors.transparent,
              onRowTap: (data) {
                Navigation.to(Routes.InventoryTransactions, {
                  "inventoryName": data.name,
                  "inventoryID": data.id,
                  "unit": data.unit,
                });
              },
              actions: [
                XTextIconButton(
                  title: t.inventoryView.add,
                  icon: Iconsax.add_copy,
                  onPressed: () async {
                    if (AuthService.to.isSubEnded) {
                      NotifyService.notice(title: t.subEnded);
                      return;
                    }
                    InventoryItemModel? p =
                        await Get.dialog(const InventoryForm());
                    if (p != null) {
                      c.inventories.add(p);
                    }
                  },
                ),
              ],
              columns: [
                TableColumn(
                  flex: 3,
                  title: t.inventory.name,
                  // minWidth: 300,
                  builder: (data) {
                    return TableColumn.stringBuilder(data.name, clip: false);
                  },
                ),
                TableColumn(
                  flex: 2,
                  title: t.inventory.currentQuantity,
                  minWidth: 150,
                  builder: (data) {
                    return TableColumn.stringBuilder(
                        '${data.quantity} ${data.unit}');
                  },
                ),
                TableColumn(
                  title: '',
                  minWidth: 100,
                  builder: (data) {
                    return Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        IconButton(
                          iconSize: 18,
                          icon: const Icon(
                            Iconsax.box_add_copy,
                            size: 18,
                          ),
                          onPressed: () async {
                            if (AuthService.to.isSubEnded) {
                              NotifyService.notice(title: t.subEnded);
                              return;
                            }
                            InventoryItemTransactionModel? result =
                                await Get.dialog(TransactionCreate(data.id));
                            if (result == null) return;
                            c.fetch();
                          },
                        ),
                        IconButton(
                          iconSize: 18,
                          icon: const Icon(
                            Iconsax.edit,
                            size: 18,
                          ),
                          onPressed: () async {
                            if (AuthService.to.isSubEnded) {
                              NotifyService.notice(title: t.subEnded);
                              return;
                            }
                            InventoryItemModel? p = await Get.dialog(
                                InventoryForm(inventory: data));
                            if (p != null) {
                              c.fetch();
                            }
                          },
                        ),
                      ],
                    );
                  },
                ),
              ],
            ),
          ],
        ));
  }
}
