import 'package:core_app/components/dialogs/basic.dart';
import 'package:core_app/core/api/labs.dart';
import 'package:core_app/core/helpers/api_handler.dart';
import 'package:core_app/core/models/lab.dart';
import 'package:core_app/core/utils/i18n/translations.g.dart';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

class LabFormDialog extends StatelessWidget {
  const LabFormDialog(this.repo, {super.key});

  final LabModel? repo;

  @override
  Widget build(BuildContext context) {
    final name = (repo?.name ?? '').obs;
    final phoneNumber = (repo?.phoneNumber ?? '').obs;
    return BasicDialog(
      title: repo == null ? t.labView.add : t.labView.edit,
      children: [
        TextFormField(
          onChanged: name,
          initialValue: name.value,
          decoration: InputDecoration(
            labelText: t.lab.name,
          ),
        ),
        const SizedBox(height: 11),
        TextFormField(
          onChanged: phoneNumber,
          initialValue: phoneNumber.value,
          decoration: InputDecoration(
            labelText: t.lab.phone,
          ),
        ),
        const SizedBox(height: 13),
        Align(
          alignment: Get.locale?.languageCode == 'ar'
              ? Alignment.bottomLeft
              : Alignment.centerRight,
          child: ElevatedButton(
            child: Text(t.buttonTxt.save),
            onPressed: () {
              if (repo == null) {
                tryAPI(() async {
                  final result =
                      await LabsAPI.create(name.value, phoneNumber.value);
                  Get.back(result: result);
                });
              } else {
                tryAPI(() async {
                  final result = await LabsAPI.edit(
                      repo!.id, name.value, phoneNumber.value);
                  Get.back(result: result);
                  return;
                });
              }
            },
          ),
        ),
      ],
    );
  }
}
