import 'package:core_app/components/dialogs/basic.dart';
import 'package:core_app/components/fields/clickable_field.dart';
import 'package:core_app/components/fields/text.dart';
import 'package:core_app/components/search_patient.dart';
import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/core/models/lab.dart';
import 'package:core_app/core/utils/datetime.dart';
import 'package:core_app/core/utils/i18n/translations.g.dart';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'controller.dart';

extension on String {
  String? get capitalizeFirstLetters {
    return split(" ").map((str) => str._capitalizeFirst).join(" ");
  }

  String? get _capitalizeFirst {
    return "${this[0].toUpperCase()}${substring(1)}";
  }
}

class RequestForm extends StatelessWidget {
  const RequestForm(this.labId, this.repo, {super.key});

  final String labId;
  final LabRequestModel? repo;

  @override
  Widget build(BuildContext context) {
    final c = Get.put(CRequestForm(labId, repo));
    const textStyle = TextStyle(
      color: ThemeColors.notion,
      fontWeight: FontWeight.w600,
      fontSize: 16,
    );
    final isGoodSize = context.width > 850;
    final double fieldWidth = isGoodSize == false ? 1 : 0.48;
    return BasicDialog(
      width: 1000,
      title: (repo == null ? t.labRequestView.add : t.labRequestView.edit).tr,
      actions: [
        Align(
          alignment: Alignment.centerRight,
          child: ElevatedButton(
            onPressed: repo == null ? c.save : c.updateData,
            child: Text(t.buttonTxt.save),
          ),
        ),
      ],
      children: [
        Wrap(
          spacing: 11,
          runSpacing: 11,
          children: [
            FractionallySizedBox(
              widthFactor: isGoodSize ? .545 : 1,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    t.labRequestForm.generalInfo,
                    style: textStyle,
                  ),
                  const SizedBox(height: 11),
                  Wrap(
                    spacing: 11,
                    runSpacing: 11,
                    children: [
                      FractionallySizedBox(
                        widthFactor: fieldWidth,
                        child: SearchPatients(
                          c.patient,
                          initValue: repo?.patient?.name,
                        ),
                      ),
                      FractionallySizedBox(
                        widthFactor: fieldWidth,
                        child: TextFormField(
                          controller: c.price,
                          inputFormatters: [DecimalTextInputFormatter(2)],
                          decoration:
                              InputDecoration(labelText: t.labRequest.price),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 11),
                  Wrap(
                    spacing: 11,
                    runSpacing: 11,
                    children: [
                      FractionallySizedBox(
                        widthFactor: fieldWidth,
                        child: Obx(() => DropdownButtonFormField<String>(
                              onChanged: c.sendingBranch,
                              value: c.branches.isEmpty
                                  ? null
                                  : c.sendingBranch.value,
                              decoration: InputDecoration(
                                  labelText: t.labRequest.sendingBranch),
                              items: [
                                for (var b in c.branches)
                                  DropdownMenuItem(
                                    value: b.id,
                                    child: Text(b.name.capitalizeFirst!),
                                  ),
                              ],
                            )),
                      ),
                      FractionallySizedBox(
                        widthFactor: fieldWidth,
                        child: Obx(() => DropdownButtonFormField<String>(
                              onChanged: c.receivingBranch,
                              value: c.branches.isEmpty
                                  ? null
                                  : c.receivingBranch.value,
                              decoration: InputDecoration(
                                  labelText: t.labRequest.receivingBranch),
                              items: [
                                for (var b in c.branches)
                                  DropdownMenuItem(
                                    value: b.id,
                                    child: Text(b.name.capitalizeFirst!),
                                  ),
                              ],
                            )),
                      ),
                    ],
                  ),
                  const SizedBox(height: 11),
                  Wrap(
                    spacing: 11,
                    runSpacing: 11,
                    children: [
                      FractionallySizedBox(
                        widthFactor: fieldWidth,
                        child: TextFormField(
                          maxLines: null,
                          controller: c.notes,
                          decoration:
                              InputDecoration(labelText: t.labRequest.notes),
                        ),
                      ),
                      FractionallySizedBox(
                        widthFactor: fieldWidth,
                        child: TextFormField(
                          maxLines: null,
                          controller: c.nextStep,
                          decoration:
                              InputDecoration(labelText: t.labRequest.nextStep),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 11),
                  Wrap(
                    spacing: 11,
                    runSpacing: 11,
                    children: [
                      FractionallySizedBox(
                        widthFactor: fieldWidth,
                        child: Obx(() => ClickableField(
                              hint: t.labRequest.sendDate,
                              value: c.sendDate.value?.toDate(),
                              onTap: () async {
                                final result = await showDatePicker(
                                  context: context,
                                  initialDate:
                                      c.sendDate.value ?? DateTime.now(),
                                  firstDate: DateTime(2015, 8),
                                  lastDate: DateTime(2101),
                                );
                                if (result != null) {
                                  c.sendDate.value = result;
                                }
                              },
                            )),
                      ),
                      FractionallySizedBox(
                        widthFactor: fieldWidth,
                        child: Obx(() => ClickableField(
                              hint: t.labRequest.expectedDeliveryDate,
                              value: c.expectedDeliveryDate.value
                                  ?.toIso8601String()
                                  .split("T")[0],
                              onTap: () async {
                                final result = await showDatePicker(
                                  context: context,
                                  initialDate: c.expectedDeliveryDate.value ??
                                      DateTime.now(),
                                  firstDate: DateTime(2015, 8),
                                  lastDate: DateTime(2101),
                                );
                                if (result != null) {
                                  c.expectedDeliveryDate.value = result;
                                }
                              },
                            )),
                      ),
                    ],
                  ),
                  if (repo != null) const SizedBox(height: 11),
                  if (repo != null)
                    Obx(() => ClickableField(
                          hint: t.labRequest.actualDeliveryDate,
                          value: c.actualDeliveryDate.value?.toDate(),
                          onTap: () async {
                            final result = await showDatePicker(
                              context: context,
                              initialDate:
                                  c.actualDeliveryDate.value ?? DateTime.now(),
                              firstDate: DateTime(2015, 8),
                              lastDate: DateTime(2101),
                            );
                            if (result != null) {
                              c.actualDeliveryDate.value = result;
                            }
                          },
                        )),
                  const SizedBox(height: 11),
                  Text(
                    t.labRequestForm.details,
                    style: textStyle,
                  ),
                  const SizedBox(height: 11),
                  Wrap(
                    spacing: 11,
                    runSpacing: 11,
                    children: [
                      FractionallySizedBox(
                        widthFactor: fieldWidth,
                        child: Obx(() => DropdownButtonFormField<String>(
                              onChanged: c.ponticDesign,
                              decoration: InputDecoration(
                                  labelText: t.labRequest.ponticDesign),
                              value: c.ponticDesign.value != null &&
                                      c.ponticDesign.value!.isNotEmpty
                                  ? c.ponticDesign.value?.capitalizeFirstLetters
                                  : null,
                              items: const [
                                DropdownMenuItem(
                                  value: "",
                                  child: Text("None"),
                                ),
                                DropdownMenuItem(
                                  value: 'Ovate',
                                  child: Text('Ovate'),
                                ),
                                DropdownMenuItem(
                                  value: 'Ridge Lap',
                                  child: Text('Ridge Lap'),
                                ),
                                DropdownMenuItem(
                                  value: 'Modified Ridge Lap',
                                  child: Text('Modified Ridge Lap'),
                                ),
                              ],
                            )),
                      ),
                      FractionallySizedBox(
                        widthFactor: fieldWidth,
                        child: Obx(() => DropdownButtonFormField<String>(
                              decoration: InputDecoration(
                                  labelText: t.labRequest.implantWork),
                              value: c.implantWork.value != null &&
                                      c.implantWork.value!.isNotEmpty
                                  ? c.implantWork.value?.capitalizeFirstLetters
                                  : null,
                              onChanged: c.implantWork,
                              items: const [
                                DropdownMenuItem(
                                  value: "",
                                  child: Text("None"),
                                ),
                                DropdownMenuItem(
                                  value: 'Cement Retaine',
                                  child: Text('Cement Retaine'),
                                ),
                                DropdownMenuItem(
                                  value: 'Screw Retaine',
                                  child: Text('Screw Retaine'),
                                ),
                              ],
                            )),
                      ),
                    ],
                  ),
                  const SizedBox(height: 11),
                  Wrap(
                    spacing: 11,
                    runSpacing: 11,
                    children: [
                      FractionallySizedBox(
                        widthFactor: fieldWidth,
                        child: Obx(() => DropdownButtonFormField<String>(
                              onChanged: c.zro2,
                              isExpanded: true,
                              decoration:
                                  InputDecoration(labelText: t.labRequest.zro2),
                              value: c.zro2.value != null &&
                                      c.zro2.value!.isNotEmpty
                                  ? c.zro2.value!.capitalizeFirstLetters
                                  : null,
                              items: const [
                                DropdownMenuItem(
                                  value: "",
                                  child: Text("None"),
                                ),
                                DropdownMenuItem(
                                  value:
                                      'Monolithic Zirconia High Translucency',
                                  child: Text(
                                      'Monolithic Zirconia High Translucency'),
                                ),
                                DropdownMenuItem(
                                  value:
                                      'Monolithic Zirconia Ultra Translucency',
                                  child: Text(
                                      'Monolithic Zirconia Ultra Translucency'),
                                ),
                                DropdownMenuItem(
                                  value: 'Monolithic Zirconia Multilayered',
                                  child:
                                      Text('Monolithic Zirconia Multilayered'),
                                ),
                                DropdownMenuItem(
                                  value: 'Layered Zirconia',
                                  child: Text('Layered Zirconia'),
                                ),
                              ],
                            )),
                      ),
                      FractionallySizedBox(
                        widthFactor: fieldWidth,
                        child: Obx(() => DropdownButtonFormField<String>(
                              onChanged: c.threeDPrinting,
                              decoration: InputDecoration(
                                  labelText: t.labRequest.threeDPrinting),
                              value: c.threeDPrinting.value != null &&
                                      c.threeDPrinting.value!.isNotEmpty
                                  ? c.threeDPrinting.value
                                      ?.capitalizeFirstLetters
                                  : null,
                              items: const [
                                DropdownMenuItem(
                                  value: "",
                                  child: Text("None"),
                                ),
                                DropdownMenuItem(
                                  value: 'Full Model',
                                  child: Text('Full Model'),
                                ),
                                DropdownMenuItem(
                                  value: 'Quadrant Model',
                                  child: Text('Quadrant Model'),
                                ),
                                DropdownMenuItem(
                                  value: 'Model Segment',
                                  child: Text('Model Segment'),
                                ),
                                DropdownMenuItem(
                                  value: 'Bite Splint',
                                  child: Text('Bite Splint'),
                                ),
                                DropdownMenuItem(
                                  value: 'Digital Denture',
                                  child: Text('Digital Denture'),
                                ),
                              ],
                            )),
                      ),
                    ],
                  ),
                  const SizedBox(height: 11),
                  Wrap(
                    spacing: 11,
                    runSpacing: 11,
                    children: [
                      FractionallySizedBox(
                        widthFactor: fieldWidth,
                        child: Obx(() => DropdownButtonFormField<String>(
                              onChanged: c.emax,
                              decoration:
                                  InputDecoration(labelText: t.labRequest.emax),
                              value: c.emax.value != null &&
                                      c.emax.value!.isNotEmpty
                                  ? c.emax.value?.capitalizeFirstLetters
                                  : null,
                              items: const [
                                DropdownMenuItem(
                                  value: "",
                                  child: Text("None"),
                                ),
                                DropdownMenuItem(
                                  value: 'Monolithic',
                                  child: Text('Monolithic'),
                                ),
                                DropdownMenuItem(
                                  value: 'Layered',
                                  child: Text('Layered'),
                                ),
                              ],
                            )),
                      ),
                      FractionallySizedBox(
                        widthFactor: fieldWidth,
                        child: Obx(() => DropdownButtonFormField<String>(
                              onChanged: c.customAbutment,
                              decoration: InputDecoration(
                                  labelText: t.labRequest.customAbutment),
                              value: c.customAbutment.value != null &&
                                      c.customAbutment.value!.isNotEmpty
                                  ? c.customAbutment.value!
                                      .capitalizeFirstLetters
                                  : null,
                              items: const [
                                DropdownMenuItem(
                                  value: "",
                                  child: Text("None"),
                                ),
                                DropdownMenuItem(
                                  value: 'Titanium',
                                  child: Text('Titanium'),
                                ),
                                DropdownMenuItem(
                                  value: 'ZrO2',
                                  child: Text('ZrO2'),
                                ),
                                DropdownMenuItem(
                                  value: 'Pekkton',
                                  child: Text('Pekkton'),
                                ),
                              ],
                            )),
                      ),
                    ],
                  ),
                  const SizedBox(height: 11),
                  Wrap(
                    spacing: 11,
                    runSpacing: 11,
                    children: [
                      FractionallySizedBox(
                        widthFactor: fieldWidth,
                        child: Obx(() => DropdownButtonFormField<String>(
                              isExpanded: true,
                              onChanged: c.fullArchImplant,
                              decoration: InputDecoration(
                                  labelText: t.labRequest.fullArchImplant),
                              value: c.fullArchImplant.value != null &&
                                      c.fullArchImplant.value!.isNotEmpty
                                  ? c.fullArchImplant.value
                                      ?.capitalizeFirstLetters
                                  : null,
                              items: const [
                                DropdownMenuItem(
                                  value: "",
                                  child: Text("None"),
                                ),
                                DropdownMenuItem(
                                  value: 'Zr Framework + UT ZR Or Emax',
                                  child: Text('Zr Framework + UT ZR Or Emax'),
                                ),
                                DropdownMenuItem(
                                  value: 'Metal Framework + UT ZR Or Emax',
                                  child:
                                      Text('Metal Framework + UT ZR or Emax'),
                                ),
                                DropdownMenuItem(
                                  value: 'Pekkton Framework + UT ZR OiEmax',
                                  child:
                                      Text('Pekkton Framework + UT ZR OiEmax'),
                                ),
                              ],
                            )),
                      ),
                      FractionallySizedBox(
                        widthFactor: fieldWidth,
                        child: Obx(() => DropdownButtonFormField<String>(
                              isExpanded: true,
                              onChanged: c.pmma,
                              decoration:
                                  InputDecoration(labelText: t.labRequest.pmma),
                              value: c.pmma.value != null &&
                                      c.pmma.value!.isNotEmpty
                                  ? c.pmma.value
                                  : null,
                              items: const [
                                DropdownMenuItem(
                                  value: "",
                                  child: Text("None"),
                                ),
                                DropdownMenuItem(
                                  value: 'Mono Shaded',
                                  child: Text('Mono Shaded'),
                                ),
                                DropdownMenuItem(
                                  value: 'Multi Shaded',
                                  child: Text('Multi Shaded'),
                                ),
                                DropdownMenuItem(
                                  value: 'Mono Shaded (Stained + Glazed)',
                                  child: Text('Mono Shaded (Stained + Glazed)'),
                                ),
                                DropdownMenuItem(
                                  value: 'Multi Shaded (Stained + Glazed)',
                                  child:
                                      Text('Multi Shaded (Stained + Glazed)'),
                                ),
                              ],
                            )),
                      ),
                    ],
                  ),
                  const SizedBox(height: 11),
                  Obx(() => ClickableField(
                        hint: t.labRequest.misc,
                        value: c.miscellaneous.isEmpty
                            ? null
                            : c.miscellaneous.join(', '),
                        onTap: () {
                          Get.dialog(_MiscChoose(c: c));
                        },
                      )),
                ],
              ),
            ),
            FractionallySizedBox(
              widthFactor: isGoodSize ? .44 : 1,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    t.labRequest.shadeDetails,
                    style: textStyle,
                  ),
                  const SizedBox(height: 11),
                  _Shades(c.shadePoints),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }
}

class _MiscChoose extends StatelessWidget {
  const _MiscChoose({
    Key? key,
    required this.c,
  }) : super(key: key);

  final CRequestForm c;

  @override
  Widget build(BuildContext context) {
    const millingAbutment = 'Milling Abutment';
    const verificationJig = 'Verification Jig';
    const biteBlock = 'Bite Block';
    const specialTray = 'Special Tray';
    const nightGuard = 'Night Guard';
    const manualWaxUp = 'Manual Wax up';
    const implantPlanning = 'Implant Planning';
    const dsd = 'DSD + Digital Wax up';
    return BasicDialog(
      title: 'Miscellaneous',
      width: 350,
      children: [
        Obx(() => CheckboxListTile(
              value: c.miscellaneous.contains(millingAbutment),
              title: const Text(millingAbutment),
              onChanged: (value) {
                if (c.miscellaneous.contains(millingAbutment)) {
                  c.miscellaneous.remove(millingAbutment);
                } else {
                  c.miscellaneous.add(millingAbutment);
                }
              },
            )),
        Obx(() => CheckboxListTile(
              value: c.miscellaneous.contains(verificationJig),
              title: const Text(verificationJig),
              onChanged: (value) {
                if (c.miscellaneous.contains(verificationJig)) {
                  c.miscellaneous.remove(verificationJig);
                } else {
                  c.miscellaneous.add(verificationJig);
                }
              },
            )),
        Obx(() => CheckboxListTile(
              value: c.miscellaneous.contains(biteBlock),
              title: const Text(biteBlock),
              onChanged: (value) {
                if (c.miscellaneous.contains(biteBlock)) {
                  c.miscellaneous.remove(biteBlock);
                } else {
                  c.miscellaneous.add(biteBlock);
                }
              },
            )),
        Obx(() => CheckboxListTile(
              value: c.miscellaneous.contains(specialTray),
              title: const Text(specialTray),
              onChanged: (value) {
                if (c.miscellaneous.contains(specialTray)) {
                  c.miscellaneous.remove(specialTray);
                } else {
                  c.miscellaneous.add(specialTray);
                }
              },
            )),
        Obx(() => CheckboxListTile(
              value: c.miscellaneous.contains(nightGuard),
              title: const Text(nightGuard),
              onChanged: (value) {
                if (c.miscellaneous.contains(nightGuard)) {
                  c.miscellaneous.remove(nightGuard);
                } else {
                  c.miscellaneous.add(nightGuard);
                }
              },
            )),
        Obx(() => CheckboxListTile(
              value: c.miscellaneous.contains(manualWaxUp),
              title: const Text(manualWaxUp),
              onChanged: (value) {
                if (c.miscellaneous.contains(manualWaxUp)) {
                  c.miscellaneous.remove(manualWaxUp);
                } else {
                  c.miscellaneous.add(manualWaxUp);
                }
              },
            )),
        Obx(() => CheckboxListTile(
              value: c.miscellaneous.contains(implantPlanning),
              title: const Text(implantPlanning),
              onChanged: (value) {
                if (c.miscellaneous.contains(implantPlanning)) {
                  c.miscellaneous.remove(implantPlanning);
                } else {
                  c.miscellaneous.add(implantPlanning);
                }
              },
            )),
        Obx(() => CheckboxListTile(
              value: c.miscellaneous.contains(dsd),
              title: const Text(dsd),
              onChanged: (value) {
                if (c.miscellaneous.contains(dsd)) {
                  c.miscellaneous.remove(dsd);
                } else {
                  c.miscellaneous.add(dsd);
                }
              },
            )),
      ],
    );
  }
}

class _Shades extends StatefulWidget {
  const _Shades(this.points);
  final List<ShadePoint> points;

  @override
  State<_Shades> createState() => _ShadesState();
}

class _ShadesState extends State<_Shades> {
  late final List<ShadePoint> points;

  @override
  void initState() {
    points = widget.points;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        AspectRatio(
          aspectRatio: 1600 / 1022, // image file dimensions in px
          child: LayoutBuilder(builder: (context, c) {
            return Stack(
              clipBehavior: Clip.antiAlias,
              children: [
                Positioned.fill(
                  child: GestureDetector(
                    onTapUp: (details) {
                      var dxPercentage =
                          (details.localPosition.dx * 100) / c.maxWidth;
                      var dyPercentage =
                          (details.localPosition.dy * 100) / c.maxHeight;
                      points.add(ShadePoint(dxPercentage, dyPercentage, ''));
                      setState(() {});
                    },
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(21),
                      child: Image.asset(
                        'assets/v1_teeth.jpg',
                        filterQuality: FilterQuality.high,
                      ),
                    ),
                  ),
                ),
                for (var p in points)
                  Positioned(
                    left: (p.dx * c.maxWidth) / 100,
                    top: (p.dy * c.maxHeight) / 100,
                    child: GestureDetector(
                      onTap: () {
                        points.remove(p);
                        setState(() {});
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            vertical: 3, horizontal: 7),
                        decoration: const BoxDecoration(
                          color: Colors.black,
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(1),
                            topRight: Radius.circular(50),
                            bottomLeft: Radius.circular(50),
                            bottomRight: Radius.circular(50),
                          ),
                        ),
                        child: Text(
                          (points.indexOf(p) + 1).toString(),
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ),
              ],
            );
          }),
        ),
        const SizedBox(height: 13),
        ListView.builder(
          shrinkWrap: true,
          itemCount: points.length,
          itemBuilder: (context, index) {
            final p = points[points.length - 1 - index];
            return ListTile(
              key: ValueKey(p),
              horizontalTitleGap: 0,
              leading: Chip(
                labelPadding: const EdgeInsets.all(3),
                side: const BorderSide(
                  color: ThemeColors.notion,
                  width: .3,
                ),
                label: Text(
                  (points.length - index).toString(),
                  style: const TextStyle(
                    color: ThemeColors.text,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              title: TextFormField(
                maxLines: null,
                initialValue: p.notes,
                decoration: const InputDecoration(labelText: 'Notes'),
                onChanged: (val) {
                  p.notes = val;
                },
              ),
              trailing: IconButton(
                onPressed: () {
                  points.removeAt(points.length - 1 - index);
                  setState(() {});
                },
                icon: const Icon(Icons.remove),
                alignment: Alignment.centerRight,
              ),
            );
          },
        ),
      ],
    );
  }
}
