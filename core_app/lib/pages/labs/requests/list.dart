import 'package:core_app/components/buttons/text_icon.dart';
import 'package:core_app/components/tables/table.dart';
import 'package:core_app/core/api/lab_requests.dart';
import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/core/helpers/api_handler.dart';
import 'package:core_app/core/models/lab.dart';
import 'package:core_app/core/utils/datetime.dart';
import 'package:core_app/core/utils/i18n/translations.g.dart';
import 'package:core_app/pages/labs/list.dart';

import 'package:core_app/services/notify/messenger.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'form/form.dart';

class RequestListComponent extends StatelessWidget {
  const RequestListComponent({super.key});

  @override
  Widget build(BuildContext context) {
    final c = Get.find<LabsListController>();
    return Column(
      children: [
        const SizedBox(height: 20),
        Obx(() {
          final value2 = c.labRequests.value[c.currentPage.value];
          return TableComponent<LabRequestModel>(
            title: t.labRequestView.requests,
            data: value2 ?? [],
            actions: [
              XTextIconButton(
                icon: Iconsax.add_copy,
                title: t.labRequestView.add,
                onPressed: () async {
                  if (c.selectedLabId.value == null) {
                    NotifyService.notice(title: 'Select lab');
                    return;
                  }
                  final LabRequestModel? r = await Get.dialog(RequestForm(
                    c.selectedLabId.value!,
                    null,
                  ));
                  if (r != null) {
                    c.labRequests[c.currentPage.value]?.add(r);
                    c.labRequests.refresh();
                  }
                },
              ),
            ],
            columns: [
              if (c.selectedLabId.value == null)
                TableColumn(
                  flex: 1,
                  title: t.labRequest.clinicLab,
                  minWidth: 125,
                  builder: (data) {
                    final lab = c.labs.firstWhere(
                        (element) => element.id == data.clinicLabId);
                    return TableColumn.stringBuilder(lab.name);
                  },
                ),
              TableColumn(
                flex: 2,
                title: t.labRequest.patientId,
                minWidth: 150,
                builder: (data) {
                  return TableColumn.stringBuilder(data.patient?.name ?? 'N/A');
                },
              ),
              TableColumn(
                flex: 2,
                minWidth: 100,
                title: t.labRequest.sendDate,
                builder: (data) {
                  return TableColumn.stringBuilder(data.sendDate.toDate());
                },
              ),
              TableColumn(
                flex: 2,
                minWidth: 200,
                title: t.labRequest.expectedDeliveryDate,
                builder: (data) {
                  return TableColumn.stringBuilder(
                      data.expectedDeliveryDate.toDate());
                },
              ),
              TableColumn(
                flex: 2,
                minWidth: 200,
                title: t.labRequest.actualDeliveryDate,
                builder: (data) {
                  return TableColumn.stringBuilder(
                      data.actualDeliveryDate?.toDate() ?? '');
                },
              ),
              TableColumn(
                flex: 1,
                minWidth: 100,
                title: '',
                builder: (data) {
                  return Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      IconButton(
                        iconSize: 18,
                        icon: const Icon(
                          Iconsax.edit_copy,
                          size: 18,
                        ),
                        onPressed: () async {
                          final LabRequestModel? r = await Get.dialog(
                              RequestForm(data.clinicLabId, data));
                          if (r != null) {
                            final i = c.labRequests[c.currentPage.value]!
                                .indexOf(data);
                            c.labRequests[c.currentPage.value]!.remove(data);
                            c.labRequests[c.currentPage.value]!.insert(i, r);
                            c.labRequests.refresh();
                          }
                        },
                      ),
                      IconButton(
                        iconSize: 18,
                        icon: const Icon(
                          Iconsax.trash_copy,
                          size: 18,
                        ),
                        onPressed: () async {
                          tryAPI(() async {
                            await LabRequestsAPI.delete(data.id);
                            c.labRequests[c.currentPage.value]!.remove(data);
                            c.labRequests.refresh();
                          });
                        },
                      ),
                    ],
                  );
                },
              ),
            ],
          );
        }),
        const SizedBox(height: 20),
        Obx(() {
          if (!c.next.value && c.currentPage.value <= 1) {
            return const SizedBox.shrink();
          }
          return Align(
            alignment: Alignment.centerRight,
            child: Container(
              width: 200,
              height: 50,
              padding: const EdgeInsets.symmetric(horizontal: 11, vertical: 5),
              decoration: BoxDecoration(
                color: ThemeColors.bg,
                borderRadius: BorderRadius.circular(13),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  if (c.currentPage.value > 1)
                    IconButton(
                      icon: const Icon(Icons.keyboard_arrow_left_rounded),
                      onPressed: c.loadPreviousPage,
                    ),
                  Text(
                    'Page ${c.currentPage.value}',
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF364152),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (c.next.value)
                    IconButton(
                      icon: const Icon(Icons.keyboard_arrow_right_rounded),
                      onPressed: c.loadPage,
                    ),
                ],
              ),
            ),
          );
        }),
      ],
    );
  }
}
