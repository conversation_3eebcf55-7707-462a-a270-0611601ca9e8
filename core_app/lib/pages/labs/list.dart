import 'package:core_app/components/buttons/popup_menu.dart';
import 'package:core_app/core/api/lab_requests.dart';
import 'package:core_app/core/api/labs.dart';
import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/core/helpers/api_handler.dart';
import 'package:core_app/core/models/lab.dart';
import 'package:core_app/core/utils/i18n/translations.g.dart';
import 'package:core_app/layout/page_layout.dart';

import 'package:core_app/services/notify/messenger.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'form.dart';
import 'requests/list.dart';

class LabsListController extends GetxController {
  static LabsListController get to => Get.find();

  final labs = <LabModel>[].obs;

  RxBool next = true.obs;
  RxInt currentPage = 0.obs;
  final selectedLabId = Rx<String?>(null);
  final labRequests = <int, List<LabRequestModel>>{}.obs;

  void loadPage() {
    currentPage++;
    tryAPI(() async {
      var list =
          await LabRequestsAPI.list(selectedLabId.value, currentPage.value);
      if (list.isEmpty) {
        next.value = false;
        // currentPage--;
        labRequests[currentPage.value] = [];
      } else if (list.length < 20) {
        labRequests[currentPage.value] = list;
        next.value = false;
      } else {
        labRequests[currentPage.value] = list;
        next.value = true;
      }
    });
  }

  loadPreviousPage() {
    currentPage--;
  }

  @override
  Future<void> onInit() async {
    tryAPI(() async {
      labs.value = await LabsAPI.list();
      // if (labs.isNotEmpty) selectedLabId.value = labs.first.id;
    }).then((value) => loadPage());
    super.onInit();
  }

  Future<void> fetchLabs() async {
    await tryAPI(() async {
      labs.value = await LabsAPI.list();
      // if (labs.isNotEmpty) selectedLabId.value = labs.first.id;
    });
  }
}

class LabsListPage extends StatelessWidget {
  const LabsListPage({super.key});

  @override
  Widget build(BuildContext context) {
    final c = Get.find<LabsListController>();
    return PageLayout(
      title: t.labView.labs,
      children: [
        Obx(() => Wrap(
              spacing: 11,
              runSpacing: 11,
              children: [
                for (var l in c.labs)
                  GestureDetector(
                    onTap: () {
                      if (c.selectedLabId.value != l.id) {
                        c.selectedLabId.value = l.id;
                        c.labRequests.clear();
                      } else {
                        c.selectedLabId.value = null;
                      }
                      c.currentPage.value = 0;
                      c.loadPage();
                    },
                    child: Container(
                      constraints: const BoxConstraints(minWidth: 210),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(13),
                        border: Border.all(
                          width: c.selectedLabId.value == l.id ? 1 : .5,
                          color: c.selectedLabId.value == l.id
                              ? ThemeColors.primaryLight
                              : Colors.grey.shade400,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  l.name,
                                  style: const TextStyle(
                                    color: ThemeColors.text,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 26,
                                  ),
                                ),
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Text(
                                      '${t.lab.phone}: ',
                                      style: const TextStyle(
                                        color: ThemeColors.notion,
                                        fontWeight: FontWeight.w400,
                                      ),
                                    ),
                                    Text(
                                      l.phoneNumber.isEmpty
                                          ? "N/A"
                                          : l.phoneNumber,
                                      style: TextStyle(
                                        color: l.phoneNumber.isEmpty
                                            ? Colors.grey
                                            : ThemeColors.text,
                                        // fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ],
                                )
                              ],
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: AutoPopupMenu(
                              items: [
                                AutoPopupMenuItem(
                                  name: t.edit,
                                  onTap: () async {
                                    await Get.dialog(LabFormDialog(l));
                                    c.fetchLabs();
                                  },
                                ),
                                AutoPopupMenuItem(
                                  name: t.delete,
                                  onTap: () {
                                    tryAPI(() async {
                                      await LabsAPI.delete(l.id);
                                      await c.fetchLabs();
                                      NotifyService.success(
                                          'Deleted successfully');
                                    });
                                  },
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                Material(
                  clipBehavior: Clip.antiAlias,
                  color: Colors.transparent,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(13),
                  ),
                  child: InkWell(
                    onTap: () async {
                      final LabModel? lab =
                          await Get.dialog(const LabFormDialog(null));
                      if (lab != null) {
                        c.labs.add(lab);
                      }
                    },
                    child: const SizedBox(
                      height: 90,
                      width: 100,
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Icon(Icons.add, size: 24, color: ThemeColors.text),
                            SizedBox(height: 5),
                            Text('Add Lab',
                                style: TextStyle(
                                    color: ThemeColors.text, fontSize: 14)),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            )),
        const RequestListComponent(),
      ],
    );
  }
}
