import 'package:core_app/components/buttons/text_icon.dart';
import 'package:core_app/components/tables/table.dart';
import 'package:core_app/core/api/specialities.dart';
import 'package:core_app/core/helpers/api_handler.dart';
import 'package:core_app/core/models/speciality.dart';
import 'package:core_app/core/utils/i18n/translations.g.dart';
import 'package:core_app/layout/page_layout.dart';
import 'package:core_app/routes/navigation.dart';

import 'package:core_app/routes/route_paths.dart';
import 'package:core_app/services/auth.dart';
import 'package:core_app/services/notify/messenger.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'create/speciality.dart';

class SpecialititesListController extends GetxController {
  final specialities = <SpecialityTemplateModel>[].obs;

  void fetchAPI() {
    tryAPI(() async {
      specialities.value = await SpecialitiesAPI.list();
    });
  }

  @override
  void onInit() {
    fetchAPI();
    super.onInit();
  }
}

class SpecialitiesListPage extends StatelessWidget {
  const SpecialitiesListPage({super.key});

  @override
  Widget build(BuildContext context) {
    final c = Get.find<SpecialititesListController>();
    return PageLayout(
      title: t.specialitiesList.title,
      children: [
        Obx(() => TableComponent<SpecialityTemplateModel>(
              title: t.specialitiesList.title,
              onRowTap: (data) {
                Navigation.to(Routes.SpecialityProcedures, {
                  'specialityID': data.id,
                });
              },
              actions: [
                XTextIconButton(
                  title: t.specialitiesList.add,
                  icon: Iconsax.add_copy,
                  onPressed: () async {
                    if (AuthService.to.isSubEnded) {
                      NotifyService.notice(title: t.subEnded);
                      return;
                    }

                    SpecialityTemplateModel? repo = await Get.dialog(
                        const CreateSpecialityTemplateForm(null));
                    if (repo != null) c.specialities.add(repo);
                  },
                ),
              ],
              data: c.specialities.value,
              columns: [
                TableColumn(
                  flex: 3,
                  title: t.speciality.name,
                  // minWidth: 300,
                  builder: (data) {
                    return TableColumn.stringBuilder(data.name, clip: false);
                  },
                ),
                TableColumn(
                  title: '',
                  minWidth: 150,
                  builder: (data) {
                    return Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        IconButton(
                          iconSize: 18,
                          icon: const Icon(
                            Iconsax.edit_copy,
                            size: 18,
                          ),
                          onPressed: () async {
                            if (AuthService.to.isSubEnded) {
                              NotifyService.notice(title: t.subEnded);
                              return;
                            }
                            SpecialityTemplateModel? repo = await Get.dialog(
                                CreateSpecialityTemplateForm(data));
                            if (repo != null) {
                              int i = c.specialities.indexOf(data);
                              c.specialities.remove(data);
                              c.specialities.insert(i, repo);
                            }
                          },
                        ),
                        IconButton(
                          iconSize: 18,
                          icon: const Icon(
                            Iconsax.trash_copy,
                            size: 18,
                          ),
                          onPressed: () {
                            if (AuthService.to.isSubEnded) {
                              NotifyService.notice(title: t.subEnded);
                              return;
                            }
                            tryAPI(() async {
                              await SpecialitiesAPI.delete(data.id);
                              c.specialities.remove(data);
                            });
                          },
                        ),
                      ],
                    );
                  },
                ),
              ],
            )),
      ],
    );
  }
}
