import 'package:core_app/components/dialogs/basic.dart';
import 'package:core_app/core/utils/i18n/translations.g.dart';
import 'package:core_app/core/utils/pre_wrriten_procedures.dart';
import 'package:core_app/pages/specialities/list.dart';

import 'package:core_app/services/api.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';

class PreWrritenSpecialityController extends GetxController {
  final selectedGroup = RxMap();

  /// contains the iput of every procedure like price, crown ..etc if the user has written one
  final customDataForProcedures = RxMap();

  void save() async {
    await ApiService.post('/speciality-templates/bulk', data: {
      'specialities': [
        {
          'speciality': selectedGroup.value['speciality'],
          'procedures': (selectedGroup.value['procedures'] as List)
            ..forEach(
              (e) => e['price'] = double.tryParse(
                      customDataForProcedures[e['procedure']]?['price'] ??
                          '0.0') ??
                  0.0,
            ),
        },
      ],
    });
    Get.back();
    Get.find<SpecialititesListController>().fetchAPI();
  }
}

class PreWrittenSpecialityForm extends StatelessWidget {
  const PreWrittenSpecialityForm({super.key});

  String yesOrNo(bool i) => i ? 'Yes' : 'No';

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final c = Get.put(PreWrritenSpecialityController());
    return Obx(() {
      final preWrettenProcedures =
          List<Map>.from(PRE_WRETTEN_PROCEDURES['specialities'] as List<Map>);
      preWrettenProcedures.removeWhere(
          (e) => Get.find<SpecialititesListController>().specialities.any(
                (element) =>
                    e['speciality'].toLowerCase().trim() ==
                    element.name.toLowerCase().trim(),
              ));
      return BasicDialog(
        title: "Pre-Written",
        width: 800,
        children: [
          if (c.selectedGroup.isEmpty)
            for (final s in preWrettenProcedures)
              ListTile(
                horizontalTitleGap: 7,
                leading: const Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Icon(
                      Icons.circle_outlined,
                      size: 16,
                    ),
                  ],
                ),
                title: Text(s['speciality']),
                subtitle: Text(
                  s['procedures'].map((e) => e['procedure']).join(', '),
                ),
                subtitleTextStyle: theme.textTheme.labelMedium?.copyWith(
                  color: Colors.black54,
                ),
                onTap: () {
                  c.selectedGroup.value = {
                    'speciality': s['speciality'],
                    'procedures':
                        List.from(s['procedures'].map((e) => Map.from(e))),
                  };
                },
              )
          else ...[
            ListTile(
              title: Text(
                c.selectedGroup['speciality'],
                style: theme.textTheme.titleLarge,
              ),
              trailing: ElevatedButton(
                onPressed: c.save,
                child: Text(t.buttonTxt.save),
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 400,
              width: double.infinity,
              child: SingleChildScrollView(
                child: Form(
                  key: GlobalKey(),
                  child: Column(
                    children: [
                      for (final p in c.selectedGroup['procedures'])
                        SizedBox(
                          // height: 80,
                          key: Key(p['procedure']),
                          width: double.infinity,
                          child: ListTile(
                            title: Text(
                              p['procedure'],
                              style: theme.textTheme.bodyMedium,
                            ),
                            subtitle: Text(
                              'Teeth Removing: ${yesOrNo(p['toothRemoved'])} | '
                              'Crown: ${yesOrNo(p['crown'])} | '
                              'Endo: ${yesOrNo(p['endo'])} | '
                              'Implant: ${yesOrNo(p['implant'])}',
                              style: theme.textTheme.labelSmall,
                            ),
                            trailing: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                SizedBox(
                                  width: 150,
                                  child: TextFormField(
                                    key: Key(p['procedure'] + 'textfield'),
                                    textInputAction: TextInputAction.next,
                                    decoration: const InputDecoration(
                                      labelText: 'Price',
                                      hintText: '0.0',
                                    ),
                                    onChanged: (value) {
                                      c.customDataForProcedures[
                                          p['procedure']] = {
                                        'price': value,
                                      };
                                    },
                                  ),
                                ),
                                const SizedBox(width: 11),
                                IconButton(
                                  autofocus: false,
                                  focusNode: FocusNode(skipTraversal: true),
                                  icon: const Icon(
                                    Iconsax.trash_copy,
                                    size: 18,
                                  ),
                                  onPressed: () {
                                    (c.selectedGroup['procedures'] as List)
                                        .remove(p);
                                    c.selectedGroup.refresh();
                                  },
                                ),
                              ],
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ],
      );
    });
  }
}
