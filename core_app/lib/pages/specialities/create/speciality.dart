import 'package:core_app/components/dialogs/basic.dart';
import 'package:core_app/core/api/specialities.dart';
import 'package:core_app/core/helpers/api_handler.dart';
import 'package:core_app/core/models/speciality.dart';
import 'package:core_app/core/utils/i18n/translations.g.dart';
import 'package:core_app/pages/specialities/create/pre_written.dart';

import 'package:flutter/material.dart';

import 'package:get/get.dart';

class _CreateSpecialityTemplateController extends GetxController {
  final specialityName = ''.obs;
  final SpecialityTemplateModel? repo;

  _CreateSpecialityTemplateController(this.repo);

  @override
  void onInit() {
    specialityName.value = repo?.name ?? '';
    super.onInit();
  }

  void save() {
    tryAPI(() async {
      final i = await SpecialitiesAPI.create(specialityName.value);
      Get.back(result: i);
    });
  }

  void edit() {
    tryAPI(() async {
      final i = await SpecialitiesAPI.edit(repo!.id, specialityName.value);
      Get.back(result: i);
    });
  }
}

class CreateSpecialityTemplateForm extends StatelessWidget {
  const CreateSpecialityTemplateForm(this.repo, {super.key});

  final SpecialityTemplateModel? repo;

  @override
  Widget build(BuildContext context) {
    final c = Get.put(_CreateSpecialityTemplateController(repo));
    return BasicDialog(
      title: repo != null ? t.specialitiesList.edit : t.specialitiesList.add,
      children: [
        if (repo == null)
          Align(
            alignment: Alignment.centerRight,
            child: TextButton(
              child: const Text('Pre-written Specialities'),
              onPressed: () async {
                Get.back();
                Get.dialog(const PreWrittenSpecialityForm());
              },
            ),
          ),
        const SizedBox(height: 11),
        TextFormField(
          onChanged: c.specialityName,
          initialValue: c.specialityName.value,
          decoration: InputDecoration(
            labelText: t.speciality.name,
          ),
        ),
        const SizedBox(height: 13),
        Align(
          alignment: Get.locale?.languageCode == 'ar'
              ? Alignment.centerLeft
              : Alignment.centerRight,
          child: ElevatedButton(
            onPressed: c.repo != null ? c.edit : c.save,
            child: Text(t.buttonTxt.save),
          ),
        )
      ],
    );
  }
}
