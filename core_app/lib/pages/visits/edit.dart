import 'package:core_app/components/buttons/icon.dart';
import 'package:core_app/components/buttons/text.dart';
import 'package:core_app/components/dialogs/basic.dart';
import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/core/helpers/api_handler.dart';
import 'package:core_app/core/models/visit.dart';
import 'package:core_app/core/utils/i18n/translations.g.dart';
import 'package:core_app/pages/visits/create/dialogs/treatment.dart';

import 'package:core_app/services/api.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class _EditVisitController extends GetxController {
  final VisitModel visit;
  _EditVisitController(this.visit);

  final comments = ''.obs;
  final diagnosis = ''.obs;
  final nextVisit = ''.obs;
  final treatments = <String>[].obs;
  final treatmentController = TextEditingController();

  save() {
    tryAPI(() async {
      await ApiService.patch('/visits/${visit.id}', data: {
        "comments": comments.value,
        "diagnosis": diagnosis.value,
        "nextVisit": nextVisit.value,
        "treatments": treatments,
      });
      visit.comments = comments.value;
      visit.diagnosis = diagnosis.value;
      visit.nextVisit = nextVisit.value;
      visit.treatments = treatments.value;
      Get.back();
    });
  }

  @override
  void onInit() {
    comments.value = visit.comments ?? '';
    diagnosis.value = visit.diagnosis;
    nextVisit.value = visit.nextVisit;
    treatments.value = visit.treatments ?? [];
    super.onInit();
  }
}

class EditVisitDialog extends StatelessWidget {
  const EditVisitDialog(this.visit, {super.key});

  final VisitModel visit;

  @override
  Widget build(BuildContext context) {
    final c = Get.put(_EditVisitController(visit));
    return BasicDialog(
      title: 'Edit Visit',
      children: [
        TextFormField(
          maxLines: null,
          onChanged: c.diagnosis,
          initialValue: c.diagnosis.value,
          decoration: InputDecoration(
            labelText: t.visit.diagnosis,
          ),
        ),
        const SizedBox(height: 13),
        const TreatmentsListFormComponent(),
        const SizedBox(height: 13),
        TextFormField(
          maxLines: null,
          onChanged: c.nextVisit,
          initialValue: c.nextVisit.value,
          decoration: InputDecoration(
            labelText: t.visit.nextVisit,
          ),
        ),
        const SizedBox(height: 16),
        Align(
          alignment: Alignment.centerRight,
          child: XTextButton(
            title: t.buttonTxt.save,
            onPressed: c.save,
          ),
        ),
      ],
    );
  }
}

class TreatmentsListFormComponent extends StatelessWidget {
  const TreatmentsListFormComponent({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final focus = FocusNode();
    final controller = Get.find<_EditVisitController>();
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: TextFormField(
                maxLines: 1,
                focusNode: focus,
                controller: controller.treatmentController,
                onFieldSubmitted: (val) {
                  if (val.isNotEmpty) {
                    controller.treatments.add(val);
                    controller.treatmentController.text = '';
                    focus.requestFocus();
                  }
                },
                decoration: InputDecoration(
                  labelText: t.visit.treatments,
                ),
              ),
            ),
            const SizedBox(width: 7),
            XIconButton(
              icon: Icons.add,
              onPressed: () {
                if (controller.treatmentController.text.isNotEmpty) {
                  controller.treatments
                      .add(controller.treatmentController.text);
                  controller.treatmentController.text = '';
                }
              },
            ),
          ],
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 23),
          child: Obx(() => Column(
                children: [
                  for (var i = 0; i < controller.treatments.length; i++)
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 7),
                      child: Material(
                        color: Colors.white,
                        child: ListTile(
                          onTap: () async {
                            String? f = await Get.dialog(
                                EditVisitTreatmentFormDialog(
                                    controller.treatments[i].capitalizeFirst!));
                            if (f == null) return;
                            controller.treatments
                                .remove(controller.treatments[i]);
                            controller.treatments.insert(i, f);
                          },
                          horizontalTitleGap: 0,
                          contentPadding: EdgeInsets.zero,
                          dense: true,
                          leading: Text(
                            "  ${(i + 1)}.",
                            style: TextStyle(
                              fontSize: 15,
                              fontWeight: FontWeight.w600,
                              color: ThemeColors.text.withValues(alpha: 0.71),
                            ),
                          ),
                          title: Text(
                            controller.treatments[i].capitalizeFirst!,
                            style: TextStyle(
                              fontSize: 15,
                              fontWeight: FontWeight.w600,
                              color: ThemeColors.text.withValues(alpha: 0.71),
                            ),
                          ),
                          trailing: XIconButton(
                            icon: Icons.delete_outline_rounded,
                            onPressed: () {
                              controller.treatments
                                  .remove(controller.treatments[i]);
                            },
                          ),
                        ),
                      ),
                    ),
                ],
              )),
        ),
      ],
    );
  }
}
