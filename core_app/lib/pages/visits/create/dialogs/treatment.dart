import 'package:core_app/components/dialogs/basic.dart';
import 'package:core_app/components/fields/text.dart';
import 'package:flutter/material.dart';

class EditVisitTreatmentFormDialog extends StatelessWidget {
  const EditVisitTreatmentFormDialog(this.data, {super.key});

  final String data;

  @override
  Widget build(BuildContext context) {
    final c = TextEditingController(text: data);
    return BasicDialog(
      title: "Edit Treatment",
      onClose: (close) {
        close(c.text);
      },
      children: [
        MainTextField(
          controller: c,
        ),
      ],
    );
  }
}
