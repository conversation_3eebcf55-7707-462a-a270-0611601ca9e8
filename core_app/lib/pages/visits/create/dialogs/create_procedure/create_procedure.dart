import 'package:core_app/components/buttons/icon.dart';
import 'package:core_app/components/buttons/text.dart';
import 'package:core_app/components/dialogs/basic.dart';
import 'package:core_app/components/fields/text.dart';
import 'package:core_app/components/teeth_view.dart';
import 'package:core_app/core/api/procedures.dart';
import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/core/models/procedure.dart';
import 'package:core_app/core/models/speciality.dart';
import 'package:core_app/core/models/user.dart';
import 'package:core_app/core/models/inventory_link.dart';
import 'package:core_app/core/utils/i18n/translations.g.dart';
import 'package:core_app/pages/visits/create/dialogs/create_procedure/controller.dart';

import 'package:core_app/services/config.dart';
import 'package:core_app/services/notify/messenger.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

const _spacedHeight = SizedBox(height: 11);

class ProcedureFormDialog extends StatelessWidget {
  const ProcedureFormDialog({
    super.key,
    this.andCreate = false,
    this.visitId,
  });

  /// if true, the patment will be created on dialog close
  /// else dialog will return [IncompletedProcedure] on close.
  final bool andCreate;

  /// needed if [andCreate] is true
  final String? visitId;

  static Future<List<VisitProcedureModel>> create(
      String visitId, IncompletedProcedure p) async {
    final list = <VisitProcedureModel>[];
    for (var tooth in p.toothNumber) {
      list.add(await ProceduresAPI.create(
        visitId,
        dentstId: p.dentistId,
        toothNumber: tooth.toLowerCase(),
        speciality: p.speciality,
        procedure: p.procedure,
        nextVisit: p.nextVisit,
        procedureTemplatId: p.templateId,
        finalPrice: p.finalPrice,
        toothRemoved: p.toothRemoved,
        notes: p.notes,
        inventoryConsumptions: p.inventoryConsumptions,
      ));
    }
    return list;
  }

  @override
  Widget build(BuildContext context) {
    final c = Get.put(ProcedureFormController());
    return BasicDialog(
      title: t.visitProcedureForm.create,
      children: [
        Obx(() {
          return DropdownButtonFormField<UserModel>(
            value: c.selectedDentist.value,
            onChanged: (val) {
              c.selectedDentist.value = val;
            },
            decoration: InputDecoration(
              labelText: t.visitProcedure.dentist,
            ),
            items: [
              for (var d in c.allDentists)
                DropdownMenuItem(
                  value: d,
                  child: Text(d.name.capitalize!),
                ),
            ],
          );
        }),
        _spacedHeight,
        const _SpecialityField(),
        _spacedHeight,
        const _ProcedureField(),
        _spacedHeight,
        const _InventoryConsumptionSection(),
        _spacedHeight,
        Obx(
          () => Wrap(
            spacing: 9,
            runSpacing: 9,
            children: [
              for (var tooth in c.teethNumber.value)
                Chip(
                  label: Text(
                    tooth.toUpperCase(),
                    style: const TextStyle(
                      color: ThemeColors.text,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  shadowColor: ThemeColors.primary,
                  side: const BorderSide(color: ThemeColors.primary),
                  deleteIcon: const Icon(Iconsax.close_circle_copy,
                      color: ThemeColors.primary, size: 18),
                  onDeleted: () {
                    c.teethNumber.remove(tooth);
                  },
                ),
            ],
          ),
        ),
        const SizedBox(height: 9),
        GestureDetector(
          onTap: () async {
            c.teethNumber.removeWhere((e) => e.isEmpty);
            await Get.dialog(_ChooseTeethDialog(c.teethNumber));
          },
          child: Container(
            width: double.infinity,
            decoration: BoxDecoration(
              border: Border.all(color: ThemeColors.text),
              borderRadius: BorderRadius.circular(5),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 7),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Iconsax.add_copy, color: ThemeColors.text, size: 21),
                const SizedBox(width: 5),
                Text(
                  t.visitProcedureForm.affectedTeeth,
                  style: const TextStyle(
                    color: ThemeColors.text,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),
        _spacedHeight,
        TextFormField(
          controller: c.notes,
          decoration: InputDecoration(labelText: t.visitProcedure.notes),
        ),
        _spacedHeight,
        TextFormField(
          controller: c.nextVisit,
          decoration: InputDecoration(labelText: t.visitProcedure.nextVisit),
        ),
        _spacedHeight,
        const _PriceField(),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            XTextButton(
              title: t.buttonTxt.save,
              onPressed: () async {
                final teethNumber = c.teethNumber;
                if (teethNumber.isEmpty) {
                  NotifyService.notice(title: t.visitProcedureForm.chooseTeeth);
                  return;
                }

                for (var t in teethNumber) {
                  if (t.startsWith(RegExp(r"^(lr|ll|ul|ur)([0-9]|[a-e])$")) &&
                      t.length == 3) {
                    continue;
                  } else {
                    NotifyService.notice(title: 'Invalid teeth number `$t`');
                    return;
                  }
                }

                if (c.selectedProcedure.value == null) {
                  NotifyService.notice(
                      title: t.visitProcedureForm.chooseSpecialityAndProcedure);
                  return;
                }
                if (andCreate) {
                  Get.back(
                    result: await create(
                      visitId!,
                      IncompletedProcedure(
                        notes: c.notes.text,
                        nextVisit: c.nextVisit.text,
                        procedure: c.procedureName.text,
                        speciality: c.specialityName.text,
                        toothRemoved: c.teethRemoved.value,
                        dentistId: c.selectedDentist.value!.id,
                        templateId: c.selectedProcedure.value!.id,
                        toothNumber: c.teethNumber,
                        finalPrice: double.tryParse(c.finalPrice.text) ?? 0,
                        inventoryConsumptions: c.getInventoryConsumptions(),
                      ),
                    ),
                  );
                } else {
                  Get.back(
                      result: IncompletedProcedure(
                    notes: c.notes.text,
                    nextVisit: c.nextVisit.text,
                    procedure: c.procedureName.text,
                    speciality: c.specialityName.text,
                    toothRemoved: c.teethRemoved.value,
                    dentistId: c.selectedDentist.value!.id,
                    templateId: c.selectedProcedure.value!.id,
                    toothNumber: c.teethNumber,
                    finalPrice: double.tryParse(c.finalPrice.text) ?? 0,
                    inventoryConsumptions: c.getInventoryConsumptions(),
                  ));
                }
              },
            ),
          ],
        ),
      ],
    );
  }
}

class _SpecialityField extends StatefulWidget {
  const _SpecialityField();

  @override
  State<_SpecialityField> createState() => _SpecialityFieldState();
}

class _SpecialityFieldState extends State<_SpecialityField> {
  final edit = false.obs;

  @override
  Widget build(BuildContext context) {
    final c = Get.find<ProcedureFormController>();
    return Row(
      children: [
        Expanded(
          child: AnimatedSwitcher(
            duration: kThemeAnimationDuration,
            child: edit.value
                ? TextFormField(
                    controller: c.specialityName,
                    decoration: InputDecoration(
                      labelText: t.procedureTemplate.speciality,
                    ),
                  )
                : Obx(() => DropdownButtonFormField<SpecialityTemplateModel>(
                      decoration: InputDecoration(
                        labelText: t.procedureTemplate.speciality,
                      ),
                      value: c.selectedSpeciality.value,
                      onChanged: (val) {
                        c.selectedSpeciality.value = val;
                        c.specialityName.text = val?.name ?? '';
                        c.selectedProcedure.value = null;
                        c.procedureName.text = '';
                        c.finalPrice.text = '0.0';
                        c.filtredProcedures.value = c.procedures
                            .where((e) => e.specialityId == val?.id)
                            .toList();
                      },
                      items: [
                        for (final s in c.specialities)
                          DropdownMenuItem(
                            value: s,
                            child: Text(s.name.capitalizeFirst!),
                          ),
                      ],
                    )),
          ),
        ),
        const SizedBox(width: 7),
        Obx(() => c.selectedSpeciality.value == null
            ? const SizedBox()
            : XIconButton(
                icon: Icons.edit_outlined,
                onPressed: c.selectedSpeciality.value != null
                    ? () {
                        setState(() {
                          edit.value = !edit.value;
                        });
                      }
                    : null,
              ))
      ],
    );
  }
}

class _ProcedureField extends StatefulWidget {
  const _ProcedureField();

  @override
  State<_ProcedureField> createState() => __ProcedureFieldState();
}

class __ProcedureFieldState extends State<_ProcedureField> {
  bool edit = false;

  @override
  Widget build(BuildContext context) {
    final c = Get.find<ProcedureFormController>();
    return Row(
      children: [
        Expanded(
          child: Obx(
            () {
              if (c.selectedProcedure.value == null) {
                edit = false;
              }
              return AnimatedSwitcher(
                  duration: kThemeAnimationDuration,
                  child: edit
                      ? MainTextField(
                          label: t.visitProcedure.procedure,
                          controller: c.procedureName,
                        )
                      : DropdownButtonFormField<ProcedureTemplateModel>(
                          decoration: InputDecoration(
                            labelText: t.visitProcedure.procedure,
                          ),
                          value: c.selectedProcedure.value,
                          onChanged: (val) {
                            if (val != null) {
                              c.selectedProcedure.value = val;
                              c.procedureName.text = val.name;
                              c.finalPrice.text = val.price.toString();
                              // Fetch inventory links for the selected procedure
                              c.fetchInventoryLinks(val.id);
                            }
                          },
                          items: [
                            for (final p in c.filtredProcedures)
                              DropdownMenuItem(
                                value: p,
                                child: Text(p.name),
                              ),
                          ],
                        ));
            },
          ),
        ),
        const SizedBox(width: 7),
        Obx(() => c.selectedProcedure.value == null
            ? const SizedBox()
            : XIconButton(
                icon: Icons.edit_outlined,
                onPressed: c.selectedProcedure.value != null
                    ? () {
                        setState(() {
                          edit = !edit;
                        });
                      }
                    : null,
              ))
      ],
    );
  }
}

class _PriceField extends StatefulWidget {
  const _PriceField();

  @override
  State<_PriceField> createState() => _PriceSectionState();
}

class _PriceSectionState extends State<_PriceField> {
  final edit = false.obs;
  final finalPriceType = 'Discount'.obs;
  final discount = TextEditingController(text: '0');
  final discountPercentage = TextEditingController(text: '0');

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final c = Get.find<ProcedureFormController>();
    return Obx(() {
      var ignore = c.selectedProcedure.value == null;
      return Opacity(
        opacity: ignore ? .51 : 1,
        child: IgnorePointer(
          ignoring: ignore,
          child: Column(
            children: [
              Row(
                children: [
                  Expanded(
                    child: MainTextField(
                      inputFormatters: MainTextField.decimal,
                      label:
                          'Final Price (${c.selectedProcedure.value?.price ?? 0})',
                      controller: c.finalPrice,
                      onChange: onFinalPrice,
                    ),
                  ),
                  const SizedBox(width: 7),
                  Column(
                    children: [
                      Obx(() => TextButton(
                            child: Text(edit.value ? 'Collapse' : 'Expand'),
                            onPressed: () {
                              setState(() {
                                edit.value = !edit.value;
                              });
                            },
                          )),
                      const SizedBox(width: 7),
                      Obx(() => Container(
                            padding: const EdgeInsets.symmetric(horizontal: 5),
                            child: Text(
                              "x${c.priceMultiplier}",
                              style: const TextStyle(
                                fontWeight: FontWeight.w600,
                                color: ThemeColors.notion,
                              ),
                            ),
                          )),
                    ],
                  ),
                ],
              ),
              _spacedHeight,
              Obx(() => AnimatedSize(
                    duration: kThemeAnimationDuration,
                    child: !edit.value
                        ? const SizedBox()
                        : Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Obx(() => AnimatedSwitcher(
                                    duration: kThemeAnimationDuration,
                                    child: Text(
                                      finalPriceType.value,
                                      key: GlobalKey(),
                                      style: const TextStyle(
                                        fontWeight: FontWeight.w600,
                                        color: ThemeColors.textLight,
                                      ),
                                    ),
                                  )),
                              const SizedBox(height: 5),
                              Row(
                                children: [
                                  Flexible(
                                    child: MainTextField(
                                      controller: discount,
                                      label: 'Value',
                                      inputFormatters: MainTextField.decimal,
                                      onChange: onDiscount,
                                    ),
                                  ),
                                  const SizedBox(width: 7),
                                  Flexible(
                                    child: MainTextField(
                                      label: 'Percentage %',
                                      controller: discountPercentage,
                                      inputFormatters: MainTextField.decimal,
                                      onChange: onPercentageDiscount,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                  )),
            ],
          ),
        ),
      );
    });
  }

  void checkFinalPriceType() {
    final c = Get.find<ProcedureFormController>();
    final finalPrice = double.parse(c.finalPrice.text);
    final mainPrice = c.selectedProcedure.value!.price;

    if (finalPrice < mainPrice) {
      finalPriceType.value = 'Discount';
      return;
    }

    if (finalPrice > mainPrice) {
      finalPriceType.value = 'Surcharge';
      return;
    }

    finalPriceType.value = 'Discount';
  }

  void onFinalPrice(val) {
    final c = Get.find<ProcedureFormController>();
    if (val.isEmpty) {
      discount.text = '0';
      discountPercentage.text = '0';
      return;
    }

    final finalPrice = double.parse(val);
    final mainPrice = c.selectedProcedure.value!.price;

    if (finalPrice > mainPrice) {
      discount.text = finalPrice.round().toString();
      final percentage = ((finalPrice) / mainPrice * 100).round();
      discountPercentage.text = percentage.round().toString();

      checkFinalPriceType();
      return;
    }

    discount.text = (mainPrice - finalPrice).toString();
    final percentage = (mainPrice - finalPrice) / mainPrice * 100;
    discountPercentage.text = percentage.round().toString();

    checkFinalPriceType();
  }

  void onPercentageDiscount(String val) {
    final c = Get.find<ProcedureFormController>();
    if (val.isEmpty) {
      discount.text = '';
      c.finalPrice.text = c.selectedProcedure.value!.price.toString();

      checkFinalPriceType();
      return;
    }

    final percentage = double.parse(val);
    final mainPrice = c.selectedProcedure.value!.price;

    if (percentage > 100) {
      var string = mainPrice.round().toString();
      discountPercentage.text = '100';
      discount.text = string;
      c.finalPrice.text = string;
      NotifyService.notice(
        title: 'Invalid discount',
        body: 'Discount can not be more than 100%',
      );
      return;
    }

    final divBy100 = mainPrice / 100;
    // if (percentage <= 100) {
    discount.text = (divBy100 * percentage).round().toString();
    c.finalPrice.text =
        (mainPrice - (divBy100 * percentage)).round().toString();
    // } else {
    //   discount.text = (divBy100 * percentage).toString();
    //   c.finalPrice.text = (divBy100 * percentage).toString();
    // }

    checkFinalPriceType();
  }

  void onDiscount(String val) {
    final c = Get.find<ProcedureFormController>();
    if (val.isEmpty) {
      discountPercentage.text = '';
      c.finalPrice.text = c.selectedProcedure.value!.price.toString();

      checkFinalPriceType();
      return;
    }
    final discountPrice = double.parse(val);
    final mainPrice = c.selectedProcedure.value!.price;

    if (discountPrice > mainPrice) {
      var string = mainPrice.round().toString();
      discountPercentage.text = '100';
      c.finalPrice.text = string;
      discount.text = string;
      NotifyService.notice(
        title: 'Invalid discount',
        body:
            'Discount can not be more than $mainPrice ${ConfigService.to.currency}',
      );

      return;
    }

    final percentage = ((discountPrice) / mainPrice * 100).round();
    discountPercentage.text = percentage.round().toString();

    // if (mainPrice >= discountPrice) {
    c.finalPrice.text = (mainPrice - discountPrice).round().toString();
    // } else {
    //   c.finalPrice.text = discountPrice.toString();
    // }

    checkFinalPriceType();
  }
}

class _ChooseTeethDialog extends StatefulWidget {
  const _ChooseTeethDialog(this.selected);

  final List<String> selected;

  @override
  State<_ChooseTeethDialog> createState() => _ChooseTeethDialogState();
}

class _ChooseTeethDialogState extends State<_ChooseTeethDialog> {
  List<String> selected = [];
  bool childView = false;

  @override
  void initState() {
    selected = widget.selected;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BasicDialog(
      width: 1200,
      title: t.visitProcedureForm.chooseTeeth,
      onClose: (close) {
        close(selected);
      },
      actions: [
        TextButton(
          child: Text(t.switchView),
          onPressed: () {
            setState(() {
              childView = !childView;
            });
          },
        )
      ],
      children: [
        TeethViewComponent(
          childTeeth: childView,
          selectedTeeth: selected,
          onTap: (position, status) {
            final isSelected = selected.contains(position);
            setState(() {
              if (isSelected) {
                selected.remove(position);
              } else {
                selected.add(position);
              }
            });
          },
        ),
      ],
    );
  }
}

class _InventoryConsumptionSection extends StatelessWidget {
  const _InventoryConsumptionSection();

  @override
  Widget build(BuildContext context) {
    final c = Get.find<ProcedureFormController>();

    return Obx(() {
      if (c.selectedProcedure.value == null) {
        return const SizedBox.shrink();
      }

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Inventory Consumption',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF364152),
            ),
          ),
          const SizedBox(height: 8),
          if (c.isLoadingInventoryLinks.value)
            const Center(child: CircularProgressIndicator())
          else if (c.inventoryLinks.isEmpty)
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: const Text(
                'No inventory items linked to this procedure template.',
                style: TextStyle(
                  color: Colors.grey,
                  fontStyle: FontStyle.italic,
                ),
              ),
            )
          else
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  // Header
                  Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade50,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(8),
                        topRight: Radius.circular(8),
                      ),
                    ),
                    child: const Row(
                      children: [
                        Expanded(
                          flex: 3,
                          child: Text(
                            'Item',
                            style: TextStyle(fontWeight: FontWeight.w600),
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Text(
                            'Stock',
                            style: TextStyle(fontWeight: FontWeight.w600),
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Text(
                            'Quantity',
                            style: TextStyle(fontWeight: FontWeight.w600),
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Items
                  for (int i = 0; i < c.inventoryLinks.length; i++)
                    _InventoryConsumptionItem(
                      link: c.inventoryLinks[i],
                      isLast: i == c.inventoryLinks.length - 1,
                    ),
                ],
              ),
            ),
        ],
      );
    });
  }
}

class _InventoryConsumptionItem extends StatelessWidget {
  const _InventoryConsumptionItem({
    required this.link,
    required this.isLast,
  });

  final InventoryLink link;
  final bool isLast;

  @override
  Widget build(BuildContext context) {
    final c = Get.find<ProcedureFormController>();
    final quantityController = TextEditingController();

    return Obx(() {
      final currentQuantity =
          c.inventoryConsumptions[link.inventoryItemId] ?? 0;
      if (quantityController.text != currentQuantity.toString()) {
        quantityController.text = currentQuantity.toString();
      }

      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          border: isLast
              ? null
              : Border(
                  bottom: BorderSide(color: Colors.grey.shade200),
                ),
        ),
        child: Row(
          children: [
            Expanded(
              flex: 3,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    link.item.name,
                    style: const TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: 14,
                    ),
                  ),
                  Text(
                    'Unit: ${link.item.unit}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              flex: 2,
              child: Text(
                '${link.item.quantity}',
                style: TextStyle(
                  fontSize: 14,
                  color: link.item.quantity < link.item.warningQuantity
                      ? Colors.orange
                      : Colors.black,
                  fontWeight: link.item.quantity < link.item.warningQuantity
                      ? FontWeight.w600
                      : FontWeight.normal,
                ),
              ),
            ),
            Expanded(
              flex: 2,
              child: TextFormField(
                controller: quantityController,
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                ],
                decoration: const InputDecoration(
                  isDense: true,
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                ),
                onChanged: (value) {
                  final quantity = int.tryParse(value) ?? 0;
                  c.updateInventoryConsumption(link.inventoryItemId, quantity);
                },
              ),
            ),
          ],
        ),
      );
    });
  }
}
