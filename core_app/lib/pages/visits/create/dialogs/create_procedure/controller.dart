import 'package:core_app/core/api/procedures.dart';
import 'package:core_app/core/api/specialities.dart';
import 'package:core_app/core/api/users.dart';
import 'package:core_app/core/api/inventory_links.dart';
import 'package:core_app/core/models/procedure.dart';
import 'package:core_app/core/models/speciality.dart';
import 'package:core_app/core/models/user.dart';
import 'package:core_app/core/models/inventory_link.dart';
import 'package:core_app/services/auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class IncompletedProcedure {
  final String dentistId;
  final String templateId;
  final String notes;
  final String nextVisit;
  final double finalPrice;
  final bool toothRemoved;
  final String speciality;
  final String procedure;
  final List<String> toothNumber;
  final List<InventoryConsumption>? inventoryConsumptions;

  const IncompletedProcedure({
    required this.dentistId,
    required this.finalPrice,
    required this.nextVisit,
    required this.notes,
    required this.procedure,
    required this.speciality,
    required this.templateId,
    required this.toothRemoved,
    required this.toothNumber,
    this.inventoryConsumptions,
  });
}

class ProcedureFormController extends GetxController {
  final specialities = <SpecialityTemplateModel>[].obs;
  final procedures = <ProcedureTemplateModel>[];
  final filtredProcedures = <ProcedureTemplateModel>[].obs;
  final allDentists = <UserModel>[].obs;

  final selectedDentist = Rx<UserModel?>(null);
  final selectedSpeciality = Rx<SpecialityTemplateModel?>(null);
  final selectedProcedure = Rx<ProcedureTemplateModel?>(null);

  final teethRemoved = false.obs;
  final notes = TextEditingController();
  final nextVisit = TextEditingController();
  final procedureName = TextEditingController();
  final specialityName = TextEditingController();

  final teethNumber = <String>[].obs;

  int get priceMultiplier => teethNumber.length;
  final finalPrice = TextEditingController(text: '0');

  // Inventory links management
  final inventoryLinks = <InventoryLink>[].obs;
  final inventoryConsumptions = <String, int>{}.obs; // itemId -> quantity
  final isLoadingInventoryLinks = false.obs;

  void fetchAPI() async {
    specialities.value = await SpecialitiesAPI.list();
    procedures.clear();
    procedures.addAll(await ProcedureTemplatesAPI.list());
  }

  void filterProcedures() {
    filtredProcedures.value = procedures
        .where((e) => e.specialityId == selectedSpeciality.value?.id)
        .toList();
  }

  Future<void> fetchInventoryLinks(String procedureTemplateId) async {
    isLoadingInventoryLinks.value = true;
    try {
      inventoryLinks.value = await InventoryLinksAPI.list(procedureTemplateId);
      // Initialize consumption quantities with default values
      inventoryConsumptions.clear();
      for (var link in inventoryLinks) {
        inventoryConsumptions[link.inventoryItemId] = link.quantity;
      }
    } catch (e) {
      inventoryLinks.clear();
      inventoryConsumptions.clear();
    } finally {
      isLoadingInventoryLinks.value = false;
    }
  }

  void updateInventoryConsumption(String itemId, int quantity) {
    if (quantity > 0) {
      inventoryConsumptions[itemId] = quantity;
    } else {
      inventoryConsumptions.remove(itemId);
    }
  }

  List<InventoryConsumption> getInventoryConsumptions() {
    return inventoryConsumptions.entries
        .where((entry) => entry.value > 0)
        .map((entry) =>
            InventoryConsumption(itemId: entry.key, quantity: entry.value))
        .toList();
  }

  @override
  Future<void> onInit() async {
    super.onInit();
    final users = await UsersAPI.list();
    allDentists.value = users.where((e) => e.isDentist).toList();
    if (AuthService.getUser.isDentist) {
      selectedDentist.value =
          allDentists.firstWhere((e) => e.id == AuthService.getUser.id);
    } else if (allDentists.length == 1) {
      selectedDentist.value = allDentists.first;
    }
    fetchAPI();
  }
}
