import 'package:core_app/core/api/patients.dart';
import 'package:core_app/core/models/appointment.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:path/path.dart' as path;
import 'package:core_app/core/api/procedures.dart';
import 'package:core_app/core/api/visits.dart';
import 'package:core_app/core/helpers/api_handler.dart';

import 'package:core_app/pages/payments/create/create.dart';

import 'package:file_picker/file_picker.dart';

import 'dialogs/create_procedure/controller.dart';

class CreateVisitController extends GetxController {
  final AppointmentModel repo;
  final bool canViewProfile;
  CreateVisitController(this.repo, this.canViewProfile);

  final tempHide = false.obs;
  final comments = ''.obs;
  final diagnosis = ''.obs;
  final nextVisit = ''.obs;
  final files = <PlatformFile>[].obs;
  final treatments = <String>[].obs;
  final procedures = <IncompletedProcedure>[].obs;
  final treatmentController = TextEditingController();

  void save() {
    tryAPI(() async {
      final treatmentsList = treatments;

      if (treatmentController.text.isNotEmpty) {
        treatmentsList.add(treatmentController.text);
      }

      final v = await VisitsAPI.create(
        branchId: repo.branchId,
        patientId: repo.patientId,
        diagnosis: diagnosis.value,
        nextVisit: nextVisit.value,
        appointmentId: repo.id,
        comments: comments.value,
        treatments: treatmentsList,
      );

      for (var p in procedures) {
        for (var tooth in p.toothNumber) {
          await ProceduresAPI.create(
            v.id,
            dentstId: p.dentistId,
            toothNumber: tooth.toLowerCase(),
            speciality: p.speciality,
            procedure: p.procedure,
            nextVisit: p.nextVisit,
            procedureTemplatId: p.templateId,
            finalPrice: p.finalPrice,
            toothRemoved: p.toothRemoved,
            notes: p.notes,
          );
        }
      }

      await PatientsAPI.uploadFile(
        repo.patientId,
        v.id,
        files.fold<Map<String, Object>>({}, (previousValue, e) {
          previousValue
              .addAll({path.basename(e.name): kIsWeb ? e.bytes! : e.path!});
          return previousValue;
        }),
      );
      Get.back(result: v);

      final totalProceduresPrice =
          procedures.fold<double>(0.0, (previousValue, element) {
        final totalPrice = element.finalPrice * element.toothNumber.length;
        return previousValue + totalPrice;
      });
      Get.dialog(CreatePaymentDialog(
        patientId: v.patientId,
        branchId: v.branchId,
        visitId: v.id,
        treatments: v.treatments ?? [],
        hint: 'Total Procedures price: $totalProceduresPrice',
      ));
    });
  }
}
