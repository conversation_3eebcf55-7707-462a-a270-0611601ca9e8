import 'package:core_app/components/buttons/icon.dart';
import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/core/utils/i18n/translations.g.dart';
import 'package:core_app/pages/visits/create/controller.dart';
import 'package:core_app/pages/visits/create/dialogs/create_procedure/controller.dart';
import 'package:core_app/pages/visits/create/dialogs/create_procedure/create_procedure.dart';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ListVisitProcedureComponent extends StatelessWidget {
  const ListVisitProcedureComponent({super.key});

  @override
  Widget build(BuildContext context) {
    final c = Get.find<CreateVisitController>();
    return Obx(
      () => Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                t.visit.procedures,
                style: const TextStyle(
                  color: ThemeColors.notion,
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
              ),
              Material(
                color: Colors.transparent,
                child: IconButton(
                  onPressed: () async {
                    c.tempHide.value = true;
                    IncompletedProcedure? wait =
                        await Get.dialog(const ProcedureFormDialog());
                    if (wait != null) {
                      c.procedures.add(wait);
                    }
                    c.tempHide.value = false;
                  },
                  icon: const Icon(
                    Icons.add,
                    size: 21,
                    color: ThemeColors.notion,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 7),
          if (c.procedures.isEmpty)
            SizedBox(
              height: 40,
              child: Center(
                child: Text(
                  t.visitForm.noProcedures,
                  style: const TextStyle(
                    color: ThemeColors.notion,
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                  ),
                ),
              ),
            )
          else
            for (var p in c.procedures)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: ListTile(
                  selected: p.toothRemoved,
                  selectedColor: Colors.red.shade200,
                  horizontalTitleGap: 0,
                  tileColor: Colors.white,
                  title: Text(p.procedure),
                  subtitle: Text(
                      "${p.speciality} | ${p.toothNumber.join(', ').toUpperCase()}"),
                  trailing: XIconButton(
                    icon: Icons.delete_outline_rounded,
                    onPressed: () {
                      c.procedures.remove(p);
                    },
                  ),
                ),
              ),
        ],
      ),
    );
  }
}
