import 'package:core_app/components/buttons/icon.dart';
import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/core/utils/i18n/translations.g.dart';
import 'package:core_app/pages/visits/create/controller.dart';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../dialogs/treatment.dart';

class TreatmentsListFormComponent extends StatelessWidget {
  const TreatmentsListFormComponent({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final focus = FocusNode();
    final controller = Get.find<CreateVisitController>();
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: TextFormField(
                maxLines: 1,
                focusNode: focus,
                controller: controller.treatmentController,
                onFieldSubmitted: (val) {
                  if (val.isNotEmpty) {
                    controller.treatments.add(val);
                    controller.treatmentController.text = '';
                    focus.requestFocus();
                  }
                },
                decoration: InputDecoration(
                  labelText: t.visit.treatments,
                ),
              ),
            ),
            const SizedBox(width: 7),
            XIconButton(
              icon: Icons.add,
              onPressed: () {
                if (controller.treatmentController.text.isNotEmpty) {
                  controller.treatments
                      .add(controller.treatmentController.text);
                  controller.treatmentController.text = '';
                }
              },
            ),
          ],
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 23),
          child: Obx(() => Column(
                children: [
                  for (var i = 0; i < controller.treatments.length; i++)
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 7),
                      child: Material(
                        color: Colors.white,
                        child: ListTile(
                          onTap: () async {
                            String? f = await Get.dialog(
                                EditVisitTreatmentFormDialog(
                                    controller.treatments[i].capitalizeFirst!));
                            if (f == null) return;
                            controller.treatments
                                .remove(controller.treatments[i]);
                            controller.treatments.insert(i, f);
                          },
                          horizontalTitleGap: 0,
                          contentPadding: EdgeInsets.zero,
                          dense: true,
                          leading: Text(
                            "  ${(i + 1)}.",
                            style: TextStyle(
                              fontSize: 15,
                              fontWeight: FontWeight.w600,
                              color: ThemeColors.text.withValues(alpha: 0.71),
                            ),
                          ),
                          title: Text(
                            controller.treatments[i].capitalizeFirst!,
                            style: TextStyle(
                              fontSize: 15,
                              fontWeight: FontWeight.w600,
                              color: ThemeColors.text.withValues(alpha: 0.71),
                            ),
                          ),
                          trailing: XIconButton(
                            icon: Icons.delete_outline_rounded,
                            onPressed: () {
                              controller.treatments
                                  .remove(controller.treatments[i]);
                            },
                          ),
                        ),
                      ),
                    ),
                ],
              )),
        ),
      ],
    );
  }
}
