import 'package:core_app/components/buttons/icon.dart';
import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/core/utils/i18n/translations.g.dart';
import 'package:core_app/pages/visits/create/controller.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:path/path.dart' as path;

class PickedFilesComponent extends StatelessWidget {
  const PickedFilesComponent({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<CreateVisitController>();
    return Obx(() => Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  t.visit.files,
                  style: const TextStyle(
                    color: ThemeColors.notion,
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                  ),
                ),
                Material(
                  color: Colors.transparent,
                  child: IconButton(
                    icon: const Icon(
                      Icons.add,
                      size: 21,
                      color: ThemeColors.notion,
                    ),
                    onPressed: () async {
                      final FilePickerResult? result = await FilePicker.platform.pickFiles(
                        allowMultiple: true,
                        allowedExtensions: ['jpg', 'png', 'pdf'],
                        type: FileType.custom,
                      );
                      if (result == null || result.files.isEmpty) return;
                      controller.files.addAll(result.files);
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (controller.files.isEmpty)
              SizedBox(
                height: 40,
                child: Center(
                  child: Text(
                    t.visitForm.noFiles,
                    style: const TextStyle(
                      color: ThemeColors.notion,
                      fontWeight: FontWeight.w500,
                      fontSize: 14,
                    ),
                  ),
                ),
              )
            else
              ListView.builder(
                shrinkWrap: true,
                itemCount: controller.files.length,
                itemBuilder: (context, index) {
                  final file = controller.files[index];
                  var basename = path.basename(file.name);
                  return ListTile(
                    horizontalTitleGap: 0,
                    leading: Icon(
                      !basename.isPDFFileName
                          ? Icons.image_outlined
                          : Icons.picture_as_pdf_outlined,
                      color: ThemeColors.textLight,
                    ),
                    title: Text(
                      basename,
                      style: const TextStyle(
                        color: ThemeColors.text,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    trailing: XIconButton(
                      icon: Icons.delete_outline_rounded,
                      onPressed: () {
                        controller.files.removeAt(index);
                      },
                    ),
                  );
                },
              )
          ],
        ));
  }
}
