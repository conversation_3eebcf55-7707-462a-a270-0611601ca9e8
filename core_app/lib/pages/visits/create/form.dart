import 'package:core_app/components/buttons/text.dart';
import 'package:core_app/components/dialogs/basic.dart';
import 'package:core_app/core/models/appointment.dart';
import 'package:core_app/core/utils/i18n/translations.g.dart';
import 'package:core_app/pages/visits/create/controller.dart';
import 'package:core_app/routes/navigation.dart';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:core_app/pages/patients/profile/profile.dart'
    deferred as patient_profile;
import 'package:core_app/pages/patients/profile/controller.dart'
    deferred as patient_profile_controller;

import 'components/treatments_fields.dart';
import 'components/picked_files.dart';
import 'components/list_procedures.dart';

class CreateVisitDialog extends StatelessWidget {
  const CreateVisitDialog(this.repo, this.canViewProfile, {super.key});

  final AppointmentModel repo;
  final bool canViewProfile;

  @override
  Widget build(BuildContext context) {
    final c = Get.put(CreateVisitController(repo, canViewProfile));
    return Obx(() => AnimatedOpacity(
          duration: kThemeAnimationDuration,
          opacity: c.tempHide.value ? 0 : 1,
          child: BasicDialog(
            title: t.visitForm.createVisit,
            children: [
              if (canViewProfile)
                TextButton(
                  child: Text(t.visitForm.viewPatientProfile),
                  onPressed: () async {
                    // Navigation.to(Routes.PatientProfile, {
                    //   'patientID': repo.patientId,
                    // });
                    Navigation.query['patientID'] = repo.patientId;
                    await patient_profile.loadLibrary();
                    await patient_profile_controller.loadLibrary();
                    Get.put(
                        patient_profile_controller.PatientProfileController(
                            repo.patientId),
                        tag: repo.patientId);
                    Navigation.push(
                        patient_profile.PateintProfilePage(repo.patientId));
                  },
                ),
              TextFormField(
                maxLines: null,
                onChanged: c.diagnosis,
                decoration: InputDecoration(
                  labelText: t.visit.diagnosis,
                ),
              ),
              const SizedBox(height: 13),
              const TreatmentsListFormComponent(),
              const SizedBox(height: 13),
              TextFormField(
                maxLines: null,
                onChanged: c.nextVisit,
                decoration: InputDecoration(
                  labelText: t.visit.nextVisit,
                ),
              ),
              const SizedBox(height: 13),
              TextFormField(
                onChanged: c.comments,
                maxLines: null,
                decoration: InputDecoration(
                  labelText: t.visit.comments,
                ),
              ),
              const SizedBox(height: 13),
              const ListVisitProcedureComponent(),
              const SizedBox(height: 7),
              const PickedFilesComponent(),
              const SizedBox(height: 16),
              Align(
                alignment: Alignment.centerRight,
                child: XTextButton(
                  title: t.buttonTxt.save,
                  onPressed: c.save,
                ),
              ),
            ],
          ),
        ));
  }
}
