import 'package:core_app/components/dialogs/basic.dart';
import 'package:core_app/core/api/custom_payments.dart';
import 'package:core_app/core/helpers/api_handler.dart';
import 'package:core_app/core/models/payment.dart';
import 'package:core_app/routes/navigation.dart';

import 'package:core_app/services/notify/messenger.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class CreateCustomPaymentMethodController extends GetxController {
  final customPaymentMethods = <String>[].obs;
  final customPaymentMethodName = ''.obs;
  final customPaymentMethodPercentage = ''.obs;
  final customPaymentMethodFlatFee = ''.obs;

  final CustomPaymentModel? customPayment;
  CreateCustomPaymentMethodController({this.customPayment}) {
    if (customPayment != null) {
      customPaymentMethodName(customPayment!.name);
      customPaymentMethodPercentage(customPayment!.percentageFee.toString());
      customPaymentMethodFlatFee(customPayment!.flatFee.toString());
    }
  }

  void save() async {
    final percentageFee = double.tryParse(customPaymentMethodPercentage.value);
    final flatFee = double.tryParse(customPaymentMethodFlatFee.value);
    if (percentageFee == null || flatFee == null) {
      NotifyService.notice(
        title: 'Invalid Input',
        body: 'Please enter a valid percentage and flat fee',
      );
      return;
    }
    if (customPayment != null) {
      tryAPI(() async {
        await CustomPaymentsAPI.update(
          id: customPayment!.id,
          name: customPaymentMethodName.value,
          percentageFee: percentageFee,
          flatFee: flatFee,
        );
        Navigation.back(true);
      });
      return;
    }
    tryAPI(() async {
      await CustomPaymentsAPI.create(
        name: customPaymentMethodName.value,
        percentageFee: percentageFee,
        flatFee: flatFee,
      );
      Navigation.back(true);
    });
  }
}

class CreateCustomPayment extends StatelessWidget {
  const CreateCustomPayment({super.key, this.customPayment});

  final CustomPaymentModel? customPayment;

  @override
  Widget build(BuildContext context) {
    final c = Get.put(
        CreateCustomPaymentMethodController(customPayment: customPayment));
    return BasicDialog(
      title: 'Create Custom Payment Method',
      children: [
        TextFormField(
          initialValue: customPayment?.name,
          onChanged: c.customPaymentMethodName,
          decoration: const InputDecoration(
            labelText: 'Name',
          ),
        ),
        const SizedBox(height: 13),
        TextFormField(
          readOnly: customPayment != null,
          initialValue: customPayment?.percentageFee.toString(),
          onChanged: c.customPaymentMethodPercentage,
          decoration: const InputDecoration(
            labelText: 'Percentage',
          ),
        ),
        const SizedBox(height: 13),
        TextFormField(
          readOnly: customPayment != null,
          initialValue: customPayment?.flatFee.toString(),
          onChanged: c.customPaymentMethodFlatFee,
          decoration: const InputDecoration(
            labelText: 'Flat Fee',
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            const Spacer(),
            const SizedBox(width: 16),
            ElevatedButton(
              onPressed: c.save,
              child: const Text('Save'),
            ),
          ],
        ),
      ],
    );
  }
}
