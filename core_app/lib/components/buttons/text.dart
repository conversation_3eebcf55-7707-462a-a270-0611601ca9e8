import 'package:core_app/core/constants/colors.dart';
import 'package:flutter/material.dart';

class XTextButton extends StatefulWidget {
  const XTextButton({super.key, this.color, this.onPressed, required this.title});

  final String title;
  final Color? color;
  final Function()? onPressed;

  @override
  State<XTextButton> createState() => _XIconButtonState();
}

class _XIconButtonState extends State<XTextButton> {
  bool _isHovering = false;
  bool _loading = false;

  bool get isHovering => _isHovering || _loading;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () async {
        if (_loading) return;
        setState(() => _loading = true);
        await widget.onPressed?.call();
        if (mounted) setState(() => _loading = false);
      },
      child: MouseRegion(
        cursor: SystemMouseCursors.click,
        onEnter: (_) => setState(() => _isHovering = true),
        onExit: (_) => setState(() => _isHovering = false),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          padding: const EdgeInsets.symmetric(
            horizontal: 10,
            vertical: 5,
          ),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(9),
            color: isHovering ? ThemeColors.primaryDark : ThemeColors.primaryLighter,
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                widget.title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: isHovering ? ThemeColors.primaryLighter : ThemeColors.primaryDark,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
