import 'package:core_app/core/constants/colors.dart';
import 'package:flutter/material.dart';

class XIconButton extends StatefulWidget {
  const XIconButton({super.key, required this.icon, this.onPressed});

  final IconData icon;
  final Function()? onPressed;

  @override
  State<XIconButton> createState() => _XIconButtonState();
}

class _XIconButtonState extends State<XIconButton> {
  bool _isHovering = false;
  bool _loading = false;

  bool get isHovering => _isHovering || _loading;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () async {
        if (_loading) return;
        setState(() => _loading = true);
        await widget.onPressed?.call();
        if (mounted) setState(() => _loading = false);
      },
      child: MouseRegion(
        cursor: SystemMouseCursors.click,
        onEnter: (_) => setState(() => _isHovering = true),
        onExit: (_) => setState(() => _isHovering = false),
        child: AnimatedContainer(
          padding: const EdgeInsets.all(5),
          duration: const Duration(milliseconds: 300),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(9),
            color: isHovering ? ThemeColors.primaryDark : ThemeColors.primaryLighter,
          ),
          child: Icon(
            widget.icon,
            size: 24,
            color: isHovering ? ThemeColors.primaryLighter : ThemeColors.primaryDark,
          ),
        ),
      ),
    );
  }
}
