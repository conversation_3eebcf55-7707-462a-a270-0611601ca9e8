import 'package:flutter/material.dart';

class AutoPopupMenuItem {
  final void Function() onTap;
  final String name;
  AutoPopupMenuItem({
    required this.name,
    required this.onTap,
  });
}

class AutoPopupMenu extends StatelessWidget {
  const AutoPopupMenu({
    super.key,
    this.size = 24,
    required this.items,
  });

  final double size;
  final List<AutoPopupMenuItem> items;

  @override
  Widget build(BuildContext context) {
    return PopupMenuButton(
      clipBehavior: Clip.antiAlias,
      position: PopupMenuPosition.under,
      surfaceTintColor: Colors.white,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(7)),
      child: Material(
        color: Colors.transparent,
        clipBehavior: Clip.antiAlias,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(11)),
        child: Padding(
          padding: const EdgeInsets.all(3),
          child: Icon(
            Icons.more_vert,
            size: size,
            color: const Color(0xFF677A87),
          ),
        ),
      ),
      onSelected: (value) {
        items.firstWhere((e) => e.name == value).onTap();
      },
      itemBuilder: (context) => [
        for (var i in items)
          PopupMenuItem(
            value: i.name,
            child: Text(i.name),
          ),
      ],
    );
  }
}
