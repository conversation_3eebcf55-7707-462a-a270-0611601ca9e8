import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/core/utils/responsive.dart';
import 'package:flutter/material.dart';

class XTextIconButton extends StatefulWidget {
  const XTextIconButton({
    super.key,
    required this.icon,
    this.onPressed,
    required this.title,
    this.iconSize = 24,
    this.fontSize = 16,
    this.minimizable = true,
  });

  final String title;
  final IconData icon;
  final Function()? onPressed;
  final double iconSize;
  final double fontSize;
  final bool minimizable;

  @override
  State<XTextIconButton> createState() => _XIconButtonState();
}

class _XIconButtonState extends State<XTextIconButton> {
  bool _isHovering = false;
  bool _loading = false;

  bool get isHovering => _isHovering || _loading;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () async {
        if (_loading) return;
        setState(() => _loading = true);
        await widget.onPressed?.call();
        if (mounted) setState(() => _loading = false);
      },
      child: MouseRegion(
        cursor: SystemMouseCursors.click,
        onEnter: (_) => setState(() => _isHovering = true),
        onExit: (_) => setState(() => _isHovering = false),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          padding: const EdgeInsets.symmetric(
            horizontal: 10,
            vertical: 5,
          ),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(9),
            color: isHovering ? ThemeColors.primaryDark : ThemeColors.primaryLighter,
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Icon(
                widget.icon,
                size: widget.iconSize,
                color: isHovering ? ThemeColors.primaryLighter : ThemeColors.primaryDark,
              ),
              if (context.mobileView == false || !widget.minimizable) const SizedBox(width: 5),
              if (context.mobileView == false || !widget.minimizable)
                Text(
                  widget.title,
                  style: TextStyle(
                    fontSize: widget.fontSize,
                    fontWeight: FontWeight.w600,
                    color: isHovering ? ThemeColors.primaryLighter : ThemeColors.primaryDark,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
