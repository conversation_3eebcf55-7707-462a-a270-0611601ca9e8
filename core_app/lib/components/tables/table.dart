import 'dart:math';

import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/core/utils/responsive.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class TableColumn<T> {
  const TableColumn({
    this.flex = 1,
    this.minWidth = 0,
    required this.title,
    required this.builder,
  });

  final int flex;
  final String title;
  final double minWidth;
  final Widget Function(T data) builder;

  static Widget stringBuilder(String s, {bool clip = true}) {
    return Text(
      s,
      overflow: clip ? TextOverflow.ellipsis : TextOverflow.visible,
      style: const TextStyle(
        fontSize: 14,
        color: Color(0xFF364152),
        fontWeight: FontWeight.w500,
      ),
    );
  }
}

class TableComponent<T> extends StatelessWidget {
  const TableComponent({
    super.key,
    required this.title,
    this.onRowTap,
    this.actions = const [],
    this.data = const [],
    required this.columns,
    this.getColor,
  });

  final String title;
  final List<Widget> actions;
  final List<T> data;
  final void Function(T data)? onRowTap;
  final List<TableColumn<T>> columns;
  final Function(T index)? getColor;

  @override
  Widget build(BuildContext context) {
    final int flexSum = columns
        .map((e) => e.flex)
        .fold(0, (previousValue, e) => previousValue + e);
    final double flexFactor = flexSum == 0 ? 1 : 1 / flexSum;

    final actionsList = [
      for (final action in actions)
        Padding(
          padding: context.mobileView
              ? EdgeInsets.zero
              : const EdgeInsets.only(left: 16),
          child: action,
        ),
    ];
    return Container(
      width: double.infinity,
      clipBehavior: Clip.antiAlias,
      decoration: BoxDecoration(
        color: ThemeColors.bg,
        borderRadius: BorderRadius.circular(13),
      ),
      child: Column(
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 28, vertical: 28),
            child: Wrap(
              alignment: WrapAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 24,
                    color: ThemeColors.text,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (context.mobileView) const SizedBox(height: 16),
                if (actions.isNotEmpty)
                  Wrap(
                    spacing: 11,
                    runSpacing: 11,
                    children: actionsList,
                  ),
              ],
            ),
          ),
          LayoutBuilder(builder: (context, cons) {
            return ScrollConfiguration(
              behavior: ScrollConfiguration.of(context)
                  .copyWith(dragDevices: PointerDeviceKind.values.toSet()),
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // const SizedBox(height: 21),
                    Container(
                      constraints: const BoxConstraints(minHeight: 65),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 28, vertical: 14),
                      decoration: BoxDecoration(
                        border: Border(
                          top: BorderSide(
                            width: .2,
                            color:
                                context.theme.dividerTheme.color ?? Colors.grey,
                          ),
                          bottom: BorderSide(
                            width: .2,
                            color:
                                context.theme.dividerTheme.color ?? Colors.grey,
                          ),
                        ),
                      ),
                      child: Row(
                        children: [
                          for (final col in columns)
                            SizedBox(
                              width: max(
                                  col.minWidth,
                                  (cons.maxWidth - 56) *
                                      col.flex *
                                      flexFactor), // 56 = 28 * 2, is the parent horizontal padding
                              child: Text(
                                col.title,
                                style: const TextStyle(
                                  fontSize: 16,
                                  color: ThemeColors.text,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                    for (final row in data)
                      Material(
                        key: ValueKey(row),
                        color: getColor?.call(row) ?? Colors.transparent,
                        child: InkWell(
                          onTap: onRowTap != null ? () => onRowTap!(row) : null,
                          child: Container(
                            constraints: const BoxConstraints(minHeight: 60),
                            decoration: BoxDecoration(
                              border: Border(
                                bottom: BorderSide(
                                  width: .2,
                                  color: context.theme.dividerTheme.color ??
                                      Colors.grey,
                                ),
                              ),
                            ),
                            child: Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 28),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  for (final col in columns)
                                    SizedBox(
                                      width: max(
                                          col.minWidth,
                                          (cons.maxWidth - 56) *
                                              col.flex *
                                              flexFactor), // 56 = 28 * 2, is the parent horizontal padding
                                      child: col.builder(row),
                                    ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            );
          }),
        ],
      ),
    );
  }
}
