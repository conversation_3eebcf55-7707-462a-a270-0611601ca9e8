import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/core/constants/procedures_colors.dart';
import 'package:core_app/core/models/patient.dart';
import 'package:core_app/core/utils/responsive.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';

typedef CallbackToothTap = Function(String position, TeethStatus? status);

class _TeethViewData extends InheritedWidget {
  const _TeethViewData({
    required this.onTap,
    required super.child,
    required this.childTeeth,
    required this.teethStatus,
    required this.selectedTeeth,
  });

  final bool childTeeth;
  final CallbackToothTap? onTap;
  final List<String> selectedTeeth;
  final Map<String, TeethStatus> teethStatus;

  static _TeethViewData of(BuildContext context) {
    final _TeethViewData? result =
        context.dependOnInheritedWidgetOfExactType<_TeethViewData>();
    assert(result != null, 'No _TeethViewData found in context');
    return result!;
  }

  @override
  bool updateShouldNotify(_TeethViewData oldWidget) =>
      childTeeth != oldWidget.childTeeth ||
      onTap != oldWidget.onTap ||
      teethStatus != oldWidget.teethStatus ||
      selectedTeeth != oldWidget.selectedTeeth ||
      selectedTeeth.length != oldWidget.selectedTeeth.length;
}

class TeethViewComponent extends StatelessWidget {
  const TeethViewComponent({
    super.key,
    this.onTap,
    this.margin,
    required this.childTeeth,
    this.teethStatus = const {},
    this.width = double.infinity,
    this.selectedTeeth = const [],
  });

  final double width;
  final bool childTeeth;
  final EdgeInsets? margin;
  final CallbackToothTap? onTap;
  final List<String> selectedTeeth;
  final Map<String, TeethStatus> teethStatus;

  @override
  Widget build(BuildContext context) {
    final teethCount = childTeeth ? 10 : 16;
    return Container(
      width: width,
      margin: margin,
      decoration: BoxDecoration(
        color: ThemeColors.primary,
        borderRadius: BorderRadius.circular(3),
      ),
      child: _TeethViewData(
        onTap: onTap,
        childTeeth: childTeeth,
        teethStatus: teethStatus,
        selectedTeeth: selectedTeeth,
        child: LayoutBuilder(
          builder: (ctx, constraints) {
            final column = Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    for (int i = 0; i < teethCount; i++)
                      _ToothItemBuilder(
                        isUpperTooth: true,
                        realToothNumber: i + 1,
                      ),
                  ],
                ),
                const SizedBox(height: 27),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    for (int i = 0; i < teethCount; i++)
                      _ToothItemBuilder(
                        isUpperTooth: false,
                        realToothNumber: i + 1,
                      ),
                  ],
                ),
              ],
            );

            if (constraints.maxWidth >= (teethCount * 75 + 82)) {
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 16),
                child: column,
              );
            }
            return ScrollConfiguration(
              behavior: ScrollConfiguration.of(context)
                  .copyWith(dragDevices: PointerDeviceKind.values.toSet()),
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                padding:
                    const EdgeInsets.symmetric(horizontal: 41, vertical: 16),
                child: column,
              ),
            );
          },
        ),
      ),
    );
  }
}

class _ToothItemBuilder extends StatelessWidget {
  const _ToothItemBuilder({
    this.isUpperTooth = false,
    required this.realToothNumber,
  });
  final bool isUpperTooth;
  final int realToothNumber;

  @override
  Widget build(BuildContext context) {
    final c = _TeethViewData.of(context);
    var position = getPosition(realToothNumber, c.childTeeth);
    var toothType = getToothType(realToothNumber, c.childTeeth);
    return Opacity(
      opacity: c.selectedTeeth.contains(position) ? .51 : 1,
      child: _ToothItem(
        name: toothType,
        position: position,
        isUpperTooth: isUpperTooth,
        status: c.teethStatus[position],
      ),
    );
  }

  String getPosition(int realToothNumber, bool childTooth) {
    String toothPosition = "";
    final i = realToothNumber - 1;

    const upperRight = 'ur';
    const upperLeft = 'ul';
    const lowerRight = 'lr';
    const lowerLeft = 'll';

    if (childTooth) {
      const abc = 'abcde';
      if (isUpperTooth) {
        // if isUpperTooth
        if (realToothNumber <= 5) {
          toothPosition = "$upperRight${abc[4 - i]}";
        }
        if (realToothNumber > 5 && realToothNumber <= 10) {
          toothPosition = "$upperLeft${abc[i - 5]}";
        }
      } else {
        // if !isUpperTooth
        if (realToothNumber <= 5) {
          toothPosition = "$lowerRight${abc[4 - i]}";
        }
        if (realToothNumber > 5 && realToothNumber <= 10) {
          toothPosition = "$lowerLeft${abc[i - 5]}";
        }
      }
      return toothPosition;
    }

    if (isUpperTooth) {
      // if isUpperTooth
      if (realToothNumber <= 8) {
        toothPosition = "$upperRight${8 - i}";
      }
      if (realToothNumber > 8 && realToothNumber <= 16) {
        toothPosition = "$upperLeft${realToothNumber - 8}";
      }
    } else {
      // if !isUpperTooth
      if (realToothNumber <= 8) {
        toothPosition = "$lowerRight${8 - i}";
      }
      if (realToothNumber > 8 && realToothNumber <= 16) {
        toothPosition = "$lowerLeft${realToothNumber - 8}";
      }
    }
    return toothPosition;
  }

  String getToothType(int realToothNumber, bool childTooth) {
    const molar = 'molar';
    const canine = 'canine';
    const incisior = 'incisior';
    const premolar = 'premolar';

    if (childTooth) {
      String toothName = molar;
      if (realToothNumber >= 4 && realToothNumber <= 7) {
        toothName = incisior;
      }
      if (realToothNumber == 8 || realToothNumber == 3) {
        toothName = canine;
      }
      return toothName;
    }
    String toothName = canine;
    if (realToothNumber >= 7 && realToothNumber <= 10) {
      toothName = incisior;
    }
    if (realToothNumber >= 1 && realToothNumber <= 3) {
      toothName = molar;
    }
    if (realToothNumber >= 14 && realToothNumber <= 16) {
      toothName = molar;
    }
    if (realToothNumber >= 4 && realToothNumber <= 5) {
      toothName = premolar;
    }
    if (realToothNumber >= 12 && realToothNumber <= 13) {
      toothName = premolar;
    }
    return toothName;
  }
}

class _ToothItem extends StatelessWidget {
  const _ToothItem({
    this.isUpperTooth = false,
    required this.name,
    required this.status,
    required this.position,
  });

  final String name;
  final String position;
  final bool isUpperTooth;
  final TeethStatus? status;

  @override
  Widget build(BuildContext context) {
    bool isMobile = context.mobileView;
    final c = _TeethViewData.of(context);
    final List<Color> colors = [];
    if (status != null) {
      if (status!.endo) colors.add(kEndoToothColor);
      if (status!.crown) colors.add(kCrownToothColor);
      if (status!.implant) colors.add(kImplantToothColor);
    }
    return MouseRegion(
      cursor: c.onTap == null ? MouseCursor.defer : SystemMouseCursors.click,
      child: GestureDetector(
        onTap: c.onTap == null ? null : () => c.onTap!(position, status),
        child: Container(
          width: isMobile ? 51 : 65,
          height: isMobile ? 51 : 65,
          clipBehavior: Clip.none,
          margin: const EdgeInsets.all(5),
          padding: const EdgeInsets.all(7),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(21),
            color: status?.removed ?? false
                ? kRemovedToothColor.withValues(alpha: 0.81)
                : ThemeColors.primaryLight,
          ),
          child: Stack(
            clipBehavior: Clip.none,
            children: [
              Align(
                alignment: Alignment.center,
                child: Transform.rotate(
                  angle: isUpperTooth ? 3.18 : 0,
                  child: ImageIcon(
                    AssetImage("assets/teeth/$name.png"),
                    color: Colors.white,
                    size: isMobile ? 40 : 50,
                  ),
                ),
              ),
              if (status != null)
                Positioned(
                  left: -11,
                  right: -11,
                  bottom: -11,
                  child: Wrap(
                    spacing: 3,
                    runSpacing: 3,
                    alignment: WrapAlignment.end,
                    runAlignment: WrapAlignment.end,
                    crossAxisAlignment: WrapCrossAlignment.end,
                    children: [
                      for (var c in colors)
                        CircleAvatar(
                          maxRadius: 7,
                          backgroundColor: c,
                        ),
                      if (status!.operative.isNotEmpty)
                        if (status!.operative == 'MOD')
                          Container(
                            padding: const EdgeInsets.all(1),
                            decoration: BoxDecoration(
                              color: kOperativeToothColor,
                              borderRadius: BorderRadius.circular(3),
                            ),
                            child: Text(
                              status!.operative,
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                                fontSize: 11,
                              ),
                            ),
                          )
                        else
                          CircleAvatar(
                            maxRadius: 9,
                            backgroundColor: kOperativeToothColor,
                            child: FittedBox(
                              fit: BoxFit.contain,
                              child: Text(
                                status!.operative,
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ),
                    ],
                  ),
                ),
              Positioned(
                top: isMobile ? -19 : -23,
                left: isMobile ? -11 : -13,
                child: Container(
                  padding: const EdgeInsets.all(3),
                  decoration: BoxDecoration(
                    color: ThemeColors.primary,
                    borderRadius: BorderRadius.circular(51),
                  ),
                  child: Center(
                    child: Text(
                      position.toUpperCase(),
                      style: TextStyle(
                        fontSize: isMobile ? 12 : 16,
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
