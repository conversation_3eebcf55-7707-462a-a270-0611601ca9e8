import 'package:core_app/core/api/patients.dart';
import 'package:core_app/core/models/patient.dart';
import 'package:flutter/material.dart';
import 'package:get/get_utils/get_utils.dart';

class SearchPatients extends StatefulWidget {
  const SearchPatients(
    this.setPatient, {
    super.key,
    this.initValue,
    this.onSearchInputChange,
  });

  final void Function(String id) setPatient;
  final void Function(String val)? onSearchInputChange;
  final String? initValue;

  @override
  State<SearchPatients> createState() => _SearchPatientsState();
}

class _SearchPatientsState extends State<SearchPatients> {
  // Timer? _debounce;
  String selectedPatientName = '';
  Iterable<PatientModel> patients = [];
  Iterable<String> patientsNames = [];

  fetchAPI(String q) async {
    patients = await PatientsAPI.list(1, q);
    patientsNames = patients.map((e) => e.name.capitalize!);
    return patients;
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(builder: (context, constraints) {
      return Autocomplete<PatientModel>(
        optionsViewBuilder: (context, onSelected, options) => Align(
          alignment: Alignment.topLeft,
          child: Material(
            elevation: 8,
            color: Colors.white,
            clipBehavior: Clip.antiAlias,
            borderRadius: BorderRadius.circular(11),
            child: SizedBox(
              height: 80.0 * options.length,
              width: constraints.biggest.width,
              child: ListView.builder(
                itemExtent: 75,
                shrinkWrap: false,
                itemCount: options.length,
                clipBehavior: Clip.antiAlias,
                itemBuilder: (BuildContext context, int index) {
                  final PatientModel option = options.elementAt(index);
                  return Material(
                    color: Colors.white,
                    child: InkWell(
                      onTap: () => onSelected(option),
                      borderRadius: BorderRadius.circular(11),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(option.name),
                            Text(
                                '${option.phoneNumber} - #${option.fileNumber}'),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ),
        fieldViewBuilder:
            (context, textEditingController, focusNode, onFieldSubmitted) {
          if (patients.isEmpty && textEditingController.text.isEmpty) {
            textEditingController.text = widget.initValue ?? '';
          }

          return TextFormField(
            maxLines: 1,
            focusNode: focusNode,
            onChanged: widget.onSearchInputChange,
            controller: textEditingController,
            onFieldSubmitted: (_) => onFieldSubmitted(),
            decoration: const InputDecoration(labelText: 'Patient'),
          );
        },
        optionsBuilder: (TextEditingValue textEditingValue) async {
          if (textEditingValue.text == '') {
            return const Iterable<PatientModel>.empty();
          }
          // if (_debounce?.isActive ?? false) _debounce?.cancel();
          // _debounce = Timer(const Duration(milliseconds: 500), () {
          // });

          if (patientsNames.isNotEmpty) {
            var localSearch =
                patients.where((e) => e.name.contains(textEditingValue.text));
            if (localSearch.isNotEmpty) return localSearch;
          }
          return await fetchAPI(textEditingValue.text);
        },
        onSelected: (v) {
          widget.setPatient(v.id);
          selectedPatientName = v.name;
          setState(() {});
        },
      );
    });
  }
}
