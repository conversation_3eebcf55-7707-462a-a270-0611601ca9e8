import 'package:core_app/components/buttons/icon.dart';
import 'package:core_app/core/utils/responsive.dart';
import 'package:core_app/routes/navigation.dart';
import 'package:get/get.dart';

import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:flutter/material.dart';

class BasicDialog extends StatelessWidget {
  const BasicDialog({
    Key? key,
    this.controllerTag,
    this.onClose,
    this.width = 500,
    required this.title,
    this.dismissible = false,
    this.actions = const [],
    this.children = const [],
  }) : super(key: key);

  final String title;
  final double width;
  final String? controllerTag;
  final bool dismissible;
  final List<Widget> actions;
  final List<Widget> children;
  final void Function(Function([dynamic result]) close)? onClose;

  @override
  Widget build(BuildContext context) {
    final paddingOfKeyboard = MediaQuery.of(context).viewInsets.bottom;
    return Material(
      color: Colors.black12,
      child: Stack(
        children: [
          if (dismissible)
            SizedBox.expand(
              child: GestureDetector(
                onTap: onClose == null
                    ? Get.back
                    : () => onClose!(Navigation.back),
              ),
            ),
          Center(
            child: Container(
              padding: const EdgeInsets.only(bottom: 20),
              width: context.desktopView ? width : double.infinity,
              clipBehavior: Clip.antiAlias,
              margin: context.desktopView
                  ? const EdgeInsets.all(30)
                  : const EdgeInsets.all(0),
              decoration: BoxDecoration(
                // color: const Color.fromARGB(255, 252, 254, 255),
                color: const Color(0xFFFFFFFF),
                borderRadius:
                    context.desktopView ? BorderRadius.circular(27) : null,
                // gradient: const LinearGradient(
                //   begin: Alignment.topLeft,
                //   end: Alignment.bottomRight,
                //   colors: [
                //     Colors.white,
                //      Color.fromARGB(255, 252, 254, 255),
                //   ]
                //   ),
              ),
              child: SingleChildScrollView(
                padding: EdgeInsets.only(
                  top: 23,
                  left: 23,
                  right: 23,
                  bottom: paddingOfKeyboard + 23,
                ),
                child: AnimatedSize(
                  duration: kThemeAnimationDuration,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              title,
                              style: const TextStyle(
                                fontSize: 26,
                                color: Color(0xFF3caee3),
                                fontWeight: FontWeight.w200,
                              ),
                            ),
                          ),
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              ...actions,
                              const SizedBox(width: 11),
                              Container(
                                decoration: BoxDecoration(
                                  color: Colors.white12,
                                  borderRadius: BorderRadius.circular(26),
                                ),
                                child: XIconButton(
                                  icon: Iconsax.close_circle_copy,
                                  onPressed: onClose == null
                                      ? Get.back
                                      : () => onClose!(Navigation.back),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      ...children,
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
