import 'package:flutter/material.dart';
import 'package:core_app/core/constants/colors.dart';

class MarkdownRenderer extends StatelessWidget {
  final String content;
  final double maxWidth;

  const MarkdownRenderer({
    Key? key,
    required this.content,
    this.maxWidth = double.infinity,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final lines = content.split('\n').skip(1).toList();
    final widgets = <Widget>[];

    for (int i = 0; i < lines.length; i++) {
      final line = lines[i].trim();

      if (line.isEmpty) {
        widgets.add(const SizedBox(height: 12));
        continue;
      }

      // Headers
      if (line.startsWith('#')) {
        final level = line.split(' ').first.length;
        final text = line.substring(level).trim();
        widgets.add(_buildHeader(text, level));
        continue;
      }

      // Lists
      if (line.startsWith('- ') || line.startsWith('* ')) {
        final text = line.substring(2);
        widgets.add(_buildListItem(text, false, null));
        continue;
      }

      if (RegExp(r'^\d+\. ').hasMatch(line)) {
        final match = RegExp(r'^(\d+)\. (.+)$').firstMatch(line);
        if (match != null) {
          final number = match.group(1)!;
          final text = match.group(2)!;
          widgets.add(_buildListItem(text, true, number));
        }
        continue;
      }

      // Regular text
      widgets.add(_buildTextBlock(line));
    }

    return Container(
      width: maxWidth,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: widgets,
      ),
    );
  }

  Widget _buildHeader(String text, int level) {
    double fontSize;
    FontWeight fontWeight;
    EdgeInsets padding;

    switch (level) {
      case 1:
        fontSize = 24;
        fontWeight = FontWeight.bold;
        padding = const EdgeInsets.only(bottom: 16, top: 8);
        break;
      case 2:
        fontSize = 20;
        fontWeight = FontWeight.w600;
        padding = const EdgeInsets.only(bottom: 12, top: 16);
        break;
      case 3:
        fontSize = 18;
        fontWeight = FontWeight.w600;
        padding = const EdgeInsets.only(bottom: 8, top: 12);
        break;
      default:
        fontSize = 16;
        fontWeight = FontWeight.w500;
        padding = const EdgeInsets.only(bottom: 8, top: 8);
    }

    return Padding(
      padding: padding,
      child: _buildRichText(text, fontSize, fontWeight, ThemeColors.primary),
    );
  }

  Widget _buildTextBlock(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: _buildRichText(text, 16, FontWeight.normal, ThemeColors.text),
    );
  }

  Widget _buildListItem(String text, bool isOrdered, [String? number]) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8, left: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.only(top: 8, right: 12),
            child: isOrdered
                ? Text(
                    number ?? '•',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: ThemeColors.primary,
                    ),
                  )
                : Container(
                    width: 6,
                    height: 6,
                    margin: const EdgeInsets.only(top: 8),
                    decoration: const BoxDecoration(
                      color: ThemeColors.primary,
                      shape: BoxShape.circle,
                    ),
                  ),
          ),
          Expanded(
            child:
                _buildRichText(text, 16, FontWeight.normal, ThemeColors.text),
          ),
        ],
      ),
    );
  }

  Widget _buildRichText(
      String text, double fontSize, FontWeight fontWeight, Color color) {
    final spans = <TextSpan>[];
    final parts = _splitBoldText(text);

    for (final part in parts) {
      if (part.isBold) {
        spans.add(TextSpan(
          text: part.text,
          style: TextStyle(
            fontSize: fontSize,
            fontWeight: FontWeight.bold,
            color: color,
            height: 1.5,
          ),
        ));
      } else {
        spans.add(TextSpan(
          text: part.text,
          style: TextStyle(
            fontSize: fontSize,
            fontWeight: fontWeight,
            color: color,
            height: 1.5,
          ),
        ));
      }
    }

    return RichText(
      text: TextSpan(children: spans),
    );
  }

  List<_TextPart> _splitBoldText(String text) {
    final parts = <_TextPart>[];
    final regex = RegExp(r'\*\*(.*?)\*\*|__(.*?)__');
    int lastIndex = 0;

    for (final match in regex.allMatches(text)) {
      // Add text before the match
      if (match.start > lastIndex) {
        parts.add(_TextPart(
          text: text.substring(lastIndex, match.start),
          isBold: false,
        ));
      }

      // Add the bold text
      final boldText = match.group(1) ?? match.group(2) ?? '';
      parts.add(_TextPart(
        text: boldText,
        isBold: true,
      ));

      lastIndex = match.end;
    }

    // Add remaining text
    if (lastIndex < text.length) {
      parts.add(_TextPart(
        text: text.substring(lastIndex),
        isBold: false,
      ));
    }

    return parts;
  }
}

class _TextPart {
  final String text;
  final bool isBold;

  _TextPart({required this.text, required this.isBold});
}
