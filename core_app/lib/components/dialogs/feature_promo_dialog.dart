import 'package:core_app/routes/navigation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:core_app/components/dialogs/basic.dart';
import 'package:core_app/components/dialogs/markdown_renderer.dart';
import 'package:core_app/components/buttons/text_icon.dart';
import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/core/utils/version_manager.dart';

class FeaturePromoDialog extends StatelessWidget {
  final String content;
  final String version;
  final VoidCallback? onClose;

  const FeaturePromoDialog({
    Key? key,
    required this.content,
    required this.version,
    this.onClose,
  }) : super(key: key);

  void _handleClose() {
    // Mark version as shown
    VersionManager.markVersionAsShown(version);

    // Close dialog
    if (onClose != null) {
      onClose!();
    } else {
      Navigation.back();
    }
  }

  @override
  Widget build(BuildContext context) {
    return BasicDialog(
      title: 'What\'s New',
      width: 600,
      dismissible: true,
      onClose: (close) => _handleClose(),
      actions: [
        _buildActionButton(),
      ],
      children: [
        _buildVersionInfo(),
        const SizedBox(height: 20),
        _buildContent(),
      ],
    );
  }

  Widget _buildVersionInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeColors.primaryLighter,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.rocket_launch,
            color: ThemeColors.primary,
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(
            'Version $version',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: ThemeColors.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    return Container(
      constraints: const BoxConstraints(
        maxHeight: 400,
      ),
      child: SingleChildScrollView(
        child: MarkdownRenderer(
          content: content,
          maxWidth: double.infinity,
        ),
      ),
    );
  }

  Widget _buildActionButton() {
    return Container(
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [
            ThemeColors.primary,
            ThemeColors.primaryDark,
          ],
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: XTextIconButton(
        title: 'Got it!',
        icon: Icons.check,
        onPressed: _handleClose,
      ),
    );
  }
}

// Service class to show feature promo dialog
class FeaturePromoService {
  static bool _isShowingDialog = false;

  static Future<void> checkAndShowFeaturePromo() async {
    // Prevent multiple dialogs from opening
    if (_isShowingDialog) return;

    try {
      final content = await VersionManager.getLatestVersion();
      if (content == null) return;

      final version = VersionManager.extractVersionFromContent(content);
      if (version == null) return;

      var shouldShowVersion = VersionManager.shouldShowVersion(version);
      if (shouldShowVersion) {
        print('shouldShowVersion: $version');
        print('shouldShowVersion: $shouldShowVersion');
        _isShowingDialog = true;
        await _showDialog(content, version);
        _isShowingDialog = false;
      }
    } catch (e) {
      _isShowingDialog = false;
      debugPrint('Error checking feature promo: $e');
    }
  }

  static Future<void> _showDialog(String content, String version) async {
    // Check if there's already a dialog open
    if (Get.isDialogOpen == true) return;

    VersionManager.markVersionAsShown(version);

    await Get.dialog(
      FeaturePromoDialog(
        content: content,
        version: version,
      ),
      barrierDismissible: false,
    );
  }
}
