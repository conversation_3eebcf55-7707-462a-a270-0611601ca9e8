import 'package:core_app/core/constants/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:math' as math;

class DecimalTextInputFormatter extends TextInputFormatter {
  DecimalTextInputFormatter(this.decimalRange, {this.activatedNegativeValues})
      : assert(
          decimalRange >= 0,
          'DecimalTextInputFormatter declaretion error',
        );

  final int decimalRange;
  final bool? activatedNegativeValues;

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue, // unused.
    TextEditingValue newValue,
  ) {
    TextSelection newSelection = newValue.selection;
    String truncated = newValue.text;

    if (newValue.text.contains(' ')) {
      return oldValue;
    }

    if (newValue.text.isEmpty) {
      return newValue;
    } else if (double.tryParse(newValue.text) == null &&
        !(newValue.text.length == 1 &&
            (activatedNegativeValues == true ||
                activatedNegativeValues == null) &&
            newValue.text == '-')) {
      return oldValue;
    }

    if (activatedNegativeValues == false && double.parse(newValue.text) < 0) {
      return oldValue;
    }

    if ((double.tryParse(oldValue.text) == 0 && !newValue.text.contains('.'))) {
      if (newValue.text.length >= oldValue.text.length) {
        return oldValue;
      }
    }

    String value = newValue.text;

    if (decimalRange == 0 && value.contains(".")) {
      truncated = oldValue.text;
      newSelection = oldValue.selection;
    }

    if (value.contains(".") &&
        value.substring(value.indexOf(".") + 1).length > decimalRange) {
      truncated = oldValue.text;
      newSelection = oldValue.selection;
    } else if (value == ".") {
      truncated = "0.";

      newSelection = newValue.selection.copyWith(
        baseOffset: math.min(truncated.length, truncated.length + 1),
        extentOffset: math.min(truncated.length, truncated.length + 1),
      );
    }

    return TextEditingValue(
      text: truncated,
      selection: newSelection,
      composing: TextRange.empty,
    );
  }
}

class MainTextField extends StatelessWidget {
  static List<TextInputFormatter> digits = [
    FilteringTextInputFormatter.digitsOnly
  ];
  static List<TextInputFormatter> singleLine = [
    FilteringTextInputFormatter.singleLineFormatter
  ];
  static List<TextInputFormatter> decimal = [
    FilteringTextInputFormatter.allow(RegExp(r'(^\d*\.?\d{0,2})'))
  ];

  const MainTextField({
    super.key,
    this.controller,
    this.leadingIcon,
    this.label,
    this.onChange,
    this.helperText,
    this.obscureText = false,
    this.textFieldWidth = double.infinity,
    this.maxLines = 1,
    this.minLines,
    this.leadingIconSize = 33,
    this.onTap,
    this.validator,
    this.focusNode,
    this.inputFormatters,
    this.autofocus = false,
    this.initialValue,
    this.onSubmit,
    this.readOnly = false,
    this.enabled = true,
    this.alignLabelWithHint = false,
    this.textCapitalization = TextCapitalization.none,
  });

  final String? label;
  final bool enabled;
  final String? helperText;
  final String? initialValue;
  final bool autofocus;
  final bool readOnly;
  final bool obscureText, alignLabelWithHint;
  final int? minLines, maxLines;
  final double? textFieldWidth;
  final double leadingIconSize;
  final FocusNode? focusNode;
  final List<TextInputFormatter>? inputFormatters;
  final TextEditingController? controller;
  final TextCapitalization textCapitalization;
  final void Function()? onTap;
  final void Function(String val)? onChange;
  final void Function(String? val)? onSubmit;
  final String? Function(String? val)? validator;

  final IconData? leadingIcon;

  @override
  Widget build(BuildContext context) {
    final outlineInputBorder = OutlineInputBorder(
      borderRadius: const BorderRadius.all(Radius.circular(11)),
      borderSide: BorderSide(
        width: .8,
        color: ThemeColors.notion.withValues(alpha: 0.37),
      ),
    );
    return SizedBox(
      width: textFieldWidth,
      child: TextFormField(
        validator: validator,
        autofocus: autofocus,
        onFieldSubmitted: onSubmit,
        readOnly: onTap != null || readOnly,
        onTap: onTap,
        enabled: enabled,
        focusNode: focusNode,
        textCapitalization: textCapitalization,
        initialValue: initialValue,
        onChanged: onChange,
        obscureText: obscureText,
        controller: controller,
        maxLines: maxLines,
        minLines: minLines,
        inputFormatters: inputFormatters,
        cursorColor: ThemeColors.primary,
        style: const TextStyle(
          color: ThemeColors.text,
        ),
        decoration: InputDecoration(
          border: outlineInputBorder,
          enabledBorder: outlineInputBorder,
          focusedBorder: outlineInputBorder,
          disabledBorder: outlineInputBorder,
          focusedErrorBorder: outlineInputBorder,
          errorBorder: outlineInputBorder.copyWith(
            borderSide: const BorderSide(
              width: .8,
              color: ThemeColors.error,
            ),
          ),
          //
          alignLabelWithHint: alignLabelWithHint,
          floatingLabelBehavior: FloatingLabelBehavior.auto,
          floatingLabelAlignment: FloatingLabelAlignment.start,
          //
          prefixIcon: leadingIcon == null
              ? null
              : Icon(
                  leadingIcon,
                  size: leadingIconSize,
                  color: const Color(0xFF84939A),
                ),
          //
          labelText: label,
          helperText: helperText,
          labelStyle: const TextStyle(
            color: Color(0xFF9AA7AD),
          ),
        ),
      ),
    );
  }
}
