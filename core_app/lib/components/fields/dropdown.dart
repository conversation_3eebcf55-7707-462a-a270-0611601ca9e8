import 'package:core_app/core/constants/colors.dart';
import 'package:flutter/material.dart';

class XDropdownButton<T> extends StatelessWidget {
  const XDropdownButton({
    super.key,
    this.value,
    this.filled = false,
    this.width,
    this.label,
    this.helperText,
    this.leadingIcon,
    required this.items,
    required this.onChange,
    this.leadingIconSize = 33,
    this.alignLabelWithHint = false,
  });

  final T? value;
  final bool alignLabelWithHint;
  final List<DropdownMenuItem<T>> items;
  final double? width, leadingIconSize;
  final IconData? leadingIcon;
  final String? label;
  final String? helperText;
  final bool filled;
  final void Function(T? val) onChange;

  @override
  Widget build(BuildContext context) {
    final outlineInputBorder = OutlineInputBorder(
      borderRadius: const BorderRadius.all(Radius.circular(11)),
      borderSide: BorderSide(
        width: .8,
        color: ThemeColors.notion.withValues(alpha: 0.37),
      ),
    );
    return SizedBox(
      width: width ?? double.infinity,
      child: DropdownButtonFormField(
        items: items,
        value: value,
        isExpanded: true,
        onChanged: onChange,
        dropdownColor: Colors.white,
        borderRadius: BorderRadius.circular(11),
        iconEnabledColor: const Color(0xFF84939A),
        icon: leadingIcon == null
            ? null
            : Icon(
                leadingIcon,
                size: 33,
                color: const Color(0xFF84939A),
              ),
        decoration: InputDecoration(
          filled: filled,
          fillColor: const Color.fromARGB(255, 253, 254, 255),
          border: outlineInputBorder,
          enabledBorder: outlineInputBorder,
          focusedBorder: outlineInputBorder,
          disabledBorder: outlineInputBorder,
          focusedErrorBorder: outlineInputBorder,
          errorBorder: outlineInputBorder.copyWith(
            borderSide: const BorderSide(
              width: .8,
              color: ThemeColors.error,
            ),
          ),
          //
          alignLabelWithHint: alignLabelWithHint,
          floatingLabelBehavior: FloatingLabelBehavior.auto,
          floatingLabelAlignment: FloatingLabelAlignment.start,
          //
          prefixIcon: leadingIcon == null
              ? null
              : Icon(
                  leadingIcon,
                  size: leadingIconSize,
                  color: const Color(0xFF84939A),
                ),
          //
          labelText: label,
          helperText: helperText,
          labelStyle: const TextStyle(
            color: Color(0xFF9AA7AD),
          ),
        ),
      ),
    );
  }
}
