import 'package:flutter/material.dart';

import 'text.dart';

class ClickableTextField extends StatefulWidget {
  const ClickableTextField({
    super.key,
    this.value,
    this.onChange,
    this.leadingIcon,
    required this.hint,
    this.onTap,
    this.onDoubleTap,
  });

  final String hint;
  final String? value;
  final IconData? leadingIcon;
  final void Function(TextEditingController val)? onTap;
  final void Function(TextEditingController val)? onDoubleTap;
  final void Function(String val)? onChange;

  @override
  State<ClickableTextField> createState() => _ClickableFieldState();
}

class _ClickableFieldState extends State<ClickableTextField> {
  late final TextEditingController _controller;

  @override
  void initState() {
    _controller = TextEditingController(text: widget.value);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var mainTextField = MainTextField(
      label: widget.hint,
      controller: _controller,
      onChange: widget.onChange,
      leadingIcon: widget.leadingIcon,
      onTap: widget.onTap == null ? null : () => widget.onTap!(_controller),
    );
    if (widget.onDoubleTap != null) {
      return GestureDetector(
        onDoubleTap: () => widget.onDoubleTap!(_controller),
        child: mainTextField,
      );
    } else {
      return mainTextField;
    }
  }
}
