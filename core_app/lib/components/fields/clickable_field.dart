import 'package:core_app/core/constants/colors.dart';
import 'package:flutter/material.dart';

class ClickableField extends StatelessWidget {
  const ClickableField({
    super.key,
    required this.hint,
    this.width,
    this.leadingIcon,
    this.value,
    required this.onTap,
  });

  final String hint;
  final String? value;
  final double? width;
  final IconData? leadingIcon;
  final void Function() onTap;

  @override
  Widget build(BuildContext context) {
    var nullValue = value != null && value!.isNotEmpty;
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: width ?? double.infinity,
        padding:
            EdgeInsets.symmetric(vertical: nullValue ? 9 : 16, horizontal: 13),
        decoration: BoxDecoration(
          borderRadius: const BorderRadius.all(Radius.circular(11)),
          border: Border.all(
            width: .8,
            color: ThemeColors.notion.withValues(alpha: 0.37),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (nullValue)
              Padding(
                padding: const EdgeInsets.only(left: 3),
                child: Text(
                  hint,
                  style: const TextStyle(
                    fontSize: 12,
                    color: Color(0xFF9AA7AD),
                  ),
                ),
              ),
            Row(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                if (leadingIcon != null)
                  Icon(
                    leadingIcon,
                    size: 33,
                    color: const Color(0xFF84939A),
                  ),
                const SizedBox(width: 5),
                Expanded(
                  child: nullValue
                      ? Text(
                          value!,
                          style: const TextStyle(
                            fontSize: 16,
                            color: ThemeColors.text,
                          ),
                        )
                      : Text(
                          hint,
                          style: const TextStyle(
                            fontSize: 16,
                            color: Color(0xFF9AA7AD),
                          ),
                        ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
