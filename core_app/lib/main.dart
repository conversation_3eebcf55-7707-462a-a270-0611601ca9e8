import 'package:core_app/core/constants/configuration.dart';
import 'package:core_app/routes/route_paths.dart';
import 'package:core_app/services/api.dart';
import 'package:core_app/services/auth.dart';
import 'package:core_app/services/config.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:get/get.dart';
import 'package:qlevar_router/qlevar_router.dart';
import 'package:syncfusion_localizations/syncfusion_localizations.dart';

import 'core/constants/colors.dart';
import 'core/fcm_init.dart';
import 'core/utils/i18n/translations.g.dart';
import 'routes/routes.dart';
import 'services/notify/messenger.dart';
import 'services/storage.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  QR.setUrlStrategy();
  QR.settings.enableLog = kDebugMode;
  QR.settings.enableLog = kDebugMode;
  Get.put(NotifyService().init());
  await Get.putAsync(() => StorageService().init());
  Get.put(ApiService().init());
  Get.put(AuthService().init());
  Get.put(ConfigService());
  if (supportsFirebase) {
    FCMInitializer.init();
    FlutterError.onError = (errorDetails) {
      FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
    };
    PlatformDispatcher.instance.onError = (error, stack) {
      FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
      return true;
    };
  }

  runApp(TranslationProvider(child: const MyApp()));
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});
  @override
  Widget build(BuildContext context) {
    final outlineInputBorder = OutlineInputBorder(
      borderRadius: const BorderRadius.all(Radius.circular(11)),
      borderSide: BorderSide(
        width: .8,
        color: ThemeColors.notion.withValues(alpha: .37),
      ),
    );
    return GetMaterialApp.router(
      title: 'Naab',
      debugShowCheckedModeBanner: false,
      routeInformationParser: const QRouteInformationParser(),
      routerDelegate: QRouterDelegate(routes,
          navKey: Get.key,
          initPath: AuthService.loggedIn ? Routes.Home : Routes.Login,
          observers: [
            GetObserver(null, Get.routing),
          ]),
      locale: TranslationProvider.of(context).flutterLocale,
      supportedLocales: AppLocaleUtils.supportedLocales,
      localizationsDelegates: const [
        ...GlobalMaterialLocalizations.delegates,
        SfGlobalLocalizations.delegate
      ],
      theme: ThemeData(
        useMaterial3: true,
        fontFamily: 'Inter',
        colorSchemeSeed: ThemeColors.primary,
        datePickerTheme: const DatePickerThemeData(
          surfaceTintColor: Colors.white,
        ),
        floatingActionButtonTheme: const FloatingActionButtonThemeData(
          elevation: .90,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.all(Radius.circular(12)),
          ),
        ),
        textButtonTheme: TextButtonThemeData(
          style: ButtonStyle(
            shape: WidgetStateProperty.all(
              RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            padding: WidgetStateProperty.all(const EdgeInsets.symmetric(
              vertical: 5,
              horizontal: 10,
            )),
          ),
        ),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ButtonStyle(
            elevation: WidgetStateProperty.all(0),
            backgroundColor:
                WidgetStateProperty.all(ThemeColors.primaryLighter),
            shape: WidgetStateProperty.all(
              RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(9),
              ),
            ),
            padding: WidgetStateProperty.all(const EdgeInsets.symmetric(
              vertical: 5,
              horizontal: 10,
            )),
            foregroundColor: WidgetStateProperty.all(ThemeColors.primaryDark),
            textStyle: WidgetStateProperty.all(const TextStyle(
              fontSize: 16,
              letterSpacing: 1.04,
              fontWeight: FontWeight.w600,
              color: ThemeColors.primaryDark,
            )),
          ),
        ),
        inputDecorationTheme: InputDecorationTheme(
          border: outlineInputBorder,
          enabledBorder: outlineInputBorder,
          focusedBorder: outlineInputBorder,
          disabledBorder: outlineInputBorder,
          focusedErrorBorder: outlineInputBorder,
          errorBorder: outlineInputBorder.copyWith(
            borderSide: const BorderSide(
              width: .8,
              color: ThemeColors.error,
            ),
          ),
          prefixIconColor: ThemeColors.notion,
          iconColor: ThemeColors.notion,
          labelStyle: const TextStyle(
            color: ThemeColors.notion,
            fontSize: 14,
            fontWeight: FontWeight.w400,
          ),
          hintStyle: const TextStyle(
            color: ThemeColors.notion,
            fontSize: 14,
            fontWeight: FontWeight.w400,
          ),
        ),
      ),
    );
  }
}
