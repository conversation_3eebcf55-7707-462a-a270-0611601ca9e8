import 'package:core_app/core/utils/responsive.dart';
import 'package:core_app/layout/controller.dart';
import 'package:core_app/layout/chat/dialog.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:qlevar_router/qlevar_router.dart';

import 'components/side_nav.dart';
import 'components/top_nav.dart';

class AppLayout extends StatelessWidget {
  const AppLayout(this.router, {super.key});

  final QRouter router;

  @override
  Widget build(BuildContext context) {
    final c = Get.put(LayoutController(router));
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: const SystemUiOverlayStyle(
        statusBarColor: Colors.white,
        statusBarBrightness: Brightness.light,
        statusBarIconBrightness: Brightness.dark,
      ),
      child: Safe<PERSON><PERSON>(
        child: Stack(
          children: [
            Positioned.fill(
              child: Material(
                color: context.theme.scaffoldBackgroundColor,
                child: Column(
                  children: [
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const TopNavComponent(),
                        Obx(() =>
                            c.miniSideNave.value || context.desktopView == false
                                ? const Divider(
                                    height: 7,
                                    thickness: .3,
                                  )
                                : const SizedBox()),
                        Obx(() =>
                            c.miniSideNave.value || context.desktopView == false
                                ? const SideNavComponent(true)
                                : const SizedBox()),
                      ],
                    ),
                    Expanded(
                      key: GlobalKey(),
                      child: Row(
                        children: [
                          Obx(() => c.miniSideNave.value ||
                                  context.desktopView == false
                              ? const SizedBox()
                              : const SideNavComponent(false)),
                          Expanded(
                            child: router,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Obx(() => c.isChatOpenned.value
                ? const ChatDialog()
                : const SizedBox.shrink()),
            Positioned(
              right: 10,
              bottom: 10,
              child: FloatingActionButton(
                mini: context.desktopView == false,
                onPressed: c.openChat,
                child: Obx(() => AnimatedSwitcher(
                      duration: 150.milliseconds,
                      child: c.isChatOpenned.value
                          ? Icon(
                              Icons.keyboard_arrow_down_rounded,
                              size: context.desktopView == false ? null : 30,
                            )
                          : Icon(
                              Iconsax.message_search_copy,
                              size: context.desktopView == false ? null : 30,
                            ),
                    )),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
