import 'package:core_app/components/buttons/icon.dart';
import 'package:core_app/components/buttons/text_icon.dart';
import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/core/utils/i18n/translations.g.dart';
import 'package:core_app/core/utils/responsive.dart';
import 'package:core_app/layout/controller.dart';
import 'package:core_app/routes/navigation.dart';

import 'package:core_app/routes/route_paths.dart';
import 'package:core_app/services/auth.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class TopNavComponent extends StatefulWidget {
  const TopNavComponent({super.key});

  @override
  State<TopNavComponent> createState() => _TopNavComponentState();
}

class _TopNavComponentState extends State<TopNavComponent> {
  bool viewSearchField = false;

  @override
  Widget build(BuildContext context) {
    final bool isDesktop = context.desktopView;
    final row = Row(
      key: const ValueKey('top_nav'),
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        SizedBox(
          height: double.infinity,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(
                width: isDesktop ? 240 : null,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    if (isDesktop)
                      Image.asset(
                        'assets/naab_logo.png',
                        width: 75,
                        height: 100,
                      )
                    else
                      Image.asset(
                        'assets/naab_logo.png',
                        width: 75,
                        height: 50,
                      ),
                    if (isDesktop)
                      Obx(() => XIconButton(
                            icon: LayoutController.to.miniSideNave.value
                                ? Iconsax.arrow_up_copy
                                : Iconsax.arrow_bottom_copy,
                            onPressed: () {
                              LayoutController.to.miniSideNave.value =
                                  !LayoutController.to.miniSideNave.value;
                            },
                          )),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              if (isDesktop)
                const SizedBox(
                  width: 340,
                  height: double.infinity,
                  child: Align(
                    alignment: Alignment.center,
                    child: _SearchField(),
                  ),
                ),
            ],
          ),
        ),
        Row(
          children: [
            if (isDesktop == false)
              XIconButton(
                icon: Iconsax.search_normal_copy,
                onPressed: () {
                  setState(() {
                    viewSearchField = !viewSearchField;
                  });
                },
              ),
            const SizedBox(width: 10),
            XTextIconButton(
              icon: Iconsax.setting_copy,
              minimizable: false,
              title: AuthService.to.user.value!.name,
              onPressed: () {
                Navigation.to(Routes.Settings);
              },
            ),
          ],
        ),
      ],
    );
    return SizedBox(
      height: isDesktop ? 80 : kToolbarHeight,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 13),
        child: isDesktop == true
            ? row
            : AnimatedSwitcher(
                key: const ValueKey('animatiedsearching'),
                duration: const Duration(milliseconds: 300),
                child: viewSearchField == false
                    ? row
                    : Row(
                        key: const ValueKey('searching_top_nav'),
                        children: [
                          const Expanded(child: _SearchField()),
                          const SizedBox(width: 10),
                          XIconButton(
                            icon: Iconsax.close_circle_copy,
                            onPressed: () {
                              setState(() {
                                viewSearchField = !viewSearchField;
                              });
                            },
                          ),
                        ],
                      ),
              ),
      ),
    );
  }
}

class _SearchField extends StatefulWidget {
  const _SearchField();

  @override
  State<_SearchField> createState() => _SearchFieldState();
}

class _SearchFieldState extends State<_SearchField> {
  final focusNode = FocusNode();

  @override
  Widget build(BuildContext context) {
    return TextField(
      focusNode: focusNode,
      onSubmitted: (value) async {
        Navigation.toNew(Routes.Search, {
          'q': value,
        });

        focusNode.requestFocus();
      },
      decoration: InputDecoration(
        filled: true,
        hintText: t.search,
        fillColor: ThemeColors.bg,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 20,
          vertical: 10,
        ),
        hintStyle: const TextStyle(
          fontSize: 14,
          color: Color(0xFF364152),
          fontWeight: FontWeight.w500,
        ),
        prefixIcon: const Icon(
          Iconsax.search_normal_copy,
          size: 18,
        ),
      ),
    );
  }
}
