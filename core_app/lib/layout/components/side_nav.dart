import 'dart:ui';

import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/core/models/user.dart';
import 'package:core_app/core/utils/i18n/translations.g.dart';
import 'package:core_app/layout/controller.dart';
import 'package:core_app/routes/navigation.dart';
import 'package:core_app/routes/route_names.dart';

import 'package:core_app/routes/route_paths.dart';
import 'package:core_app/services/auth.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SideNavComponent extends StatelessWidget {
  const SideNavComponent(this.mini, {super.key});

  final bool mini;

  @override
  Widget build(BuildContext context) {
    final c = LayoutController.to;
    final spaceSizedbox =
        mini ? const SizedBox(width: 7) : const SizedBox(height: 7);
    final btns = [
      if (mini == false)
        Text(
          t.nav.dashboard,
          style: const TextStyle(
            fontSize: 13,
            color: ThemeColors.text,
            fontWeight: FontWeight.w400,
          ),
        ),
      spaceSizedbox,
      Obx(() => _Button(
            title: t.nav.home,
            icon: Iconsax.home_copy,
            activated: c.currentPageName.value == RouteNames.Home,
            onPressed: () async {
              Navigation.to(Routes.Home);
            },
          )),
      if (AuthService.getUser.role.index >= UserRole.admin.index) spaceSizedbox,
      if (AuthService.getUser.role.index >= UserRole.admin.index)
        Obx(() => _Button(
              title: t.analysis.title,
              icon: Iconsax.presention_chart_copy,
              activated: c.currentPageName.value == RouteNames.Analysis,
              onPressed: () {
                Navigation.to(Routes.Analysis);
              },
            )),
      if (AuthService.getUser.role.index >= UserRole.admin.index) spaceSizedbox,
      if (AuthService.getUser.role.index >= UserRole.admin.index)
        Obx(() => _Button(
              title: t.nav.insurance,
              icon: Iconsax.personalcard_copy,
              activated: c.currentPageName.value == RouteNames.Insurance,
              onPressed: () => Navigation.to(Routes.Insurance),
            )),
      if (AuthService.getUser.role.index >= UserRole.secretary.index)
        spaceSizedbox,
      if (AuthService.getUser.role.index >= UserRole.secretary.index)
        Obx(() => _Button(
              title: t.nav.communication,
              icon: Iconsax.people_copy,
              activated: c.currentPageName.value == RouteNames.Communication,
              onPressed: () => Navigation.to(Routes.Communication),
            )),
      if (mini == false)
        if (AuthService.getUser.role.index != UserRole.basic.index)
          const Divider(
            height: 27,
            thickness: .3,
          ),
      if (mini == false)
        if (AuthService.getUser.role.index != UserRole.basic.index)
          Text(
            t.nav.data,
            style: const TextStyle(
              fontSize: 13,
              color: ThemeColors.text,
              fontWeight: FontWeight.w400,
            ),
          ),
      spaceSizedbox,
      if (AuthService.getUser.role.index != UserRole.basic.index)
        Obx(() => _Button(
              title: t.nav.patients,
              icon: Iconsax.user_search_copy,
              activated: c.currentPageName.value == RouteNames.PatientsList,
              onPressed: () => Navigation.to(Routes.PatientsList),
            )),
      if (AuthService.getUser.role.index >= UserRole.admin.index) spaceSizedbox,
      if (AuthService.getUser.role.index >= UserRole.admin.index)
        Obx(() => _Button(
              title: t.nav.expeenses,
              icon: Iconsax.money_send_copy,
              activated: c.currentPageName.value == RouteNames.Expenses,
              onPressed: () => Navigation.to(Routes.Expenses),
            )),
      if (AuthService.getUser.role.index != UserRole.basic.index) spaceSizedbox,
      if (AuthService.getUser.role.index != UserRole.basic.index)
        Obx(() => _Button(
              title: t.nav.inventory,
              icon: Iconsax.box_copy,
              activated: c.currentPageName.value == RouteNames.Inventories,
              onPressed: () => Navigation.to(Routes.Inventories),
            )),
      if (AuthService.getUser.role.index != UserRole.basic.index) spaceSizedbox,
      if (AuthService.getUser.role.index != UserRole.basic.index)
        Obx(() => _Button(
              title: t.nav.payments,
              icon: Iconsax.money_recive_copy,
              activated: c.currentPageName.value == RouteNames.Payments,
              onPressed: () {
                Navigation.to(Routes.Payments);
              },
            )),
      if (AuthService.getUser.role.index != UserRole.basic.index) spaceSizedbox,
      if (AuthService.getUser.role.index != UserRole.basic.index)
        Obx(() => _Button(
              title: t.nav.specialities,
              icon: Iconsax.category_copy,
              activated:
                  c.currentPageName.value == RouteNames.SpecialityTemplates,
              onPressed: () {
                Navigation.to(Routes.SpecialityTemplates);
              },
            )),
      if (AuthService.getUser.role.index >= UserRole.admin.index) spaceSizedbox,
      if (AuthService.getUser.role.index >= UserRole.admin.index)
        Obx(() => _Button(
              title: t.nav.labs,
              icon: const ImageIcon(AssetImage('assets/icons/labs.png')),
              activated: c.currentPageName.value == RouteNames.Labs,
              onPressed: () => Navigation.to(Routes.Labs),
            )),
      if (mini == false)
        if (AuthService.getUser.role.index >= UserRole.admin.index)
          const Divider(
            height: 27,
            thickness: .3,
          ),
      if (mini == false)
        if (AuthService.getUser.role.index >= UserRole.admin.index)
          Text(
            t.nav.administration,
            style: const TextStyle(
              fontSize: 13,
              color: ThemeColors.text,
              fontWeight: FontWeight.w400,
            ),
          ),
      if (AuthService.getUser.role.index >= UserRole.admin.index) spaceSizedbox,
      if (AuthService.getUser.role.index >= UserRole.admin.index)
        Obx(() => _Button(
              title: t.nav.users,
              icon: Iconsax.user_copy,
              activated: c.currentPageName.value == RouteNames.Users,
              onPressed: () => Navigation.to(Routes.Users),
            )),
      if (AuthService.getUser.role.index >= UserRole.admin.index) spaceSizedbox,
      if (AuthService.getUser.role.index >= UserRole.admin.index)
        Obx(() => _Button(
              title: t.nav.branches,
              icon: Iconsax.location_copy,
              activated: c.currentPageName.value == RouteNames.Branches,
              onPressed: () => Navigation.to(Routes.Branches),
            )),
    ];
    if (mini) {
      return SizedBox(
        height: 60,
        child: ScrollConfiguration(
          behavior: ScrollConfiguration.of(context)
              .copyWith(dragDevices: PointerDeviceKind.values.toSet()),
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.only(left: 20, right: 20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: btns,
            ),
          ),
        ),
      );
    }
    return SizedBox(
      width: 240,
      height: double.infinity,
      child: SingleChildScrollView(
        padding: const EdgeInsets.only(top: 20, left: 20, right: 15),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: btns,
        ),
      ),
    );
  }
}

class _Button extends StatefulWidget {
  const _Button({
    // super.key,
    required this.title,
    required this.icon,
    required this.onPressed,
    this.activated = false,
  });

  final String title;
  final Object icon;
  final VoidCallback onPressed;
  final bool activated;

  @override
  State<_Button> createState() => _ButtonState();
}

class _ButtonState extends State<_Button> {
  bool _isHover = false;

  bool get _isActivated => widget.activated || _isHover;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => _isHover = true),
      onExit: (_) => setState(() => _isHover = false),
      child: GestureDetector(
        onTap: widget.onPressed,
        child: AnimatedContainer(
          height: 45,
          duration: const Duration(milliseconds: 100),
          padding: const EdgeInsets.symmetric(horizontal: 27, vertical: 10),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(13),
            color: _isActivated
                ? ThemeColors.primaryLighter
                : context.theme.scaffoldBackgroundColor,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              widget.icon is IconData
                  ? Icon(
                      widget.icon as IconData,
                      size: 24,
                      color:
                          _isHover ? ThemeColors.primaryDark : ThemeColors.text,
                    )
                  : widget.icon as Widget,
              const SizedBox(width: 16),
              Text(
                widget.title,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: _isHover ? ThemeColors.primaryDark : ThemeColors.text,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
