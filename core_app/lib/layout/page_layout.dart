import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/core/utils/responsive.dart';
import 'package:flutter/material.dart';

class PageLayout extends StatelessWidget {
  const PageLayout({
    super.key,
    this.title,
    this.onRefresh,
    this.padding,
    required this.children,
  });

  final Future<void> Function()? onRefresh;
  final String? title;
  final double? padding;
  final List<Widget> children;

  @override
  Widget build(BuildContext context) {
    final insidePadding = padding ?? (context.desktopView ? 20 : 9);
    final child = LayoutBuilder(builder: (context, cons) {
      return SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        child: Container(
          padding: EdgeInsets.all(insidePadding),
          constraints: BoxConstraints(minHeight: cons.maxHeight),
          decoration: const BoxDecoration(
            color: ThemeColors.bgDark,
            borderRadius: BorderRadius.all(Radius.circular(21)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: children,
          ),
        ),
      );
    });
    if (onRefresh == null) return Material(child: child);
    return Material(
      child: RefreshIndicator(
        onRefresh: onRefresh!,
        child: child,
      ),
    );
  }
}
