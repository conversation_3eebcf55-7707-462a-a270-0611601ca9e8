import 'package:core_app/layout/chat/controller.dart';
import 'package:core_app/routes/navigation.dart';
import 'package:core_app/routes/route_names.dart';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:qlevar_router/qlevar_router.dart';
import 'package:universal_platform/universal_platform.dart';

import 'chat/screen.dart';

class LayoutController extends GetxController {
  static LayoutController get to => Get.find();

  final QRouter router;
  LayoutController(this.router) {
    currentPageName.value =
        router.navigator.currentRoute.name ?? RouteNames.Home;
    router.navigator.addListener(() {
      currentPageName.value =
          router.navigator.currentRoute.name ?? RouteNames.Home;
    });
  }

  final currentPageName = RouteNames.Home.obs;
  final miniSideNave = false.obs;

  OverlayEntry? charOverlayEntry;
  final isChatOpenned = false.obs;

  Future<void> openChat() async {
    if (Navigation.width <= 700 ||
        UniversalPlatform.isAndroid ||
        UniversalPlatform.isIOS) {
      isChatOpenned.value = false;
      Get.to(() => const ChatScreen());
      return;
    }
    if (isChatOpenned.value) {
      ChatController.to.widgetOpacity.value = 0.0;
      await 150.milliseconds.delay();
      isChatOpenned.value = false;
      // charOverlayEntry?.remove();
      Get.delete<ChatController>(force: true);
      return;
    }
    // final navigatorState = Navigator.of(QR.context!, rootNavigator: false);
    // final overlayState = navigatorState.overlay!;

    // final overlayEntryLoader = OverlayEntry(builder: (context) {
    //   return const ChatDialog();
    // });
    // overlayState.insert(overlayEntryLoader);
    LayoutController.to.isChatOpenned.value = true;
    // LayoutController.to.charOverlayEntry = overlayEntryLoader;
  }
}
