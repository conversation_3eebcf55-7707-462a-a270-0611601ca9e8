import 'package:core_app/core/helpers/api_handler.dart';
import 'package:core_app/core/models/molar_mind.dart';
import 'package:core_app/services/api.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart' hide Response, FormData, MultipartFile;
import 'package:image_picker/image_picker.dart';
import 'package:dio/dio.dart';

class ChatMessage {
  final dynamic message;
  final bool isBot;
  final String type;
  //
  final bool center;
  final bool error;
  final MolarMindResponse? imageRecognitionData;
  final DateTime? timestamp; // <-- Added timestamp

  ChatMessage({
    required this.message,
    required this.isBot,
    // ignore: unused_element
    this.type = 'text',
    this.center = false,
    this.error = false,
    this.imageRecognitionData,
    this.timestamp,
  });

  Map toJson() => {
        'message': message,
        'isBot': isBot,
        'center': center,
        'error': error,
        'imageRecognitionData': imageRecognitionData?.toJson(),
        'timestamp': timestamp?.toIso8601String(),
      };
}

class ChatController extends GetxController {
  static ChatController get to => Get.find();

  final widgetOpacity = 0.0.obs;

  final chat = <String>[];
  final chatMessages = <ChatMessage>[
    ChatMessage(
      message: 'Hey there, how can I help you?',
      isBot: true,
    )
  ].obs;
  //
  final textFocus = FocusNode(canRequestFocus: true);
  final textController = TextEditingController();
  //
  final scrollController = ScrollController();
  //
  final loading = false.obs;

  Future<void> send() async {
    loading.value = true;
    {
      String text = textController.text.trim();
      text = text[0].toUpperCase() + text.substring(1);
      chatMessages.add(ChatMessage(message: text, isBot: false));
      await Future.delayed(const Duration(milliseconds: 50));
      scrollController.animateTo(
        scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeIn,
      );
    }
    try {
      final response = await ApiService.dio.post("/ai/ask/", data: {
        'query': textController.text,
      });
      textController.clear();
      if (response.data == null || response.data == '') {
        chatMessages.add(ChatMessage(
          message: 'Could not find an answer',
          isBot: true,
          error: true,
          center: true,
        ));
        textFocus.requestFocus();
        loading.value = false;
        return;
      }

      chat.add(response.data);
      chatMessages.add(ChatMessage(
        message: response.data,
        isBot: true,
        type: response.data is String ? 'text' : 'json',
        center: false,
        error: false,
      ));
      await Future.delayed(const Duration(milliseconds: 50));
      scrollController.animateTo(
        scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeIn,
      );
      textController.clear();
    } catch (e) {
      chatMessages.add(ChatMessage(
        message: 'Something went wrong',
        isBot: true,
        error: true,
        center: true,
      ));
    }

    textFocus.requestFocus();
    loading.value = false;
  }

  @override
  Future<void> onInit() async {
    super.onInit();
    await Future.delayed(const Duration(milliseconds: 50));
    widgetOpacity.value = 1;
    await 150.milliseconds.delay();
    textFocus.requestFocus();

    final List<ChatMessage> historyMessages = [];
    await tryAPI(() async {
      final response = await ApiService.get('/ai/history');

      // Handle the new response structure with both history and molarmindReferences
      if (response.data != null) {
        // Load text chat history
        if (response.data!['history'] != null) {
          for (var element in (response.data!['history'] as List)) {
            // this item has timestamp field
            DateTime? timestamp;
            if (element['timestamp'] != null) {
              timestamp = DateTime.fromMillisecondsSinceEpoch(
                  (element['timestamp'] * 1000).round());
            } else if (element['created_at'] != null) {
              timestamp = DateTime.tryParse(element['created_at'].toString());
            }
            historyMessages.add(ChatMessage(
              message: element['message'],
              isBot: element['sender'] == 'assistant' ? true : false,
              type: element is String ? 'text' : 'json',
              center: false,
              error: false,
              timestamp: timestamp,
            ));
          }
        }

        // Load image recognition history from molarmindReferences
        if (response.data!['molarmindReferences'] != null) {
          for (var element in (response.data!['molarmindReferences'] as List)) {
            try {
              if (element is Map<String, dynamic>) {
                final imageResponse =
                    DentalClinicMolarChatImageResponse.fromJson(element);
                historyMessages.add(ChatMessage(
                  message:
                      'Previous image analysis - Found ${imageResponse.data.predictions.length} conditions',
                  isBot: true,
                  type: 'image_recognition',
                  imageRecognitionData: imageResponse.data,
                  timestamp: imageResponse.createdAt,
                ));
              }
            } catch (parseError) {
              continue;
            }
          }
        }
      }
    });

    // Sort by timestamp (oldest to newest)
    historyMessages.sort((a, b) {
      if (a.timestamp == null && b.timestamp == null) return 0;
      if (a.timestamp == null) return -1;
      if (b.timestamp == null) return 1;
      return a.timestamp!.compareTo(b.timestamp!);
    });

    chatMessages.clear();
    chatMessages.addAll(historyMessages);
  }

  void clearMessages() async {
    tryAPI(() async {
      chatMessages.clear();
      await ApiService.patch('/ai/reset/');
    });
  }

  // Image recognition methods
  final ImagePicker _imagePicker = ImagePicker();

  Future<void> pickAndAnalyzeImage() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (image == null) return;

      // Show loading message
      chatMessages.add(ChatMessage(
        message: 'Analyzing image...',
        isBot: true,
        type: 'text',
      ));

      await Future.delayed(const Duration(milliseconds: 50));
      scrollController.animateTo(
        scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeIn,
      );

      // Upload and analyze image
      final response = await uploadImageForAnalysis(image);

      // Remove loading message
      chatMessages.removeLast();

      if (response != null) {
        // Add image analysis result
        chatMessages.add(ChatMessage(
          message:
              'Image analysis complete! Found ${response.predictions.length} dental conditions.',
          isBot: true,
          type: 'image_recognition',
          imageRecognitionData: response,
        ));
      } else {
        chatMessages.add(ChatMessage(
          message: 'Failed to analyze image. Please try again.',
          isBot: true,
          error: true,
          center: true,
        ));
      }

      await Future.delayed(const Duration(milliseconds: 50));
      scrollController.animateTo(
        scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeIn,
      );
    } catch (e) {
      chatMessages.add(ChatMessage(
        message: 'Error analyzing image: $e',
        isBot: true,
        error: true,
        center: true,
      ));
    }
  }

  Future<MolarMindResponse?> uploadImageForAnalysis(XFile imageFile) async {
    try {
      final bytes = await imageFile.readAsBytes();
      final fileName = imageFile.name;

      final formData = FormData.fromMap({
        'files': MultipartFile.fromBytes(
          bytes,
          filename: fileName,
          contentType: DioMediaType('image', 'jpeg'),
        ),
      });

      final response = await ApiService.dio.post(
        '/ai/image_recognition/',
        data: formData,
        options: Options(
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        ),
      );

      if (response.statusCode == 200 && response.data != null) {
        // The backend now returns the full entry object in 'item' field
        final itemData = response.data?['item'];

        if (itemData != null) {
          // Parse the full DentalClinicMolarChatImageResponse from the item field
          final molarmindResponse =
              DentalClinicMolarChatImageResponse.fromJson(itemData);

          // Return the MolarMindResponse data
          return molarmindResponse.data;
        }
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        rethrow;
      }
      return null;
    }
  }
}
