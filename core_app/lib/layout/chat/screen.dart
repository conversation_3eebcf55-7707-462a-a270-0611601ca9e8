import 'package:core_app/core/constants/colors.dart';
import 'package:core_app/layout/chat/controller.dart';

import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'dart:io';
import 'package:core_app/services/api.dart';
import 'package:path/path.dart' as path;
import 'package:core_app/core/constants/configuration.dart';

class ChatScreen extends StatelessWidget {
  const ChatScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final c = Get.put(ChatController());
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: Scaffold(
        appBar: AppBar(
          elevation: .13,
          centerTitle: true,
          toolbarHeight: 45,
          backgroundColor: const Color(0xFF34ace3),
          systemOverlayStyle: const SystemUiOverlayStyle(
            statusBarColor: Color(0xFF34ace3),
            statusBarBrightness: Brightness.dark,
            statusBarIconBrightness: Brightness.light,
          ),
          leading: Padding(
            padding: const EdgeInsets.only(left: 7),
            child: IconButton(
              icon: const Icon(
                Iconsax.message_minus_copy,
                color: Colors.white,
              ),
              onPressed: c.clearMessages,
            ),
          ),
          title: const Text(
            'Naaby',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: ThemeColors.textLight,
            ),
          ),
          actions: [
            IconButton(
              icon: const Icon(
                Iconsax.close_square_copy,
                size: 21,
                color: Colors.white,
              ),
              onPressed: () {
                Get.back();
                // DashboardController.to.openChat();
              },
            ),
            const SizedBox(width: 7),
          ],
        ),
        bottomNavigationBar: AnimatedPadding(
          duration: const Duration(milliseconds: 200),
          padding:
              EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          child: GestureDetector(
            onTap: () {
              c.textFocus.requestFocus();
            },
            child: Container(
              height: 60,
              padding: const EdgeInsets.symmetric(horizontal: 13, vertical: 7),
              decoration: BoxDecoration(
                border: Border(
                  top: BorderSide(
                    color: Colors.grey.shade200,
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                children: [
                  Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: () {
                        c.pickAndAnalyzeImage();
                      },
                      child: const SizedBox(
                        width: 50,
                        height: double.infinity,
                        child: Icon(
                          Iconsax.gallery,
                          color: ThemeColors.primary,
                        ),
                      ),
                    ),
                  ),
                  Expanded(
                    child: TextField(
                      focusNode: c.textFocus,
                      controller: c.textController,
                      textCapitalization: TextCapitalization.sentences,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: ThemeColors.text.withValues(alpha: 0.91),
                      ),
                      onSubmitted: (value) {
                        c.send();
                      },
                      decoration: InputDecoration(
                        isDense: true,
                        border: InputBorder.none,
                        hintText: 'Type a message',
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 23,
                          vertical: 15,
                        ),
                        hintStyle: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: Colors.grey.shade500,
                        ),
                      ),
                    ),
                  ),
                  Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: () {
                        c.send();
                      },
                      child: const SizedBox(
                        width: 50,
                        height: double.infinity,
                        child: Icon(
                          Iconsax.send_1_copy,
                          color: ThemeColors.primary,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        body: Obx(() => ListView.separated(
              controller: c.scrollController,
              itemCount: c.loading.value
                  ? c.chatMessages.length + 1
                  : c.chatMessages.length,
              padding: const EdgeInsets.symmetric(horizontal: 13, vertical: 10),
              separatorBuilder: (context, index) => const SizedBox(height: 16),
              itemBuilder: (context, index) {
                if (c.loading.value == true && index == c.chatMessages.length) {
                  return const SpinKitFadingFour(
                    color: ThemeColors.primary,
                    size: 20,
                  );
                }
                final chat = c.chatMessages[index];
                return Padding(
                  padding: chat.isBot
                      ? const EdgeInsets.only(right: 25)
                      : const EdgeInsets.only(left: 25),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: chat.center
                        ? MainAxisAlignment.center
                        : chat.isBot
                            ? MainAxisAlignment.start
                            : MainAxisAlignment.end,
                    children: [
                      if (chat.isBot)
                        Image.asset(
                          'assets/chatbot_logo2.png',
                          width: 36,
                          height: 36,
                        ),
                      const SizedBox(width: 7),
                      Flexible(
                        child: Material(
                          color: chat.isBot
                              ? Colors.grey.shade200
                              : const Color(0xFFd6eef9),
                          borderRadius: chat.isBot
                              ? const BorderRadius.only(
                                  topRight: Radius.circular(10),
                                  bottomLeft: Radius.circular(10),
                                  bottomRight: Radius.circular(10),
                                )
                              : const BorderRadius.only(
                                  topLeft: Radius.circular(10),
                                  bottomLeft: Radius.circular(10),
                                  bottomRight: Radius.circular(10),
                                ),
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 10, vertical: 5),
                            child: chat.type == 'image_recognition' &&
                                    chat.imageRecognitionData != null
                                ? _buildImageRecognitionMessage(chat)
                                : Text(
                                    chat.message,
                                    style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                      color: chat.isBot
                                          ? Colors.grey.shade700
                                          : ThemeColors.text
                                              .withValues(alpha: 0.81),
                                    ),
                                  ),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            )),
      ),
    );
  }

  Widget _buildImageRecognitionMessage(ChatMessage chat) {
    final data = chat.imageRecognitionData!;
    final predictions = data.predictions;

    // No need for color legend since each prediction has its own random color

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Main message
        Text(
          chat.message,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.grey.shade700,
          ),
        ),
        const SizedBox(height: 6),

        // Image with prediction overlays (compact for dialog)
        if (data.imagePath != null) ...[
          GestureDetector(
            onTap: () {
              showDialog(
                context: Get.context!,
                builder: (context) => ImageRecognitionDialog(
                  imagePath: data.imagePath!,
                  image: data.image,
                  predictions: predictions,
                ),
              );
            },
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: AspectRatio(
                aspectRatio: data.image.width / data.image.height,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(6),
                  child: LayoutBuilder(
                    builder: (context, constraints) {
                      return Stack(
                        children: [
                          // Background image
                          Positioned.fill(
                            child: _buildImageWidget(data.imagePath!),
                          ),
                          // Prediction overlays
                          ...predictions.map((prediction) {
                            final color = prediction.tempColor ?? Colors.grey;
                            final originalImageWidth =
                                data.image.width.toDouble();
                            final originalImageHeight =
                                data.image.height.toDouble();
                            final containerWidth = constraints.maxWidth;
                            final containerHeight = constraints.maxHeight;
                            final scaleX = containerWidth / originalImageWidth;
                            final scaleY =
                                containerHeight / originalImageHeight;
                            final scaledX = prediction.x * scaleX;
                            final scaledY = prediction.y * scaleY;
                            final scaledWidth = prediction.width * scaleX;
                            final scaledHeight = prediction.height * scaleY;
                            return Positioned(
                              left: scaledX,
                              top: scaledY,
                              child: Container(
                                width: scaledWidth,
                                height: scaledHeight,
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color: color,
                                    width: 1.5,
                                  ),
                                  borderRadius: BorderRadius.circular(2),
                                ),
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: color.withValues(alpha: 0.2),
                                    borderRadius: BorderRadius.circular(2),
                                  ),
                                  child: Center(
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 2, vertical: 1),
                                      decoration: BoxDecoration(
                                        color: color,
                                        borderRadius: BorderRadius.circular(1),
                                      ),
                                      child: Text(
                                        prediction.className,
                                        style: const TextStyle(
                                          fontSize: 6,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.white,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            );
                          }).toList(),
                        ],
                      );
                    },
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(height: 6),
        ],

        // Predictions list (compact for dialog)
        if (predictions.isNotEmpty) ...[
          Text(
            'Detected:',
            style: TextStyle(
              fontSize: 11,
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 3),
          ...predictions.take(5).map((prediction) {
            final color = prediction.tempColor ?? Colors.grey;
            return Padding(
              padding: const EdgeInsets.only(bottom: 1),
              child: Row(
                children: [
                  Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: color,
                      borderRadius: BorderRadius.circular(1),
                    ),
                  ),
                  const SizedBox(width: 6),
                  Expanded(
                    child: Text(
                      '${prediction.className} (${(prediction.confidence * 100).toStringAsFixed(0)}%)',
                      style: TextStyle(
                        fontSize: 11,
                        fontWeight: FontWeight.w500,
                        color: Colors.grey.shade700,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }).toList(),

          // Show more indicator if there are more predictions
          if (predictions.length > 5)
            Text(
              '... and  [36m${predictions.length - 5} [39m more',
              style: TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.w500,
                color: Colors.grey.shade500,
                fontStyle: FontStyle.italic,
              ),
            ),
        ],
      ],
    );
  }

  Widget _buildImageWidget(String imagePath) {
    // Check if it's a network URL or local file path
    if (imagePath.startsWith('/')) {
      // Network image with fallback
      return Image.network(
        path.join(BASE_URL, imagePath),
        headers: {
          'token': '${ApiService.dio.options.headers['token']}',
        },
        fit: BoxFit.cover,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return Container(
            color: Colors.grey.shade200,
            child: Center(
              child: CircularProgressIndicator(
                value: loadingProgress.expectedTotalBytes != null
                    ? loadingProgress.cumulativeBytesLoaded /
                        loadingProgress.expectedTotalBytes!
                    : null,
                color: ThemeColors.primary,
                strokeWidth: 2,
              ),
            ),
          );
        },
        errorBuilder: (context, error, stackTrace) {
          return Container(
            color: Colors.grey.shade200,
            child: const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.image_not_supported,
                    color: Colors.grey,
                    size: 24,
                  ),
                  SizedBox(height: 4),
                  Text(
                    'Failed to load image',
                    style: TextStyle(
                      color: Colors.grey,
                      fontSize: 10,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      );
    } else {
      // Local file image
      return Image.file(
        File(imagePath),
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            color: Colors.grey.shade200,
            child: const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.image_not_supported,
                    color: Colors.grey,
                    size: 24,
                  ),
                  SizedBox(height: 4),
                  Text(
                    'Failed to load image',
                    style: TextStyle(
                      color: Colors.grey,
                      fontSize: 10,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      );
    }
  }
}

class ImageRecognitionDialog extends StatelessWidget {
  final String imagePath;
  final dynamic image; // Should match the type used in your data.image
  final List<dynamic>
      predictions; // Should match the type used in your predictions

  const ImageRecognitionDialog({
    Key? key,
    required this.imagePath,
    required this.image,
    required this.predictions,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Build a map of className to color for the key map
    final Map<String, Color> classColorMap = {};
    for (var p in predictions) {
      classColorMap[p.className] = p.tempColor ?? Colors.grey;
    }
    return Dialog(
      insetPadding: const EdgeInsets.symmetric(horizontal: 24, vertical: 40),
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.8,
        height: 700,
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Image Recognition Results',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Expanded(
              child: Row(
                children: [
                  // Image with overlays
                  Expanded(
                    flex: 4,
                    child: AspectRatio(
                      aspectRatio: image.width / image.height,
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(10),
                        child: LayoutBuilder(
                          builder: (context, constraints) {
                            final containerWidth = constraints.maxWidth;
                            final containerHeight = constraints.maxHeight;
                            return Stack(
                              children: [
                                Positioned.fill(
                                  child: _buildDialogImageWidget(imagePath),
                                ),
                                ...predictions.map((prediction) {
                                  final color =
                                      prediction.tempColor ?? Colors.grey;
                                  final originalImageWidth =
                                      image.width.toDouble();
                                  final originalImageHeight =
                                      image.height.toDouble();
                                  final scaleX =
                                      containerWidth / originalImageWidth;
                                  final scaleY =
                                      containerHeight / originalImageHeight;
                                  final scaledX = prediction.x * scaleX;
                                  final scaledY = prediction.y * scaleY;
                                  final scaledWidth = prediction.width * scaleX;
                                  final scaledHeight =
                                      prediction.height * scaleY;
                                  return Positioned(
                                    left: scaledX,
                                    top: scaledY,
                                    child: Container(
                                      width: scaledWidth,
                                      height: scaledHeight,
                                      decoration: BoxDecoration(
                                        border: Border.all(
                                          color: color,
                                          width: 2,
                                        ),
                                        borderRadius: BorderRadius.circular(3),
                                      ),
                                      child: Container(
                                        decoration: BoxDecoration(
                                          color: color.withValues(alpha: 0.18),
                                          borderRadius:
                                              BorderRadius.circular(3),
                                        ),
                                        child: Center(
                                          child: Container(
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 4, vertical: 2),
                                            decoration: BoxDecoration(
                                              color: color,
                                              borderRadius:
                                                  BorderRadius.circular(2),
                                            ),
                                            child: Text(
                                              prediction.className,
                                              style: const TextStyle(
                                                fontSize: 10,
                                                fontWeight: FontWeight.bold,
                                                color: Colors.white,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  );
                                }).toList(),
                              ],
                            );
                          },
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 18),
                  // Predictions list and key map
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Predictions',
                          style: TextStyle(
                            fontSize: 15,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Expanded(
                          child: ListView.builder(
                            itemCount: predictions.length,
                            itemBuilder: (context, idx) {
                              final p = predictions[idx];
                              final color = p.tempColor ?? Colors.grey;
                              return Padding(
                                padding: const EdgeInsets.only(bottom: 4),
                                child: Row(
                                  children: [
                                    Container(
                                      width: 12,
                                      height: 12,
                                      decoration: BoxDecoration(
                                        color: color,
                                        borderRadius: BorderRadius.circular(2),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Expanded(
                                      child: Text(
                                        '${p.className}  (${(p.confidence * 100).toStringAsFixed(1)}%)',
                                        style: const TextStyle(
                                          fontSize: 13,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDialogImageWidget(String imagePath) {
    if (imagePath.startsWith('/')) {
      return Image.network(
        path.join(BASE_URL, imagePath),
        headers: {
          'token': '${ApiService.dio.options.headers['token']}',
        },
        fit: BoxFit.contain,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            color: Colors.grey.shade200,
            child: const Center(
              child:
                  Icon(Icons.image_not_supported, color: Colors.grey, size: 40),
            ),
          );
        },
      );
    } else {
      return Image.file(
        File(imagePath),
        fit: BoxFit.contain,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            color: Colors.grey.shade200,
            child: const Center(
              child:
                  Icon(Icons.image_not_supported, color: Colors.grey, size: 40),
            ),
          );
        },
      );
    }
  }
}
