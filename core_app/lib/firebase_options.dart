// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCLx3FHqlfMlAEA-zuV88wUSx4_3FXwlQs',
    appId: '1:546248401498:web:494a8d449583c03952ae63',
    messagingSenderId: '546248401498',
    projectId: 'dentastic-app',
    authDomain: 'dentastic-app.firebaseapp.com',
    storageBucket: 'dentastic-app.appspot.com',
    measurementId: 'G-STRTNWTBD6',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCzOfYtINmaSc1aOt3SjORX6zfcPutoURs',
    appId: '1:546248401498:android:085b4d45512a84d252ae63',
    messagingSenderId: '546248401498',
    projectId: 'dentastic-app',
    storageBucket: 'dentastic-app.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDHQD4YGy7wDdWef7o5i9cR1JhfOMCkr1M',
    appId: '1:546248401498:ios:57a478f9a23d7a3252ae63',
    messagingSenderId: '546248401498',
    projectId: 'dentastic-app',
    storageBucket: 'dentastic-app.appspot.com',
    iosClientId: '546248401498-t9bi8iegeq9g4t5t3mg2s5fmf33aat54.apps.googleusercontent.com',
    iosBundleId: 'com.payrows.naab',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDHQD4YGy7wDdWef7o5i9cR1JhfOMCkr1M',
    appId: '1:546248401498:ios:57a478f9a23d7a3252ae63',
    messagingSenderId: '546248401498',
    projectId: 'dentastic-app',
    storageBucket: 'dentastic-app.appspot.com',
    iosClientId: '546248401498-t9bi8iegeq9g4t5t3mg2s5fmf33aat54.apps.googleusercontent.com',
    iosBundleId: 'com.payrows.naab',
  );
}
