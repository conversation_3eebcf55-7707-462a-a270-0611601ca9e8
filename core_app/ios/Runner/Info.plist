<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>CFBundleDevelopmentRegion</key>
		<string>$(DEVELOPMENT_LANGUAGE)</string>
		<key>CFBundleDisplayName</key>
		<string>NAAB</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>NAAB</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(FLUTTER_BUILD_NAME)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleVersion</key>
		<string>$(FLUTTER_BUILD_NUMBER)</string>
		<key>LSRequiresIPhoneOS</key>
		<true />
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIMainStoryboardFile</key>
		<string>Main</string>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UIViewControllerBasedStatusBarAppearance</key>
		<false />
		<key>CADisableMinimumFrameDurationOnPhone</key>
		<true />
		<key>UIApplicationSupportsIndirectInputEvents</key>
		<true />

		<!-- App Keys -->
		<!-- Location access-->
		<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
		<string>Naab needs access to your current location to help you easily add your doctor's
			branch location. This makes entering the address faster and more accurate.</string>
		<key>NSLocationWhenInUseUsageDescription</key>
		<string>Naab needs access to your current location to help you easily add your doctor's
			branch location. This makes entering the address faster and more accurate.</string>
		<!-- Languanges supported by slang package -->
		<key>CFBundleLocalizations</key>
		<array>
			<string>en</string>
			<string>ar</string>
		</array>
		<!-- file_picker package For files access -->
		<key>NSPhotoLibraryUsageDescription</key>
		<string>Naab needs access to your Photos library to allow doctors to upload images or videos
			related to patient visits.</string>
		<key>UISupportsDocumentBrowser</key>
		<true />
		<key>NSFileSharingEntitlements</key>
		<array>
			<string>file-sharing-all</string>
		</array>
		<!-- launch_review package -->
		<key>LSApplicationQueriesSchemes</key>
		<array>
			<string>itms-beta</string>
			<string>itms</string>
		</array>

		<key>FirebaseAppDelegateProxyEnabled</key>
		<false />


		<!-- <key>NSLocationWhenInUseUsageDescription</key>
	<string>This app might need access to your location to allow you to easily select your position for
		convenience when updating your clinic's branch location.</string> -->
		<!-- <key>NSPhotoLibraryUsageDescription</key>
	<string>This app needs access to the photo library to allow you to select photos.</string> -->

		<!-- <key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key><true/>
	</dict> -->
		<!-- Languanges supported by slang package -->
		<!-- <key>CFBundleLocalizations</key>
	<array>
		<string>en</string>
		<string>ar</string>
	</array> -->
		<!-- Permissions list starts here -->
		<!-- <key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>This app might need access to your location to allow you to easily select your position for
		convenience when updating your clinic's branch location</string> -->
		<!-- <key>NSLocationUsageDescription</key>
	<string>This app might need access to your location to allow you to easily select your position for
		convenience when updating your clinic's branch location</string> -->
		<!-- <key>NSLocationAlwaysUsageDescription</key>
	<string>This app might need access to your location to allow you to easily select your position for
		convenience when updating your clinic's branch location</string> -->
		<!-- Permission options for the `appTrackingTransparency` -->
		<!-- <key>NSUserTrackingUsageDescription</key>
	<string>We use app tracking to identify feature usage and behavior to enhance our insights on the
		features that matter and provide better experience to our users</string> -->
		<!-- Permissions lists ends here -->
		<!-- <key>LSApplicationQueriesSchemes</key>
	<array>
        <string>itms-beta</string>
        <string>itms</string>
	</array> -->
	</dict>
</plist>