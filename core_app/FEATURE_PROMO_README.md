# Feature Promo Dialog System

A beautiful and modern feature promotion dialog system for the Dentastic app that displays update information to users when new versions are released.

## 🎯 Features

- **Automatic Version Detection**: Automatically finds and displays the latest feature promo file
- **Smart Version Management**: Only shows new versions to users, prevents duplicate displays
- **Custom Markdown Renderer**: Lightweight markdown parser supporting headers, lists, and basic formatting
- **Beautiful UI**: Modern design following the app's color scheme and design patterns
- **Persistent Storage**: Uses SharedPreferences to track shown versions
- **Future-Ready**: Designed to support multi-page content with PageView

## 📁 File Structure

```
assets/
└── features_promo/
    ├── 1.0.0.md
    ├── 1.5.6.md
    └── [version].md
```

## 🚀 Usage

### 1. Creating Feature Promo Files

Create markdown files in `assets/features_promo/` with semantic versioning names:

```markdown
# 🎉 Welcome to Dentastic!

## What's New in Version 1.0.0

We're excited to introduce **Dentastic** - your comprehensive dental practice management solution!

### ✨ Key Features

- **Smart Appointment Management**
  - Easy scheduling and rescheduling
  - Automated reminders
  - Calendar integration

- **Patient Records**
  - Complete patient profiles
  - Treatment history tracking
  - Digital file management

### 🚀 Getting Started

1. **Set up your clinic profile**
2. **Add your team members**
3. **Create your first patient record**
4. **Schedule your first appointment**

---

*Thank you for choosing Dentastic!*
```

### 2. Integration in Your App

#### Automatic Check on App Start
```dart
// In your main.dart or app initialization
await FeaturePromoService.checkAndShowFeaturePromo();
```

#### Manual Trigger
```dart
// Show the latest feature promo
await FeaturePromoService.checkAndShowFeaturePromo();

// Or force show a specific version
final content = await VersionManager.getLatestVersion();
if (content != null) {
  final version = VersionManager.extractVersionFromContent(content);
  if (version != null) {
    await Get.dialog(
      FeaturePromoDialog(
        content: content,
        version: version,
      ),
    );
  }
}
```

### 3. Testing

Use the test page to verify functionality:

```dart
// Navigate to test page
Get.to(() => const TestFeaturePromoPage());
```

## 🎨 Customization

### Colors and Styling

The dialog uses the app's color scheme defined in `lib/core/constants/colors.dart`:

- **Primary**: `ThemeColors.primary` - Main accent color
- **Background**: `ThemeColors.bg` - Dialog background
- **Text**: `ThemeColors.text` - Main text color
- **Secondary Text**: `ThemeColors.notion` - Subtle text

### Markdown Support

The custom renderer supports:

- **Headers**: `# H1`, `## H2`, `### H3`
- **Lists**: `- Item` (unordered), `1. Item` (ordered)
- **Bold**: `**text**` or `__text__`

### Dialog Customization

Modify `lib/components/dialogs/feature_promo_dialog.dart` to:

- Change dialog size and layout
- Add custom animations
- Modify button styling
- Add additional content sections

## 🔧 Technical Details

### Version Manager (`lib/core/utils/version_manager.dart`)

- **`getLatestVersion()`**: Retrieves the latest version file from assets
- **`shouldShowVersion(version)`**: Checks if a version should be displayed
- **`markVersionAsShown(version)`**: Marks a version as shown
- **`extractVersionFromContent(content)`**: Extracts version from markdown content

### Markdown Renderer (`lib/components/dialogs/markdown_renderer.dart`)

- Custom parser for markdown content
- Supports headers, lists, and basic formatting
- Responsive design with proper spacing
- Follows app's typography and color scheme

### Feature Promo Dialog (`lib/components/dialogs/feature_promo_dialog.dart`)

- Built on top of `BasicDialog` for consistency
- Smooth animations and transitions
- Modern card-based design
- Responsive layout

## 🔮 Future Enhancements

### Multi-Page Support

The system is designed to easily support multi-page content:

```dart
// Future implementation with PageView
class MultiPageFeaturePromoDialog extends StatefulWidget {
  final List<String> pages; // Multiple markdown files
  final String version;
  
  // Implementation with PageView widget
}
```

### Advanced Features

- **Rich Media Support**: Images, videos, and interactive content
- **A/B Testing**: Different content for different user segments
- **Analytics**: Track user engagement with promo content
- **Localization**: Multi-language support for promo content
- **Scheduled Promos**: Show specific content at scheduled times

## 🐛 Troubleshooting

### Common Issues

1. **Dialog not showing**: Check if version is already marked as shown
2. **File not found**: Ensure markdown files are in `assets/features_promo/`
3. **Version parsing error**: Verify file naming follows semantic versioning
4. **UI issues**: Check if all required dependencies are imported

### Debug Commands

```dart
// Check last shown version
final lastShown = VersionManager.getLastShownVersion();
print('Last shown: $lastShown');

// Reset version history (for testing)
GetStorage().remove('last_shown_feature_version');

// Force show latest promo
await FeaturePromoService.checkAndShowFeaturePromo();
```

## 📝 Best Practices

1. **Version Naming**: Use semantic versioning (e.g., `1.0.0.md`, `1.5.6.md`)
2. **Content Structure**: Start with a clear title and organize content with headers
3. **File Size**: Keep markdown files concise and focused
4. **Testing**: Always test new promo content before deployment
5. **User Experience**: Don't show promos too frequently to avoid user fatigue

## 🤝 Contributing

When adding new features to the promo system:

1. Follow the existing code structure and patterns
2. Maintain the app's design system
3. Add proper error handling
4. Include tests for new functionality
5. Update this documentation

---

*This feature promo system provides a seamless way to communicate updates and new features to your users while maintaining a beautiful and professional user experience.* 