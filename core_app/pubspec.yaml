name: core_app
description: Every dentist's companion.

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.15.0+19

environment:
  sdk: ">=3.1.1 <4.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  get: ^4.6.6
  get_storage: ^2.1.1
  dio: ^5.8.0+1
  qlevar_router: ^1.11.2
  slang: ^4.8.1
  slang_flutter: ^4.8.0
  json_annotation: ^4.9.0
  # Utils
  path: ^1.9.0
  jiffy: ^6.3.1
  jwt_decode: ^0.3.1
  universal_platform: ^1.1.0
  # UI
  iconsax_flutter: ^1.0.0
  flutter_spinkit: ^5.2.1
  flex_color_picker: ^3.5.1
  syncfusion_flutter_charts: ^29.1.38
  syncfusion_flutter_calendar: ^29.1.38
  syncfusion_flutter_pdfviewer: ^29.1.38
  pdf:
  syncfusion_localizations: ^29.1.38
  # Geo Location
  latlong2: ^0.9.1
  geolocator: ^13.0.1
  flutter_map: ^7.0.2
  # Native
  printing: ^5.13.2
  file_picker: ^8.0.7
  image_picker: ^1.0.7
  url_launcher: ^6.3.1
  package_info_plus: ^8.0.2
  # Firebase
  firebase_core: ^3.13.1
  firebase_messaging: ^15.2.6
  firebase_remote_config: ^5.4.4
  firebase_analytics: ^11.4.6
  firebase_crashlytics: ^4.3.6

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  fl_chart: 0.69.2

dependency_overrides:
  http: ^1.2.2
  image: ^4.2.0
  web: ^1.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  icons_launcher: ^3.0.1
  build_runner: ^2.4.6
  json_serializable: ^6.7.1
  slang_build_runner: ^4.8.0

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^2.0.0
  msix: ^3.16.9

icons_launcher:
  image_path: assets/naab_logo.png
  platforms:
    android:
      enable: true
    ios:
      enable: true
      image_path: assets/naab_logo.jpg
    windows:
      enable: true
    macos:
      enable: true
    web:
      enable: true

msix_config:
  display_name: Dentastic
  publisher_display_name: PayRows
  identity_name: com.payrows.dentastic
  msix_version: 1.1.0.0
  logo_path: assets/logo.jpg
  capabilities: internetClient, location
  publisher: CN=CA878DFE-11DA-4C80-8CFB-241C04F55A45

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/
    - assets/svgs/
    - assets/teeth/
    - assets/icons/
    - assets/translations/
    - assets/features_promo/
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Inter
      fonts:
        - asset: assets\inter_font\Inter-Black.ttf
        - asset: assets\inter_font\Inter-Bold.ttf
        - asset: assets\inter_font\Inter-ExtraBold.ttf
        - asset: assets\inter_font\Inter-ExtraLight.ttf
        - asset: assets\inter_font\Inter-Light.ttf
        - asset: assets\inter_font\Inter-Medium.ttf
        - asset: assets\inter_font\Inter-Regular.ttf
        - asset: assets\inter_font\Inter-SemiBold.ttf
        - asset: assets\inter_font\Inter-Thin.ttf
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
