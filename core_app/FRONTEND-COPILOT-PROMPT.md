## Frontend Copilot Prompt: Link Procedures to Inventory with Auto-Consumption and Overrides

You are implementing a feature that links procedure templates to inventory items. When a user creates a visit procedure, inventory transactions should be auto-created for linked items or for user-provided overrides. Build an intuitive UI that lets users see defaults and adjust consumption before saving.

### Key Behaviors

- When creating a visit procedure, if the request includes `inventoryConsumptions`, use them. Otherwise, load default consumptions from the selected procedure template's inventory links.
- Only positive quantities are allowed. Reject 0 or negative values on the client.
- On submit, send `inventoryConsumptions` with `{ itemId, quantity }` entries only for quantities > 0.
- If the template is invalid, the API returns 404.
- If inventory would go negative, the API returns 400 with message: "Insufficient inventory for one or more items".

### Endpoints (backend implemented)

- GET `/procedure-templates/:ptid/inventory-links`

  - Response: `{ links: Array<{ id, clinicId, procedureTemplateId, inventoryItemId, quantity, item: { id, name, unit, quantity, warningQuantity } }> }`

- POST `/procedure-templates/:ptid/inventory-links`

  - Body: `{ itemId: string, quantity: number }`
  - Response: `{ link: { ... } }`

- PATCH `/procedure-templates/:ptid/inventory-links/:linkId`

  - Body: `{ quantity?: number }`
  - Response: `{ link: { ... } }`

- DELETE `/procedure-templates/:ptid/inventory-links/:linkId`

  - Response: `204`

- POST `/visits/:vid/procedures`
  - Body fields:
    - `dentistId: string`
    - `toothNumber: string`
    - `speciality: string`
    - `procedure: string`
    - `nextVisit: string`
    - `procedureTemplatId: string` // note the legacy spelling key is required
    - `finalPrice: number`
    - `toothRemoved: boolean`
    - `notes: string`
    - `inventoryConsumptions?: Array<{ itemId: string, quantity: number }>`
  - Behavior: if `inventoryConsumptions` omitted AND `procedureTemplatId` is valid, defaults will be used based on template links.

### UI Flow

1. In the "Create Visit Procedure" dialog/screen:

   - Inputs: `dentist`, `toothNumber`, `speciality`, `procedure` (text), `procedureTemplate` (select), `finalPrice`, `notes`, `nextVisit`.
   - When a `procedureTemplate` is selected (has id `ptid`):
     - Fetch `GET /procedure-templates/:ptid/inventory-links`.
     - Render a list/table of linked inventory items with default `quantity`.
     - Allow the user to adjust each `quantity` (positive integers only). Optionally allow removing an item by setting quantity to 0, but DO NOT send 0 to the API; omit the entry instead.

2. On submit:

   - Build `inventoryConsumptions` from edited rows: include `{ itemId, quantity }` for quantity > 0 only.
   - POST `/visits/:vid/procedures` with the body including `procedureTemplatId` and the `inventoryConsumptions` array.
   - If API returns 400 for insufficient inventory, show a clear error and keep the dialog open.

3. Optional management UI on the template screen (admin-only or as permitted):
   - Display current links list via GET.
   - Add new link (POST) with `item` selector and `quantity` input.
   - Edit link quantity (PATCH).
   - Remove link (DELETE).

### Types and Request Shapes (TypeScript)

```ts
type InventoryLink = {
  id: string;
  clinicId: string;
  procedureTemplateId: string;
  inventoryItemId: string;
  quantity: number;
  item: {
    id: string;
    name: string;
    unit: string;
    quantity: number; // current stock
    warningQuantity: number;
  };
};

type CreateVisitProcedureRequest = {
  dentistId: string;
  toothNumber: string;
  speciality: string;
  procedure: string;
  nextVisit: string;
  procedureTemplatId: string; // legacy spelling required by backend
  finalPrice: number;
  toothRemoved: boolean;
  notes: string;
  inventoryConsumptions?: Array<{ itemId: string; quantity: number }>;
};
```

### Example Calls

```ts
// Load default consumptions for a template
const linksRes = await api.get<{ links: InventoryLink[] }>(
  `/procedure-templates/${ptid}/inventory-links`
);
const defaultConsumptions = linksRes.data.links.map((l) => ({
  itemId: l.inventoryItemId,
  quantity: l.quantity,
}));

// Submit visit procedure with overrides (if user edited), else defaults
const body: CreateVisitProcedureRequest = {
  dentistId,
  toothNumber,
  speciality,
  procedure,
  nextVisit,
  procedureTemplatId: ptid,
  finalPrice,
  toothRemoved,
  notes,
  inventoryConsumptions: editedConsumptions, // only entries with quantity > 0
};
await api.post(`/visits/${visitId}/procedures`, body);
```

### Validation & UX

- Disable submit if any edited quantity is empty, zero, or negative.
- Show current stock in the list so users understand availability.
- If 400 insufficient inventory occurs, highlight the affected items if possible, otherwise show a form-level error.
- Do not send entries with zero quantity.

### Permissions & Auth

- All endpoints require the existing authenticated clinic user token.

### Notes

- The backend records an explanatory note in each auto-generated inventory transaction; the frontend does not need to pass a notes field for inventory consumption.
- Keep the legacy key name `procedureTemplatId` when sending the template id in the visit procedure creation call.
